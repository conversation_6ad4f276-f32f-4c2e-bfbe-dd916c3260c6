<template>
  <div>
    <Modal
      ref="comProcessModalRef"
      v-model="comProcessModal"
      :title="modalTitle"
      :isForm="isForm"
      width="85%"
      :footer-hide="!isForm"
    >
      <div slot="close" @click="closeBtn">
        <Icon type="ios-close" />
      </div>
      <div v-if="isForm">
        <div class="com-sub-title">基本信息</div>
        <Form
          ref="formRef1"
          :model="formData"
          :readonly="!isForm"
          :rules="rules1"
          :label-width="150"
          style="dispaly: flex"
        >
          <Row>
            <Col span="8">
              <FormItem label="集体教育时间段 :" prop="startTimes">
                <DatePicker
                  v-model="formData.startTimes"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :picker-options="pickerOptions"
                  style="width: 100%"
                  @on-change="changeTime"
                />
              </FormItem>
            </Col>
            <Col span="16">
              <FormItem label="集体教育范围 :" prop="talkEduScopes">
                <el-select-tree
                  :options="treeData"
                  :multiple="multiple"
                  :valueMultiple="valueMultiple"
                  @getValue="getSelectedValue"
                  style="width: 100%"
                ></el-select-tree>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="8"
              ><FormItem label="授课人姓名 :" prop="instructorName">
                <Input
                  placeholder="请填写"
                  v-model="formData.instructorName"
                ></Input> </FormItem
            ></Col>
            <Col span="8"
              ><FormItem label="授课人职务 :">
                <Input
                  v-model="formData.instructorPosition"
                  placeholder="请填写"
                ></Input> </FormItem
            ></Col>
            <Col span="8">
              <FormItem label="接受教育人数 :">
                <Input
                  type="number"
                  placeholder="请填写"
                  v-model="formData.numberOfRecipients"
                ></Input> </FormItem
            ></Col>
          </Row>
        </Form>
        <div class="com-sub-title mt-15">业务登记</div>
        <Form
          ref="formRef2"
          :model="formData"
          :rules="rules2"
          :readonly="!isForm"
          :label-width="150"
        >
          <FormItem label="课题" prop="talkEduTopic">
            <Input
              v-model="formData.talkEduTopic"
              show-word-limit
              clearable
              style="width: 63%"
            ></Input>
          </FormItem>
          <FormItem
            label="教育方式"
            :col="1"
            prop="educateMode"
            v-if="prisonType == 2"
          >
            <template>
              <Select v-model="formData.educateMode" :disabled="!isForm">
                <Option
                  v-for="(
                    item, attr
                  ) in ices_collective_talk_record_educate_mode"
                  :key="attr"
                  :value="attr"
                  >{{ item }}</Option
                >
              </Select>
            </template>
          </FormItem>
          <FormItem label="授课内容摘要" prop="summaryOfContent">
            <Button
              class="temp-cls"
              size="small"
              type="primary"
              @click="selectTemp()"
              v-if="optType !== 'view'"
              >模板</Button
            >
            <Input
              v-model="formData.summaryOfContent"
              type="textarea"
              placeholder="请填写"
              :autosize="{ minRows: 5, maxRows: 7 }"
            ></Input>
          </FormItem>
          <FormItem :label="`${personlabel}人员反应`" prop="detaineesResponse">
            <Input
              v-model="formData.detaineesResponse"
              placeholder="请填写"
              type="textarea"
              :autosize="{ minRows: 5, maxRows: 7 }"
            ></Input>
          </FormItem>
          <template>
            <Row>
              <Col span="8">
                <FormItem label="经办人">
                  <Input
                    type="text"
                    placeholder="请输入"
                    width="100%"
                    v-model="formData.jbr"
                    disabled
                  />
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="经办时间">
                  <el-date-picker
                    v-model="formData.jbsj"
                    type="datetime"
                    placeholder="选择日期时间"
                    style="width: 100%"
                    disabled
                  >
                  </el-date-picker>
                </FormItem>
              </Col>
            </Row>
          </template>
        </Form>
      </div>
      <!-- 只读模式 -->
      <div v-else class="readonlyCont">
        <div class="com-sub-title">基本信息</div>
        <Form
          ref="formRef1"
          :model="formData"
          :readonly="!isForm"
          :rules="rules1"
          :label-width="180"
          style="dispaly: flex"
        >
          <Row>
            <Col span="8">
              <FormItem label="集体教育时间段 :" prop="startTimes">
                <span class="view-text"
                  >{{ formData.talkEduStartTime }} -
                  {{ formData.talkEduEndTime }}</span
                >
              </FormItem>
            </Col>
            <Col span="16">
              <FormItem label="集体教育范围 :" prop="talkEduScope">
                <span class="view-text">{{
                  formData.talkEduScope ? formData.talkEduScope : "-"
                }}</span>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="8"
              ><FormItem label="授课人姓名 :" prop="instructorName">
                <span class="view-text">{{
                  formData.instructorName ? formData.instructorName : "-"
                }}</span>
              </FormItem></Col
            >
            <Col span="8"
              ><FormItem label="授课人职务 :">
                <span class="view-text">{{
                  formData.instructorPosition
                    ? formData.instructorPosition
                    : "-"
                }}</span>
              </FormItem></Col
            >
            <Col span="8">
              <FormItem label="接受教育人数 :">
                <span class="view-text">{{
                  formData.numberOfRecipients
                    ? formData.numberOfRecipients
                    : "-"
                }}</span>
              </FormItem></Col
            >
          </Row>
        </Form>
        <div class="com-sub-title mt-15">业务登记</div>
        <Form
          ref="formRef2"
          :model="formData"
          :rules="rules2"
          :readonly="!isForm"
          :label-width="150"
        >
          <FormItem label="课题" prop="talkEduTopic">
            <span class="view-text">{{
              formData.talkEduTopic ? formData.talkEduTopic : "-"
            }}</span>
          </FormItem>
          <FormItem
            label="教育方式"
            :col="1"
            prop="educateMode"
            v-if="prisonType == 2"
          >
            <span class="view-text">{{
              ices_collective_talk_record_educate_mode[formData.educateMode]
            }}</span>
          </FormItem>
          <FormItem label="授课内容摘要" prop="summaryOfContent">
            <Button
              class="temp-cls"
              size="small"
              type="primary"
              @click="selectTemp()"
              v-if="optType !== 'view'"
              >模板</Button
            >
            <span class="view-text">{{
              formData.summaryOfContent ? formData.summaryOfContent : "-"
            }}</span>
          </FormItem>
          <FormItem :label="`${personlabel}人员反应`" prop="detaineesResponse">
            <span class="view-text">{{
              formData.detaineesResponse ? formData.detaineesResponse : "-"
            }}</span>
          </FormItem>
          <div class="view-text">
            <Row>
              <Col span="8">
                <FormItem label="经办人">
                  <span>{{
                    formData.createUserName
                      ? formData.createUserName
                      : formData.operatorXm
                  }}</span>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="经办时间">
                  <span>{{
                    formData.createTime
                      ? formData.createTime
                      : formData.operatorTime
                  }}</span>
                </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
      <component
        v-bind:is="componentTab"
        ref="tempModalRef"
        @cancelTable="handleCancel"
        @updateStr="handleUpdate"
      ></component>
      <div slot="footer" v-if="isForm">
        <Button @click="closeFun" class="save">取 消</Button>
        <Button type="primary" @click="submitFun" class="save">提 交</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
// 监所类型prisonType 1=看守所 2=拘留所 3=戒毒所 4=支队
import { validateNecessary } from "@/util";
import tempModal from "./tempModal.vue";
import { mapActions } from "vuex";
import { reactive } from "vue";
import TreeSelectOption from "../../components/bl-form/TreeSelectOption.vue";
import elSelectTree from "../../components/bl-form/el-select-tree.vue";
export default {
  name: "talkEduGroupDetail",
  components: {
    tempModal,
    TreeSelectOption,
    elSelectTree,
  },
  props: {
    title: {
      type: String,
      default: "",
    },
    optType: {
      type: String,
      default: "",
    },
    forDatas: {
      type: Object,
      default: {},
    },
  },
  computed: {
    isForm() {
      this.operateType = this.optType;
      return this.optType !== "view";
    },
    ices_collective_talk_record_educate_mode() {
      return this.$store.state.code.localDict
        .ices_collective_talk_record_educate_mode;
    },
    modalTitle() {
      let name = {
        add: "新增",
        edit: "修改",
        view: "查看",
      };
      return `${this.title}${name[this.optType]}`;
    },
    // processedFwList() {
    //   const processData = (data) => {
    //     return data.map((item) => {
    //       const processedItem = {
    //         value: item.areaCode,
    //         label: item.areaName,
    //         children: item.children ? processData(item.children) : [],
    //       };
    //       return processedItem;
    //     });
    //   };

    //   return processData(this.fwList);
    // },
    processedFwList() {
      const processData = (data) => {
        return data.map((item) => ({
          value: item.areaCode,
          label: item.areaName,
          level: item.level,
          children: item.children ? processData(item.children) : [],
        }));
      };
      return processData(this.fwList);
    },
  },
  data() {
    return {
      multiple: true,
      value: {},
      valueMultiple: [],
      fwList: [
        {
          id: "01",
          name: "11111111",
          level: 1,
          chileren: [
            {
              id: "02",
              name: "222222",
              level: 2,
              chileren: [
                {
                  id: "02",
                  name: "222222",
                  level: 3,
                },
              ],
            },
          ],
        },
        {
          id: "02",
          name: "2222222",
          level: 1,
        },
        {
          id: "03",
          name: "3333333",
          level: 1,
          chileren: [
            {
              id: "030",
              name: "3333331",
              level: 2,
            },
          ],
        },
      ],
      treeData: [],
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      operateType: "",
      prisonType: "1",
      personlabel: "",
      formData: {
        startTimes: [],
      },
      rules1: {
        // startTimes: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) => {
        //       if (!value || value.length !== 2) {
        //         callback(new Error("请选择教育时间段"));
        //       } else {
        //         callback();
        //       }
        //     },
        //     trigger: "change,blur",
        //   },
        // ],
        startTimes: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value || value.length !== 2) {
                callback(new Error("请选择教育时间段"));
              } else if (new Date(value[0]) >= new Date(value[1])) {
                callback(new Error("结束时间必须晚于开始时间"));
              } else {
                callback();
              }
            },
            trigger: "change,blur",
          },
        ],
        instructorName: [
          {
            required: true,
            message: "请输入授课人姓名",
            trigger: "blur",
          },
        ],
        instructorPosition: [
          {
            required: true,
            message: "请选择授课人职务",
            trigger: "change,blur",
          },
        ],
      },
      rules2: {
        talkEduTopic: [
          { required: true, message: "请输入课题", trigger: "blur" },
        ],
        summaryOfContent: [
          {
            required: true,
            message: "请输入授课内容摘要",
            trigger: "blur",
          },
        ],
        detaineesResponse: [
          { required: true, message: "请输入人员反应", trigger: "change" },
        ],
        educateMode: [
          { required: true, message: "请选择教育方式", trigger: "change,blur" },
        ],
      },
      comProcessModal: true,
      componentTab: null,
    };
  },
  created() {
    let userInfo = this.$store.state;
    if (userInfo) {
      // this.prisonType = userInfo.prisonType;
      // this.prisonType = '1';
    }
    let obj = { 1: "在押", 2: "被拘留" };
    if (this.prisonType == "1") {
      this.personlabel = obj[this.prisonType];
    }
  },
  methods: {
    ...mapActions([
      "authGetRequest",
      "getRequest",
      "postRequest",
      "authPostRequest",
    ]),
    changeTime(value) {
      if (!value || value.length !== 2) {
        this.formData.talkEduStartTime = "";
        this.formData.talkEduEndTime = "";
        this.$nextTick(() => {
          this.$refs.formRef1.validateField("startTimes");
        });
        return;
      }

      let start = value[0];
      let end = value[1];

      if (start.endsWith("00:00:00")) {
        start = start.split(" ")[0] + " 00:00:00";
      }
      if (end.endsWith("00:00:00")) {
        end = end.split(" ")[0] + " 23:59:59";
      }

      this.formData.startTimes = [start, end];
      this.formData.talkEduStartTime = start;
      this.formData.talkEduEndTime = end;

      this.$nextTick(() => {
        this.$refs.formRef1.validateField("startTimes");
      });
    },
    handleFun(optType, obj = {}) {
      // debugger
      this.formData = obj;
      this.optType = optType;
      if (this.optType == "add") {
      }
    },
    closeFun() {
      this.$emit("cancel");
    },
    // 模板选择
    selectTemp() {
      this.componentTab = "tempModal";
    },
    async submitFun() {
      if (!this.formData.talkEduEndTime || !this.formData.talkEduStartTime) {
        this.$Message.error("请填写完整表单");
        return;
      }
      let validate1 = await this.$refs.formRef1.validate((valid) => {
        if (valid) {
          return valid;
        } else {
          return false;
        }
      });
      let validate2 = await this.$refs.formRef2.validate((valid) => {
        if (valid) {
          return valid;
        } else {
          return false;
        }
      });
      if (!validate1 || !validate2) {
        this.$Message.error("请填写完整表单");
      } else {
        let params = this.formData;
        delete params.talkEduScopes;
        delete params.jbr;
        delete params.jbsj;
        delete params.startTimes;
        this.authPostRequest({
          url: this.$path.bsp_test_tem_jtjy_add,
          params: params,
        }).then((res) => {
          if (res.success) {
            this.$Message.success("操作成功");
            this.$emit("cancel");
            this.$emit("refresh");
          }
        });
      }
    },
    handleCancel() {
      this.componentTab = null;
    },
    closeBtn() {
      this.$emit("cancel");
    },
    processTreeData(data) {
      if (data && data.length) {
        return data.map((item) => {
          const node = {
            name: item.areaName,
            id: item.areaCode,
            level: item.level,
            children: item.children ? this.processTreeData(item.children) : [],
          };
          return node;
        });
      }
    },
    // 处理节点选中
    handleCheck(node, checked) {
      const value = node.value;
      const index = this.formData.talkEduScopes.indexOf(value);

      if (checked && index === -1) {
        this.formData.talkEduScopes.push(value);
      } else if (!checked && index !== -1) {
        this.formData.talkEduScopes.splice(index, 1);
      }
    },
    getSelectedValue(value) {
      let arr = [];
      value.forEach((item) => {
        arr.push(item.id);
        this.formData.talkEduScope = arr.join(",");
      });
    },
    handleUpdate(str) {
      this.formData.summaryOfContent = str;
    },
    buildIdNameMap(treeData, map = {}) {
      treeData.forEach((node) => {
        if (node.id == i) {
        } else if (node.children && node.children.length > 0) {
        }
      });
    },
  },
  mounted() {
    if (this.optType !== "add") {
      // 编辑
      let userId = this.$store.state.common.orgCode;
      this.authGetRequest({
        url: this.$path.bsp_test_tem_jtjy_update,
        params: {
          orgCode: userId,
          areaType: "DETENTION_ROOM",
        },
      }).then((resp) => {
        this.fwList = resp.data;
        this.treeData = this.processTreeData(this.fwList);

        this.formData = this.forDatas;

        if (this.formData.talkEduScope) {
          let arr = this.formData.talkEduScope
            .split(",")
            .map((item) => item.trim());

          // 递归查找函数
          const findNodeNameById = (nodes, id) => {
            for (const node of nodes) {
              if (node.id === id) {
                return node.name;
              }
              if (node.children && node.children.length > 0) {
                const foundName = findNodeNameById(node.children, id);
                if (foundName) return foundName;
              }
            }
            return null;
          };

          // 查找所有匹配的名称
          const names = arr.map((id) => {
            return findNodeNameById(this.treeData, id) || id; // 如果找不到就返回原始ID
          });

          // 更新formData
          this.formData.talkEduScope = names.join(", ");
        }
      });
    } else {
      this.formData.jbr = this.$store.state.common.userName;
      this.formData.jbsj = new Date();
      this.formData.instructorName = this.formData.jbr;
      let userId = this.$store.state.common.orgCode;
      this.authPostRequest({
        url: this.$path.bsp_test_tem_jtjy_update,
        params: {
          orgCode: userId,
          areaType: "DETENTION_ROOM",
        },
      }).then((resp) => {
        this.fwList = resp.data;
        this.treeData = this.processTreeData(this.fwList);
      });
    }
  },
};
</script>

<style scoped lang="less">
.temp-cls {
  position: absolute;
  left: -70px;
  top: 32px;
}
/deep/.ivu-modal-header-inner {
  font-size: 18px !important;
  font-weight: 600;
}
.com-sub-title {
  border-left: 4px solid #3491fa;
  padding-left: 8px;
  font-size: 16px;
  font-weight: bold;
  height: 20px;
  line-height: 20px;
  position: relative;
  margin-bottom: 16px;

  img {
    vertical-align: bottom;
  }

  .extra-btn {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }

  &.layout {
    margin: 16px 16px 0;
  }

  .tip {
    display: inline-block;
    margin-left: 30px;
    font-size: 16px;
    font-weight: normal;
    color: #616f6f;
  }
}
/deep/.ivu-input {
  height: 40px !important;
}
/deep/.ivu-select-single .ivu-select-selection {
  height: 40px !important;
}
/deep/.ivu-modal-body {
  padding-right: 35px !important;
  //   padding-bottom: 80px!important;
}

.view-mode {
  background: palegreen;
  /* 只读模式下的全局样式 */
  .ivu-form-item {
    margin-bottom: 12px;

    &-label {
      color: #666;
      font-weight: normal;
    }
  }

  .view-text {
    display: inline-block;
    padding: 8px 0;
    // color: #17233d;
    font-size: 14px;
    line-height: 1.5;
    min-height: 40px;
    color: pink;
  }

  .com-sub-title {
    margin-top: 20px;
    margin-bottom: 12px;
    color: #333;
  }

  /* 调整行间距 */
  .ivu-row {
    margin-bottom: 8px;
  }
}

.readonlyCont /deep/.ivu-form-item-label {
  height: 100%;
  min-height: 46px;
  background: #e4eefc;
  justify-content: flex-start;
  text-align: left;
  padding: 10px 10px 10px 21px;
  border-right: 2px solid #fff;
  border-left: 2px solid #fff;
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  color: #616f6f;
  white-space: pre-wrap;
}
.readonlyCont/deep/.ivu-form-item-content {
  padding: 9px 6px;
  background: #d8dce6;
  height: 46px !important;
}
.readonlyCont {
  padding-bottom: 100px;
}
.tree-select-dropdown {
  padding: 5px;
  max-height: 300px;
  overflow: auto;
}
</style>
