<template>
  <div :class="!inDialog? 'detail-content' : ''" style="">
    <div :class="!inDialog?  'fm-content-info bsp-base-content record-detail-content' : ''">
      <Form ref="formData" :model="formData"  inline >
        <div class="fm-content-box">
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />基本信息</p>
          <Row>
            <Col span="3" class="col-title"><span></span></Col>
            <Col span="5">
              <img v-if="formData.photoUrl" :src="formData.photoUrl" class="personImg-box" alt="">
              <img v-else src="@/assets/images/detentionEnter/person.png" class="personImg-box" alt="">
            </Col>
            <Col span="3" class="col-title"><span></span></Col>
            <Col span="5">
              <img v-if="formData.photoUrl" :src="formData.photoUrl" class="personImg-box" alt="">
              <img v-else src="@/assets/images/detentionEnter/person.png" class="personImg-box" alt="">
            </Col>
            <Col span="3" class="col-title"><span></span></Col>
            <Col span="5">
              <img v-if="formData.photoUrl" :src="formData.photoUrl" class="personImg-box" alt="">
              <img v-else src="@/assets/images/detentionEnter/person.png" class="personImg-box" alt="">
            </Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>证件号码</span></Col>
            <Col span="5"><span>{{formData.zjhm}}</span></Col>
            <Col span="3" class="col-title"><span>证件类型</span></Col>
            <Col span="5"><span>{{formData.zjlxName}}</span></Col>
            <Col span="3" class="col-title"><span>姓名</span></Col>
            <Col span="5"><span>{{formData.xm}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>曾用名/别名/绰号</span></Col>
            <Col span="5"><span>{{formData.bm}}</span></Col>
            <Col span="3" class="col-title"><span>人员编号</span></Col>
            <Col span="5"><span>{{formData.rybh}}</span></Col>
            <Col span="3" class="col-title"><span>性别</span></Col>
            <Col span="5"><span>{{formData.xbName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>是否为在校学生</span></Col>
            <Col span="5"><span>{{formData.sfwxzxs == '0' ? '否' : '是'}}</span></Col>
            <Col span="3" class="col-title"><span>学校名称</span></Col>
            <Col span="5"><span>{{formData.xxmcName}}</span></Col>
            <Col span="3" class="col-title"><span>人员管理类别</span></Col>
            <Col span="5"><span>{{formData.gllbName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>出生日期</span></Col>
            <Col span="5"><span>{{formData.csrq}}</span></Col>
            <Col span="3" class="col-title"><span>文化程度</span></Col>
            <Col span="5"><span>{{formData.whcdName}}</span></Col>
            <Col span="3" class="col-title"><span>民族</span></Col>
            <Col span="5"><span>{{formData.mzName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>婚姻状况</span></Col>
            <Col span="5"><span>{{formData.hyzkName}}</span></Col>
            <Col span="3" class="col-title"><span>职业</span></Col>
            <Col span="5"><span>{{formData.zyName}}</span></Col>
            <Col span="3" class="col-title"><span>国籍</span></Col>
            <Col span="5"><span>{{formData.gjName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>政治面貌</span></Col>
            <Col span="5"><span>{{formData.zzmmName}}</span></Col>
            <Col span="3" class="col-title"><span>籍贯</span></Col>
            <Col span="5"><span>{{formData.jgName}}</span></Col>
            <Col span="3" class="col-title"><span>职务</span></Col>
            <Col span="5"><span>{{formData.zw}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>专长</span></Col>
            <Col span="5"><span>{{formData.tcName}}</span></Col>
            <Col span="3" class="col-title"><span>宗教信仰</span></Col>
            <Col span="5"><span>{{formData.zjxyName}}</span></Col>
            <Col span="3" class="col-title"><span>身份</span></Col>
            <Col span="5"><span>{{formData.sfName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>户籍所在地</span></Col>
            <Col span="5"><span>{{formData.hjdName}}</span></Col>
            <Col span="3" class="col-title"><span>户籍所在地详址</span></Col>
            <Col span="13"><span>{{formData.hjdxz}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>现居住地</span></Col>
            <Col span="5"><span>{{formData.xzzName}}</span></Col>
            <Col span="3" class="col-title"><span>现居住地详址</span></Col>
            <Col span="13"><span>{{formData.xzzxz}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>特殊身份</span></Col>
            <Col span="5"><span>{{formData.tssfName}}</span></Col>
            <Col span="3" class="col-title"><span>工作单位</span></Col>
            <Col span="5"><span>{{formData.gzdw}}</span></Col>
          </Row>
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />案件信息</p>
          <!-- <Row>
            <Col span="3" class="col-title"><span>案件类别</span></Col>
            <Col span="5"><span>{{formData.ajlbdmName}}</span></Col>
            <Col span="3" class="col-title"><span>限制会见案件</span></Col>
            <Col span="5"><span>{{formData.xzhjaj == '0' ? '否' : '是'}}</span></Col>
            <Col span="3" class="col-title"><span>是否限制会见</span></Col>
            <Col span="5"><span>{{formData.xzhj == '0' ? '否' : '是'}}</span></Col>
          </Row> -->
          <Row>
            <Col span="3" class="col-title"><span>同案编号</span></Col>
            <Col span="5"><span>{{formData.tabh}}</span></Col>
            <Col span="3" class="col-title"><span>收治凭证类型</span></Col>
            <Col span="5"><span>{{formData.sypzName}}</span></Col>
            <Col span="3" class="col-title"><span>收治凭证文书</span></Col>
            <Col span="5">
              <file-upload
                :defaultList="sypzwsdzUrl"
                :serviceMark="serviceMark"
                :bucketName="bucketName"
                :beforeUpload="beforeUpload"
                v-if="showFile"
                :isDetail="true"
                @fileSuccess="fileSuccessFile"
                @fileRemove="fileRemoveFile"
                @fileComplete="fileCompleteFile" />
            </Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>回执法律文书号</span></Col>
            <Col span="5"><span>{{formData.hzwsh}}</span></Col>
            <Col span="3" class="col-title"><span>入所原因</span></Col>
            <Col span="5"><span>{{formData.rsyyName}}</span></Col>
            <!-- <Col span="3" class="col-title"><span>入所时所处诉讼阶段</span></Col>
            <Col span="5"><span>{{formData.sshjName}}</span></Col> -->
            <Col span="3" class="col-title"><span>送治单位类型</span></Col>
            <Col span="5"><span>{{formData.syjglxName}}</span></Col>
          </Row>
          <Row>
            <!-- <Col span="3" class="col-title"><span>强制措施类型</span></Col>
            <Col span="5"><span>{{formData.qzcslxName}}</span></Col> -->
            <!-- <Col span="3" class="col-title"><span>送押单位类型</span></Col>
            <Col span="5"><span>{{formData.syjglxName}}</span></Col> -->
            <Col span="3" class="col-title"><span>送治单位名称</span></Col>
            <Col span="5"><span>{{formData.syjgmcName}}</span></Col>
            <Col span="3" class="col-title"><span>送治人</span></Col>
            <Col span="5"><span>{{formData.syr1}}</span></Col>
            <Col span="3" class="col-title"><span>送治人固话</span></Col>
            <Col span="5"><span>{{formData.syrgh1}}</span></Col>
          </Row>
          <Row>
            
            <Col span="3" class="col-title"><span>送治人手机</span></Col>
            <Col span="5"><span>{{formData.syrsj1}}</span></Col>
             <Col span="3" class="col-title"><span>案事件编号</span></Col>
            <Col span="5"><span>{{formData.ajbh}}</span></Col>
            <Col span="3" class="col-title"><span>办案单位</span></Col>
            <Col span="5"><span>{{formData.badwName}}</span></Col>
          </Row>
          <Row>
           
            <Col span="3" class="col-title"><span>办案单位类型</span></Col>
            <Col span="5"><span>{{formData.badwlxName}}</span></Col>
            <Col span="3" class="col-title"><span>送治期限</span></Col>
            <Col span="5"><span>{{formData.gyqx}}</span></Col>
             <Col span="3" class="col-title"><span>法律文书号</span></Col>
            <Col span="5"><span>{{formData.flwsh}}</span></Col>
          </Row>
          <!-- <Row>
            <Col span="3" class="col-title"><span>办案环节</span></Col>
            <Col span="5"><span>{{formData.bahjName}}</span></Col>
            <Col span="3" class="col-title"><span>羁押日期</span></Col>
            <Col span="5"><span>{{formData.jyrq}}</span></Col>
            <Col span="3" class="col-title"><span>拘留日期</span></Col>
            <Col span="5"><span>{{formData.jlrq}}</span></Col>
          </Row> -->
          <Row>
            <!-- <Col span="3" class="col-title"><span>逮捕日期</span></Col>
            <Col span="5"><span>{{formData.dbrq}}</span></Col> -->
            
            <!-- <Col span="3" class="col-title"><span>入所日期</span></Col>
            <Col span="5"><span>{{formData.rssj}}</span></Col> -->
          </Row>
          <Row>
           
            <Col span="3" class="col-title"><span>简要案情</span></Col>
            <Col span="21"><span>{{formData.jyaq}}</span></Col>
          </Row>
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />办案人信息</p>
          <Row>
            <Col span="3" class="col-title"><span>办案人</span></Col>
            <Col span="5"><span>{{formData.bar}}</span></Col>
            <Col span="3" class="col-title"><span>办案人电话</span></Col>
            <Col span="5"><span>{{formData.barlxff}}</span></Col>
            <Col span="3" class="col-title"><span>办案人性别</span></Col>
            <Col span="5"><span>{{formData.barxbName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>办案人证件类型</span></Col>
            <Col span="5"><span>{{formData.barzjlxName}}</span></Col>
            <Col span="3" class="col-title"><span>办案人证件号码</span></Col>
            <Col span="5"><span>{{formData.barzjhm}}</span></Col>
          </Row>
          <!-- <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />原押所送押信息</p>
          <Row>
            <Col span="3" class="col-title"><span>送押单位(原押所)</span></Col>
            <Col span="5"><span>{{formData.yysSydw}}</span></Col>
            <Col span="3" class="col-title"><span>送押民警(原押所)</span></Col>
            <Col span="5"><span>{{formData.yysSymj}}</span></Col>
            <Col span="3" class="col-title"><span>送押民警联系电话(原押所)</span></Col>
            <Col span="5"><span>{{formData.yysSymjlxdh}}</span></Col>
          </Row> -->
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />收治信息</p>
          <Row>
            <Col span="3" class="col-title"><span>是否佩戴眼镜</span></Col>
            <Col span="5"><span>{{formData.sfpdyj == '0' ? '否' : '是'}}</span></Col>
            <Col span="3" class="col-title"><span>病区</span></Col>
            <Col span="5"><span>{{formData.areaName}}</span></Col>
            <Col span="3" class="col-title"><span>病室号</span></Col>
            <Col span="5"><span>{{formData.roomName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>分配床位</span></Col>
            <Col span="5"><span>{{formData.cwh}}</span></Col>
            <Col span="3" class="col-title"><span>分配识别服</span></Col>
            <Col span="5"><span>{{formData.sfpbsf}}</span></Col>
            <Col span="3" class="col-title"><span>识别服颜色</span></Col>
            <Col span="5"><span>{{formData.suitColorName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>带入病室人</span></Col>
            <Col span="5"><span>{{formData.drjsr}}</span></Col>
            <Col span="3" class="col-title"><span>带入病室时间</span></Col>
            <Col span="5"><span>{{formData.drjssj}}</span></Col>
            <Col span="3" class="col-title"><span>收治民警姓名</span></Col>
            <Col span="5"><span>{{formData.symjxm}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>备注</span></Col>
            <Col span="13"><span>{{formData.bz}}</span></Col>
          </Row>
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />涉密信息</p>
          <Row>
            <Col span="3" class="col-title"><span>是否涉密</span></Col>
            <Col span="5"><span>{{formData.sfsm == '0' ? '否' : '是'}}</span></Col>
            <Col span="3" class="col-title"><span>人员代号</span></Col>
            <Col span="5"><span>{{formData.rydh}}</span></Col>
            <Col span="3" class="col-title"><span>涉密原因</span></Col>
            <Col span="5"><span>{{formData.smyyName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>备注</span></Col>
            <Col span="5"><span>{{formData.smbz}}</span></Col>

          </Row>

          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />救济物品发放</p>
          <Row>
            <Col span="3" class="col-title"><span>救济日期</span></Col>
            <Col span="5"><span>{{formData.jjrq}}</span></Col>
            <Col span="3" class="col-title"><span>救济原因</span></Col>
            <Col span="5"><span>{{formData.jjyyName}}</span></Col>
            <Col span="3" class="col-title"><span>领取物品</span></Col>
            <Col span="5"><span>{{formData.jjlqwp}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>经办人</span></Col>
            <Col span="5"><span>{{formData.jbr}}</span></Col>
            <Col span="3" class="col-title"><span>经办时间</span></Col>
            <Col span="5"><span>{{formData.jbsj}}</span></Col>
          </Row>
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />社会关系</p>
          <div class="man-model-content">
            <div style="width: 100%;">
              <Table :columns="columns" :data="formData.socialRelations?formData.socialRelations:[]" border>

              </Table>
            </div>
          </div>
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />其他信息</p>
          <Row>
            <Col span="3" class="col-title"><span>涉毒尿检初查结果</span></Col>
            <Col span="5"><span>{{formData.sdnjccjg}}</span></Col>
            <Col span="3" class="col-title"><span>涉毒尿检单位</span></Col>
            <Col span="5"><span>{{formData.sdnjdw}}</span></Col>
            <Col span="3" class="col-title"><span>涉毒尿检初检时间</span></Col>
            <Col span="5"><span>{{formData.sdnjccsj}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>涉毒尿检检查人</span></Col>
            <Col span="5"><span>{{formData.sdnjjcr}}</span></Col>
            <Col span="3" class="col-title"><span>原押所表现</span></Col>
            <Col span="5">
              <file-upload
                :defaultList="sypzwsdzUrl"
                :serviceMark="serviceMark"
                :bucketName="bucketName"
                :beforeUpload="beforeUpload"
                v-if="showFile"
                :isDetail="true"
                @fileSuccess="fileSuccessFile"
                @fileRemove="fileRemoveFile"
                @fileComplete="fileCompleteFile" />
            </Col>
          </Row>
        </div>
      </Form>

    </div>
    <div class="bsp-base-fotter" v-if="!inDialog">
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
    </div>
  </div>
</template>

<script>
  import { sImageUploadLocal } from '@/components/upload/image'
  import { fileUpload } from 'sd-minio-upfile'
  import {mapActions} from "vuex";

  export default {
    components:{
      sImageUploadLocal,fileUpload
    },
    props:{
      rowData:{
        type: [Array,Object],
        default: {}
      },
      inDialog:{
        default: false
      },
    },
    data(){
      return{
        formData: {
        },
        showFile:false,
        sypzwsdzUrl:[],
        yysBxShowFile:false,
        yysBxUrl:[],
        serviceMark: serverConfig.OSS_SERVICE_MARK,
        bucketName: serverConfig.bucketName,

        columns:[
          {
            type: 'index',
            width: 80,
            align: 'center',
            title:'序号'
          },
          {
            title: '姓名',
            key: 'name',
            align: 'center',
          },
          {
            title: '性别',
            key: 'genderName',
            align: 'center',
          },
          {
            title: '关系',
            key: 'relationshipName',
            align: 'center',
          },
          {
            title: '证件号码',
            key: 'idNumber',
            align: 'center',
          },
          {
            title: '联系电话',
            key: 'contact',
            align: 'center',
          },
          {
            title: '住址',
            key: 'address',
            align: 'center',
          },
          {
            title: '工作单位',
            key: 'workUnit',
            align: 'center',
          },
          {
            title: '职业',
            key: 'occupation',
            align: 'center',
          },
        ],
        dataTable:[]
      }
    },
    methods:{
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      handleClose(){
        this.$emit('close',false)
      },
      beforeUpload(){},
      fileSuccessFile(){},
      fileRemoveFile(){},
      fileCompleteFile(data,index){
      },
      getDetail(rybh){
        this.$store.dispatch('authPostRequest',{
          url: this.$path.app_detainRegKssGetList,
          params: {
            rybh:rybh
          }
        }).then(resp => {
          if(resp.code == 0){
            // this.formData = resp.data
            if(resp.data && resp.data.length > 0){
              this.formData = resp.data[0]
            }
            if(this.formData.sypzwsdz){
              this.sypzwsdzUrl = JSON.parse(this.formData.sypzwsdz)
              this.showFile = true
            }
            if(this.formData.yysBx){
              this.yysBxUrl = JSON.parse(this.formData.yysBx)
              this.yysBxShowFile = true
            }
            this.getSocialRelationsList(this.rowData.rybh)
          }else{
            this.$Modal.error({
              title: '温馨提示',
              content: resp.msg || '操作失败'
            })
          }
        })
      },
      getSocialRelationsList(rybh){
        console.log(rybh,'rybh----')
        this.$store.dispatch('authPostRequest',{
          url: this.$path.app_getSocialRelationsList,
          params: {
            rybh:rybh
          }
        }).then(resp => {
          if(resp.code == 0){
            this.formData.socialRelations = resp.data
          }else{
            this.$Modal.error({
              title: '温馨提示',
              content: resp.msg || '操作失败'
            })
          }
        })
      },
    },
    mounted(){
      console.log(this.rowData,'rowData');
      this.getDetail(this.rowData.rybh)
    }
  }
</script>

<style scoped lang="less">
  @import "~@/assets/style/formInfo.css";
  div.socialRelations-content:not(:last-child){
    margin-bottom:10px;
  }
  .detail-content{
    height: 95%;
    overflow: hidden;
  }
  .bsp-imgminio-container{
    width:100% !important;
  }
  .personImg-box{
    width: 180px;
    height: 197px;
    margin-top:10px;
  }
  .man-model-content{
    display: flex;
    width: 100%;
    margin-top: 5px;
    margin-bottom: 5px;
    ::v-deep(.bsp-base-form .ivu-table th){
      background: unset !important;
    }
  }
  .record-detail-content{
    top:30px !important;;
  }
</style>
