<template>
  <div :class="!inDialog? 'detail-content' : ''"  style="">
    <div :class="!inDialog? 'fm-content-info bsp-base-content record-detail-content' : ''">
      <Form ref="formData" :model="formData"  inline >
        <div class="fm-content-box">
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />基本信息</p>
          <Row>
            <Col span="3" class="col-title"><span>病室号</span></Col>
            <Col span="5"><span>{{combineInfoData.roomName}}</span></Col>
            <Col span="3" class="col-title"><span>姓名</span></Col>
            <Col span="5"><span>{{combineInfoData.xm}}</span></Col>
            <Col span="3" class="col-title"><span>曾用名/别名/绰号</span></Col>
            <Col span="5"><span>{{combineInfoData.bm}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>性别</span></Col>
            <Col span="5"><span>{{combineInfoData.xbName}}</span></Col>
            <Col span="3" class="col-title"><span>出生日期</span></Col>
            <Col span="5"><span>{{combineInfoData.csrq}}</span></Col>
            <Col span="3" class="col-title"><span>涉嫌罪名</span></Col>
            <Col span="5"><span>{{combineInfoData.sxzm}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>收押登记时间</span></Col>
            <Col span="5"><span>{{rowData.add_time}}</span></Col>
            <Col span="0" class="col-title"><span></span></Col>
            <Col span="8"> <Button style="margin: 0 13px 0 16px" type="primary" @click.native="showBaseInfoDialog()" v-if="!inDialog">查看入所登记详情</Button></Col>
          </Row>
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />健康检查结论</p>
          <Row>
            <Col span="3" class="col-title"><span>医生意见</span></Col>
            <Col span="5"><span>{{combineInfoData.ysyjName}}</span></Col>
            <Col span="3" class="col-title"><span>检查人</span></Col>
            <Col span="5"><span>{{combineInfoData.jcr}}</span></Col>
            <Col span="3" class="col-title"><span>检查时间</span></Col>
            <Col span="5"><span>{{combineInfoData.jcsj}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>备注</span></Col>
            <Col span="21"><span>{{combineInfoData.bz}}</span></Col>
          </Row>
          <div class="bsp-base-subtit" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
            <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />领导审批结论</p>
            <div class="form">
              <approvalInfo :actInstId="this.rowData.act_inst_id"></approvalInfo>
            </div>
          </div>


<!--          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />领导审批结论</p>-->
<!--          <Row>-->
<!--            <Col span="3" class="col-title"><span>审批结果</span></Col>-->
<!--            <Col span="5"><span>{{formData.spztName}}</span></Col>-->
<!--            <Col span="3" class="col-title"><span>审批意见</span></Col>-->
<!--            <Col span="13"><span>{{formData.approvalResult}}</span></Col>-->
<!--          </Row>-->
        </div>
      </Form>
    </div>
    <div class="bsp-base-fotter" v-if="!inDialog">
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
    </div>

    <Modal
      v-model="openModal"
      :mask-closable="false"
      :closable="true"
      class-name="select-sy-modal"
      width="80%"
      title="入所登记详情"
    >
      <div style="height: 75vh;overflow: auto;">
        <baseInfoDetail :rowData="rowData" :inDialog="true"></baseInfoDetail>
      </div>
    </Modal>
  </div>
</template>

<script>
  import approvalInfo from '../registrationManage/approvalInfo'
   import baseInfoDetail from '../../treatmentEnterManage/recordManage/detail'
    export default {
      components:{
        baseInfoDetail,approvalInfo
      },
      props:{
        rowData:{
          type: [Array,Object],
          default: {}
        },
        inDialog:{
          default: false
        },
      },
      data(){
        return{
          formData: {
          },
          combineInfoData:{

          },
          openModal:false,
        }
      },
      methods:{
        handleClose(){
          this.$emit('close',false)
        },
        showBaseInfoDialog(){
          this.openModal = true;
        },
        getDetail(rybh){
          this.$store.dispatch('authPostRequest',{
            url: this.$path.app_detainRegKssGetList,
            params: {
              rybh:rybh
            }
          }).then(resp => {
            if(resp.code == 0){
              // this.formData = resp.data
              if(resp.data && resp.data.length > 0){
                this.formData = resp.data[0]
              }
            }else{
              this.$Modal.error({
                title: '温馨提示',
                content: resp.msg || '操作失败'
              })
            }
          })

          // this.$store.dispatch('authGetRequest',{
          //   url: this.$path.app_detainRegKssGet,
          //   params: {
          //     id:id
          //   }
          // }).then(resp => {
          //   if(resp.code == 0){
          //     this.formData = resp.data
          //
          //   }else{
          //     this.$Modal.error({
          //       title: '温馨提示',
          //       content: resp.msg || '操作失败'
          //     })
          //   }
          // })
        },
        getCombineInfoDetail(rybh){
          this.$store.dispatch('authGetRequest',{
            url: this.$path.app_getCombineInfo,
            params: {
              rybh:rybh
            }
          }).then(resp => {
            if(resp.code == 0){
              if(resp.data){
                this.combineInfoData = resp.data
              }
            }else{
              this.$Modal.error({
                title: '温馨提示',
                content: resp.msg || '操作失败'
              })
            }
          })
        },
      },
      mounted(){
        this.getCombineInfoDetail(this.rowData.rybh)
        this.getDetail(this.rowData.rybh)
      }
    }
</script>

<style scoped>
  @import "~@/assets/style/formInfo.css";
  .detail-content{
    height: 95%;
    overflow: hidden;
  }
  .record-detail-content{
    top:30px !important;;
  }
</style>
