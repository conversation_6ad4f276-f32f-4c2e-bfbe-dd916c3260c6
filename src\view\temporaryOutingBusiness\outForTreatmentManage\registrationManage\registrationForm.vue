<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <div class="fm-content-info bsp-base-content" style="top:30px !important;padding:unset !important;">
      <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="200" label-colon
        style="padding: 0 .625rem;">
        <div class="fm-content-box">
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />人员信息
          </p>
          <Row>
            <Col span="3" class="col-title"><span>姓名</span></Col>
            <Col span="5"><span>{{ rowData.jgryxm }}</span></Col>
            <Col span="3" class="col-title"><span>性别</span></Col>
            <Col span="5"><span>{{ rowData.xbName }}</span></Col>
            <Col span="3" class="col-title"><span>国籍</span></Col>
            <Col span="5"><span>{{ rowData.gjName }}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>出生日期</span></Col>
            <Col span="5"><span>{{ rowData.csrq }}</span></Col>
            <Col span="3" class="col-title"><span>监室号</span></Col>
            <Col span="5"><span>{{ rowData.room_name }}</span></Col>
          </Row>

        </div>
        <div class="bsp-base-subtit" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />就医信息
          </p>
          <div class="form">
            <Row>
              <Col span="8">
              <FormItem label="预约时间" prop="appointmentTime"
                :rules="[{ trigger: 'blur,change', message: '预约时间为必填', required: true }]">
                <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                  @change="$refs.formData.validateField('appointmentTime')" v-model="formData.appointmentTime"
                  size="small" placeholder="请选择" style="width: 100%;" />
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="出所就医原因" prop="csjyyy"
                :rules="[{ trigger: 'blur,change', message: '出所就医原因为必填', required: true }]">
                <s-dicgrid v-model="formData.csjyyy" @change="$refs.formData.validateField('csjyyy')" :isSearch="true"
                  :appMark="globalAppCode" dicName="ZD_CSJYYY" />
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="就诊医院" prop="hospital"
                :rules="[{ trigger: 'blur,change', message: '就诊医院为必填', required: true }]">
                <s-dicgrid v-model="formData.hospital" @change="$refs.formData.validateField('hospital')"
                  :isSearch="true" dicName="ZD_CSJYJZYY" />
              </FormItem>
              </Col>

            </Row>
            <Row>
              <Col span="8">
              <FormItem label="病情等级" prop="symptomLevel"
                :rules="[{ trigger: 'blur,change', message: '病情等级为必填', required: true }]">
                <s-dicgrid v-model="formData.symptomLevel" @change="$refs.formData.validateField('symptomLevel')"
                  :isSearch="true" dicName="ZD_CSJYBQDJ" />
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="精神情况" prop="jszk" :rules="[{ trigger: 'change', message: '精神情况为必填', required: true }]">
                <Input type="text" v-model="formData.jszk" placeholder="请填写" />
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="是否存在拒绝服药或抗拒治疗情况" prop="sfczjjfyhkjzlqk"
                :rules="[{ trigger: 'blur,change', message: '请选择是否存在拒绝服药或抗拒治疗情况', required: true }]">
                <RadioGroup v-model="formData.sfczjjfyhkjzlqk">
                  <Radio label="1">是</Radio>
                  <Radio label="2">否</Radio>
                </RadioGroup>
              </FormItem>
              </Col>

            </Row>
            <Row>
              <Col span="8">
              <FormItem label="是否存在吞食异物情况" prop="sfcztsywqk"
                :rules="[{ trigger: 'blur,change', message: '请选择是否存在吞食异物情况', required: true }]">
                <RadioGroup v-model="formData.sfcztsywqk">
                  <Radio label="1">是</Radio>
                  <Radio label="2">否</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="是否存在不服从管理或自残行为" prop="sfczbfcglhzcxw"
                :rules="[{ trigger: 'blur,change', message: '请选择是否存在不服从管理或自残行为', required: true }]">
                <RadioGroup v-model="formData.sfczbfcglhzcxw">
                  <Radio label="1">是</Radio>
                  <Radio label="2">否</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="是否存在其他危险行为或异常情况" prop="sfczqtwxxwhycqk"
                :rules="[{ trigger: 'blur,change', message: '请选择是否存在其他危险行为或异常情况', required: true }]">
                <RadioGroup v-model="formData.sfczqtwxxwhycqk">
                  <Radio label="1">是</Radio>
                  <Radio label="2">否</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="16">
              <FormItem label="病情描述" prop="symptomDesc"
                :rules="[{ trigger: 'change', message: '病情描述为必填', required: true }]">
                <Input v-model="formData.symptomDesc" placeholder="请填写" type="textarea"
                  :autosize="{ minRows: 2, maxRows: 5 }"></Input>
              </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="16">
              <FormItem label="备注" prop="remark">
                <Input v-model="formData.remark" placeholder="请填写" type="textarea"
                  :autosize="{ minRows: 2, maxRows: 5 }"></Input>
              </FormItem>
              </Col>
            </Row>
          </div>
        </div>
      </Form>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
      <Button style="margin: 0 20px" :loading="loadingSave" @click="handleSubmit(false)">暂 存</Button>
      <Button style="margin: 0 20px" type="primary" :loading="loading" @click="handleSubmit(true)">提交</Button>
    </div>

    <start-approval ref="approval" :assigneeUserId="approvalData.assigneeUserId"
      :assigneeUserName="approvalData.assigneeUserName" :assigneeOrgId="approvalData.assigneeOrgId"
      :assigneeOrgName="approvalData.assigneeOrgName" :assigneeAreaId="approvalData.assigneeAreaId"
      :assigneeAreaName="approvalData.assigneeAreaName" :definition="approvalData.definition" :bindEvent="false"
      :showcc="false" :error="startError" :businessId="approvalData.businessId" :variables="approvalData.variables"
      :startUpSuccess="startUpSuccess" :beforeOpen="beforeOpen" :msgUrl="msgUrl" :msgTit="msgTit"
      :module="module"></start-approval>

  </div>
</template>

<script>
import { startApproval } from 'gs-start-approval'
import { userSelector } from 'sd-user-selector'
import { mapActions } from "vuex";
import { getUserCache } from '@/libs/util'

export default {
  components: {
    userSelector, startApproval
  },
  props: {
    rowData: {
      type: [Array, Object],
      default: {}
    },
    entireProcess: {
      default: false,
    }
  },
  data() {
    return {
      combineInfoData: {

      },
      formData: {
        sfczjjfyhkjzlqk: '2',
        sfcztsywqk: '2',
        sfczbfcglhzcxw: '2',
        sfczqtwxxwhycqk: '2'
      },
      loading: false,
      loadingSave: false,
      ruleValidate: {},
      msgUrl: '/#/detentionBusiness/outForTreatmentRegister',
      msgTit: '【审批】出所就医',
      businessId: this.rowData.jgrybm,
      module: serverConfig.APP_MARK,
      approvalData: {
        definition: [
          {
            name: '出所就医流程审批',
            defKey: 'chusuojiuyi123'
          }
        ],
        assigneeOrgId: this.$store.state.common.orgCode,
        assigneeOrgName: this.$store.state.common.orgName,
        assigneeUserId: this.$store.state.common.idCard,
        assigneeUserName: this.$store.state.common.userName,
        businessId: this.rowData.jgrybm,
        fApp: serverConfig.APP_MARK,
        fXxpt: 'pc',
        variables: {
          eventCode: this.rowData.jgrybm,
          busType: '105_003'
        }
      },
      isTest: true,  //演示用
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    getCurrentTimeFormatted() {
      const now = new Date();

      const year = now.getFullYear();             // 获取年份
      const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，要加1，并补零
      const day = String(now.getDate()).padStart(2, '0');        // 日期补零

      const hours = String(now.getHours()).padStart(2, '0');     // 小时补零
      const minutes = String(now.getMinutes()).padStart(2, '0'); // 分钟补零
      const seconds = String(now.getSeconds()).padStart(2, '0'); // 秒数补零

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleClose() {
      this.$emit('close', false)
    },
    handleNext(info) {
      this.$emit('nextStep', info)
    },
    getDetail(jgrybm) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_outHospitalGetByJgrybm,
        params: {
          jgrybm: jgrybm,
          current_step: this.rowData.current_step
        }
      }).then(resp => {
        if (resp.code == 0) {
          if (resp.data) {
            this.formData = resp.data
            if (this.formData.sfczjjfyhkjzlqk) {
              this.formData.sfczjjfyhkjzlqk = this.formData.sfczjjfyhkjzlqk.toString()
            } else {
              this.formData.sfczjjfyhkjzlqk = '2'
            }
            if (this.formData.sfcztsywqk) {
              this.formData.sfcztsywqk = this.formData.sfcztsywqk.toString()
            } else {
              this.formData.sfcztsywqk = '2'
            }
            if (this.formData.sfczbfcglhzcxw) {
              this.formData.sfczbfcglhzcxw = this.formData.sfczbfcglhzcxw.toString()
            } else {
              this.formData.sfczbfcglhzcxw = '2'
            }
            if (this.formData.sfczqtwxxwhycqk) {
              this.formData.sfczqtwxxwhycqk = this.formData.sfczqtwxxwhycqk.toString()
            } else {
              this.formData.sfczqtwxxwhycqk = '2'
            }
            if (this.formData.symptomDesc) {
              this.formData.symptomDesc = this.formData.symptomDesc.toString()
            }
          }
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    handleSubmit(tag) {
      console.log(this.formData, "提交数据-----")
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.saveData(tag)
        } else {
          this.loading = false
          this.$Message.error('请填写完整!!');
        }
      })
    },
    saveData(tag) {
      this.formData.dataSources ? '' : this.$set(this.formData, 'dataSources', 0)
      let params = this.formData
      params.jgrybm = this.rowData.jgrybm
      params.jgryxm = this.rowData.jgryxm
      if (tag) {
        
        params.status = "03"
      } else {
        this.loadingSave = true
        params.status = "02"
      }
      if (tag) {
        console.log("启动流程")
        this.businessId = this.rowData.jgrybm
        
        this.msgUrl = '/#/detentionBusiness/outForTreatmentRegister?eventCode=' + this.rowData.jgrybm + "&step=02"
        this.$refs['approval'].openStartApproval()
      } else {
        this.$store.dispatch('authPostRequest', {
          url: this.$path.app_outHospitalDoctorRegister,
          params: params
        }).then(resp => {   
          this.loadingSave = false
          if (resp.code == 0) {
            this.$Message.success('提交成功!');
            this.handleClose();
          } else {
            this.$Notice.error({
              title: '错误提示',
              desc: resp.msg
            })
          }
        })
      }

    },
    startError(data) {
      this.errorModal({ content: '流程启动失败。原因:' + data.msg }).then(() => {
        location.reload()
      })
    },
    startUpSuccess(data) {
      return new Promise((resolve, reject) => {
        let that = this
        setTimeout(() => {
          this.updateRegStatus(data)
        }, 500)
      })
    },
    updateRegStatus(data) {
      let params = {
        status: "03",
        jgrybm: this.rowData.jgrybm,
        jgryxm: this.rowData.jgryxm,
        taskId: "",
        actInstId: data.actInstId,
        current_step: this.rowData.current_step
      }
      //调用接口
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_outHospitalDoctorRegister,
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.$Message.success('提交成功!');
          if (this.isTest) {
            let info = {
              actInstId: params.actInstId
            }
            this.handleNext(info)
          } else {
            this.handleClose();
          }
        } else {
          this.$Message.error(resp.msg);
        }
      })
    },
    beforeOpen() {
      return new Promise((resolve, reject) => {
        this.$set(this.approvalData, 'businessId', this.rowData.jgrybm)
        let jbsj = this.getCurrentTimeFormatted();
        let jbr = getUserCache.getUserName()
        this.msgUrl = '/#/detentionBusiness/outForTreatmentRegister?eventCode=' + this.rowData.jgrybm + "&step=02"
        this.msgTit = `【出所就医审批】民警：${jbr}于${jbsj}提交了对${this.rowData.jgryxm}出所就医的申请，请尽快审批！`
        resolve(true)
      })
    },
  },
  mounted() {
    this.getDetail(this.rowData.jgrybm)
  }
}
</script>

<style scoped>
@import "~@/assets/style/formInfo.css";

.fm-content-info {
  padding: 26px 0 26px 0 !important;
}

.action-gather {
  display: flex;
}
</style>
