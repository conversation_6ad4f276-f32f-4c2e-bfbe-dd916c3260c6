<template>
  <!-- 详情页面 -->
  <div class="detail-wrap">
    <Row style="margin-top: 1px">
      <Col span="8">
        <span class="Light-blue">被监管人员</span>
        <span class="ligth-gray">{{ jgryxm }}</span>
      </Col>
      <Col span="8">
        <span class="Light-blue">监室号</span>
        <span class="ligth-gray">{{ roomName }}</span>
      </Col>
      <Col span="8">
        <span class="Light-blue">申请时间</span>
        <span class="ligth-gray">{{ formData.applyTime }} </span>
      </Col>
    </Row>
    <Row style="margin-top: 1px">
      <Col span="8">
        <span class="Light-blue">收信人姓名</span>
        <span class="ligth-gray">{{ formData.receiveMailUser }}</span>
      </Col>
      <Col span="8">
        <span class="Light-blue">关系</span>
        <span class="ligth-gray">{{ formData.relationName }}</span>
      </Col>
      <Col span="8">
        <span class="Light-blue">来信地址</span>
        <span class="ligth-gray">{{ formData.receiveAddress }} </span>
      </Col>
    </Row>
  </div>
</template>

<script>

export default {
  props: {
    formData: Object,
    jgryxm: {
      default: '',
      type: String
    },
    roomName: {
      default: '',
      type: String
    }
  },
  components: {},
  data() {
    return {
    }
  }
}
</script>

<style>

.ligth-gray {
  display: inline-block;
  flex-shrink: 0;
  background-color: #f5f7fa;
  width: 70%;
  line-height: 70px;
  text-align: center; /* 水平居中 */
  vertical-align: middle; /* 垂直居中 */
}

.Light-blue {
  display: inline-block;
  line-height: 70px;
  text-align: center; /* 水平居中 */
  vertical-align: middle; /* 垂直居中 */
  width: 30%;
  flex-shrink: 0;
  background-color: #e4eefc;
  margin: 0 1px;
}

.detail-wrap {
  width: 100%;
}

</style>
<style scoped lang="less">

.viewImg /deep/ img {
  width: 150px !important;
  height: 90px !important;
}
</style>
