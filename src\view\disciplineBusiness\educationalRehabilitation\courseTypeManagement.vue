<template>
  <div>
    <div class="table-container" v-if="tableContainer">
      <s-DataGrid ref="grid" funcMark="kfjykcmclb" :customFunc="true" :params="params">
        <!-- 设备报修登记 -->
        <template slot="customHeadFunc" slot-scope="{ func }">
          <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':kfjykcmclb:add')"
            @click.native="handleAddKc('add')">新增课程</Button>
        </template>
        <template slot="slot_is_enabled" slot-scope="{ func, row, index }">
          <i-switch v-model="row.is_enabled" size="large" :true-value="1" :false-value="0"
            @on-change="changeSwitch(row)">
            <span slot="open">开启</span>
            <span slot="close">关闭</span>
          </i-switch>
        </template>
        <template slot="customRowFunc" slot-scope="{ func, row, index }">
          <Button type="primary" style="margin-left: 10px;" v-if="func.includes(globalAppCode + ':kfjykcmclb:edit')"
            @click.native="handleEditKc(index, row)">编辑</Button>
          <Button style="margin-left: 10px;" type="error" v-if="func.includes(globalAppCode + ':kfjykcmclb:del')"
            @click.native="handleDelKc(index, row)">删除</Button>
        </template>
      </s-DataGrid>
    </div>
    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="900"
      title="新增课程">
      <Form ref="form" :model="form" :label-width="120">
        <FormItem label="课程名称" prop="coursesName" :rules="{ required: true, message: '请输入课程名称', trigger: 'blur' }">
          <Input v-model="form.coursesName" placeholder="请输入课程名称" size="large">
          </Input>
        </FormItem>

        <FormItem label="启用" prop="isEnabled" :rules="{ required: true, message: '请输入课程名称', trigger: 'change', type: 'number' }">
          <i-switch v-model="form.isEnabled" size="large" :true-value="1" :false-value="0" >
            <span slot="open">开启</span>
            <span slot="close">关闭</span>
          </i-switch>
        </FormItem>

        <FormItem label="分类颜色" prop="coursesColor" :rules="{ required: true, message: '请选择分类颜色', trigger: 'change' }">
          <div class="color-picker-container">
            <div class="color-preview" :style="{ backgroundColor: form.coursesColor || '#3097ff' }">
              <span v-if="!form.coursesColor">选择颜色</span>
            </div>
            <div class="color-options">
              <div class="predefined-colors">
                <div class="color-label">预设颜色：</div>
                <div class="color-grid">
                  <div v-for="color in colorPool" :key="color" class="color-item"
                    :class="{ 'selected': form.coursesColor === color }" :style="{ backgroundColor: color }"
                    @click="selectColor(color)" :title="color">
                  </div>
                </div>
              </div>
              <div class="custom-color">
                <div class="color-label">自定义颜色：</div>
                <input type="color" v-model="form.coursesColor" class="color-input" @change="onColorChange">
              </div>
            </div>
          </div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="primary" @click="handleSumbit('form')" class="save">确 定</Button>
        <Button @click="handleCancel('form')" class="save">关 闭</Button>
      </div>
    </Modal>
  </div>

</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions, mapState } from 'vuex'

export default {
  name: "courseTypeManagement",
  data() {

    return {
      tableContainer: true,
      params: {},
      form: {
        coursesColor: "",
        coursesName: "",
        isEnabled: 1
      },
      openModal: false,
      colorPool: [
        '#3097ff', // 蓝色
        '#ed8433', // 橙色
        '#33bb83', // 绿色
        '#8e6bee', // 紫色
        '#f56c6c', // 红色
        '#409eff', // 天蓝色
        '#67c23a', // 草绿色
        '#e6a23c', // 金黄色
        '#f78989', // 粉红色
        '#9c88ff', // 淡紫色
        '#36cfc9', // 青色
        '#ff9c6e', // 橘色
        '#73d13d', // 亮绿色
        '#ff85c0', // 玫红色
        '#597ef7', // 靛蓝色
      ],
      rowId: "",
    }

  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleAddKc(row) {
      this.openModal = true
    },
    handleEditKc(idx, { id }) {
      this.authGetRequest({ url: this.$path.edurehabCourses_get, params: { id } }).then(res => {
        if (res.success) {
          this.openModal = true
          this.form = res.data
          this.rowId = res.data.id
        }
      })
    },
    handleDelKc(idx, { id }) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: () => {
          this.$store.dispatch('authGetRequest', {
            url: this.$path.edurehabCourses_delete,
            params: {
              ids: id
            }
          }).then(res => {
            if (res.success) {
              this.$refs.grid.query_grid_data(1)
            }
          })
        }
      })
    },
    // 选择预设颜色
    selectColor(color) {
      this.form.coursesColor = color
    },
    // 自定义颜色变化事件
    onColorChange(event) {
      this.form.coursesColor = event.target.value
    },
    changeSwitch(row) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否更新？',
        onOk: () => {
          this.$store.dispatch('authPostRequest', {
            url: this.$path.edurehabCourses_update,
            params: {
              coursesName: row.courses_name,
              coursesColor: row.courses_color,
              isEnabled: row.is_enabled,
              id: row.id
            }
          }).then(res => {
            if (res.success) {
              this.$refs.grid.query_grid_data(1)
            }
          })
        },
        onCancel: () => {
          let num = row.is_enabled == 1 ? 0 : 1
          this.$store.dispatch('authPostRequest', {
            url: this.$path.edurehabCourses_update,
            params: {
              coursesName: row.courses_name,
              coursesColor: row.courses_color,
              isEnabled: num,
              id: row.id
            }
          }).then(res => {
            if (res.success) {
              this.$refs.grid.query_grid_data(1)
            }
          })
        }
      })
    },
    handleSumbit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let url = ""
          let msg = ""
          if (this.rowId) {
            url = this.$path.edurehabCourses_update
            msg = "更新成功"
          } else {
            url = this.$path.edurehabCourses_create
            msg = "新增成功"
          }
          this.authPostRequest({ url: url, params: { ...this.form, id: this.rowId } }).then(res => {
            if (res.success) {
              this.openModal = false
              this.rowId = ""
              this.$refs[name].resetFields();
              this.$refs.grid.query_grid_data(1)
              this.$Message.success(msg)
            } else {
              this.$Message.error(res.message)
            }
          })
        } else {
          this.$Message.error('验证未通过');
        }
      })
    },
    handleCancel(name) {
      this.$refs[name].resetFields();
    },
  },

  components: {
    sDataGrid
  },

  created() { },

  computed: {},

}

</script>

<style scoped lang="less">
// 分类颜色现在通过动态style设置，不再需要固定的CSS类

// 颜色选择器样式
.color-picker-container {
  .color-preview {
    width: 100%;
    height: 50px;
    border: 2px solid #e9edf5;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    color: #fff;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;

    &:hover {
      border-color: #2b5fda;
    }
  }

  .color-options {
    .predefined-colors {
      margin-bottom: 16px;

      .color-label {
        font-size: 14px;
        color: #2b3346;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .color-grid {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        gap: 8px;

        .color-item {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          cursor: pointer;
          border: 2px solid transparent;
          transition: all 0.3s ease;
          position: relative;

          &:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }

          &.selected {
            border-color: #2b5fda;
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(43, 95, 218, 0.3);

            &::after {
              content: '✓';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              color: #fff;
              font-weight: bold;
              font-size: 14px;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            }
          }
        }
      }
    }

    .custom-color {
      .color-label {
        font-size: 14px;
        color: #2b3346;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .color-input {
        width: 60px;
        height: 40px;
        border: 2px solid #e9edf5;
        border-radius: 6px;
        cursor: pointer;
        transition: border-color 0.3s ease;

        &:hover {
          border-color: #2b5fda;
        }

        &::-webkit-color-swatch-wrapper {
          padding: 0;
        }

        &::-webkit-color-swatch {
          border: none;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
