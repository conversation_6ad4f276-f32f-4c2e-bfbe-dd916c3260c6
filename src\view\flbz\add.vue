<template>
  <div class="flbz-wrap">
	<div class="flbz-content">
		<div class="form-container" style="padding: 10px; display: flex; flex-direction: column; flex: 1">
			<Form ref="addModelForm" :model="modelForm" :label-width="190" :label-colon="true">
				<div class="flbz-container">
					<div class="flbz-cont-left">
					<div class="flbz-topLeft">
						<!-- 使用新的人员选择组件 -->
						<personnel-selector v-model="modelForm.jgrybm" title="被拘留人员" placeholder="点击选择在押人员或扫码识别" :show-case-info="true"
							:enable-scan="true" :show-scan-tip="true" @change="handlePersonnelChange" />
					</div>
					</div>
					<div class="flbz-cont-right">
						<div class="list-title">申请信息</div>
						<div class="flbz-sq">
							<div>
								<Row>
									<Col span="24">
										<FormItem label="申请时间" prop="applyTime" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]" style="width: 100%">
											<el-date-picker type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" v-model="modelForm.applyTime" size="small"  placeholder="请选择" style="width: 100%;" />
										</FormItem>
									</Col>
									<Col span="24">
										<FormItem label="申请帮助事项" prop="assistanceMatter" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]" style="width: 100%">
											<Input v-model="modelForm.assistanceMatter" placeholder="请输入" type="textarea" :autosize="{minRows: 2,maxRows: 5}" />
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="是否需要办案机关许可" prop="isNeedCaseUnitAllowed" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]" style="width: 100%">
											<RadioGroup v-model="modelForm.isNeedCaseUnitAllowed">
												<Radio label="1">需要</Radio>
												<Radio label="0">不需要</Radio>
											</RadioGroup>
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="办案机关类型" prop="caseUnitType" style="width: 100%" v-if="modelForm.isNeedCaseUnitAllowed == '1'">
											<RadioGroup v-model="modelForm.caseUnitType">
												<Radio label="0">法院</Radio>
												<Radio label="1">检察院</Radio>
												<Radio label="2">办案单位</Radio>
											</RadioGroup>
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="办案机关名称" prop="caseUnitName" style="width: 100%" v-if="modelForm.isNeedCaseUnitAllowed == '1'">
											<Input v-model="modelForm.caseUnitName" placeholder="请输入" type="text" />
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="办案机关是否许可" prop="isCaseUnitAllowed" style="width: 100%" v-if="modelForm.isNeedCaseUnitAllowed == '1'">
											<RadioGroup v-model="modelForm.isCaseUnitAllowed">
												<Radio label="1">是</Radio>
												<Radio label="0">否</Radio>
											</RadioGroup>
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="办案机关反馈" prop="caseUnitFeedback" style="width: 100%" v-if="modelForm.isNeedCaseUnitAllowed == '1'">
											<Input v-model="modelForm.caseUnitFeedback" placeholder="请输入" type="text" />
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="决定时间" prop="decisionTime" style="width: 100%" v-if="modelForm.isNeedCaseUnitAllowed == '1'">
											<el-date-picker type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"  v-model="modelForm.decisionTime" size="small"  placeholder="请选择" style="width: 100%;" />
										</FormItem>
									</Col>
								</Row>
							</div>
						</div>
						<div class="list-title">值班律师信息/会见信息</div>
						<div class="flbz-ls">
							<div class="ls-info">
								<p class="ls-title">律师</p>
								<div style="padding: 12px; padding-top: 15px;">
									<Row>
										<Col span="12">
											<FormItem label="姓名" prop="lawyerName" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]" style="width: 100%" :label-width="130">
												<Input type="text" v-model="modelForm.lawyerName" placeholder="请填写" />
											</FormItem>
										</Col>
										<Col span="12">
											<FormItem label="身份证号码" prop="lawyerIdNumber" style="width: 100%" :label-width="130">
												<Input type="text" v-model="modelForm.lawyerIdNumber" placeholder="请填写" />
											</FormItem>
										</Col>
										<Col span="12">
											<FormItem label="执业证号码" prop="lawyerPracticeLicenseNumber" style="width: 100%" :label-width="130">
												<Input type="text" v-model="modelForm.lawyerPracticeLicenseNumber" placeholder="请填写" />
											</FormItem>
										</Col>
										<Col span="12">
											<FormItem label="律师单位" prop="lawyerFirm" style="width: 100%" :label-width="130">
												<Input type="text" v-model="modelForm.lawyerFirm" placeholder="请填写" />
											</FormItem>
										</Col>
									</Row>
								</div>
							</div>
						</div>
						<div class="flbz-lsInfo">
							<div>
								<Row>
									<Col span="12">
										<FormItem label="会见方式" prop="meetingMethod" style="width: 100%">
											<s-dicgrid v-model="modelForm.meetingMethod" style="width: 300px;"  dicName="ZD_WB_LSHJFS" />
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="会见室" prop="meetingRoomName" style="width: 100%">
											<Input @on-search="openRoom" search  enter-button="选择" placeholder="" v-model="modelForm.meetingRoomName" />
										</FormItem>
									</Col>
									<Col span="24">
										<FormItem label="是否特殊会见" prop="isSpecialMeeting" style="width: 100%">
											<RadioGroup v-model="modelForm.isSpecialMeeting">
												<Radio label="1">是</Radio>
												<Radio label="0">否</Radio>
											</RadioGroup>
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="带出监室时间" prop="escortingTime" style="width: 100%" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]" >
											<el-date-picker type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" v-model="modelForm.escortingTime" size="small"  placeholder="请选择" style="width: 100%;" />
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="带出安检结果" prop="inspectionResult" style="width: 100%" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]" >
											<!-- <RadioGroup v-model="modelForm.isNeedCaseUnitAllowed">
												<Radio label="1">正常</Radio>
												<Radio label="0">异常</Radio>
											</RadioGroup> -->
											<s-dicgrid v-model="modelForm.inspectionResult" style="width: 100%;" dicName="ZD_WB_JCJG" />
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="会见开始时间" prop="meetingStartTime" style="width: 100%" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]" >
											<el-date-picker type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" v-model="modelForm.meetingStartTime" size="small"  placeholder="请选择" style="width: 100%;" />
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="会见结束时间" prop="meetingEndTime" style="width: 100%" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]" >
											<el-date-picker type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" v-model="modelForm.meetingEndTime" size="small"  placeholder="请选择" style="width: 100%;" />
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="带出民警" prop="escortingPolice" style="width: 100%">
											 <user-selector v-model="modelForm.escortingPoliceSfzh" tit="用户选择"
												:text.sync="modelForm.escortingPolice" returnField="idCard"
												:orgCode="orgCode"
												numExp='num>=1' msg="至少选中1人">
											</user-selector>
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="带回民警" prop="returnPolice" style="width: 100%">
											 <user-selector v-model="modelForm.returnPoliceSfzh" tit="用户选择"
												:text.sync="modelForm.returnPolice"
												returnField="idCard"
												:orgCode="orgCode"
												numExp='num>=1' msg="至少选中1人">
											</user-selector>
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="带回监室时间" prop="returnTime" style="width: 100%" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]" >
											<el-date-picker type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" v-model="modelForm.returnTime" size="small"  placeholder="请选择" style="width: 100%;" />
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="带回安检结果" prop="returnInspectionResult" style="width: 100%" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]">
											<!-- <RadioGroup v-model="modelForm.isNeedCaseUnitAllowed">
												<Radio label="1">正常</Radio>
												<Radio label="0">异常</Radio>
											</RadioGroup> -->
											<s-dicgrid v-model="modelForm.returnInspectionResult" style="width: 100%;" dicName="ZD_WB_JCJG" />
										</FormItem>
									</Col>
									<Col span="24">
										<FormItem label="异常情况登记" prop="abnormalSituations" style="width: 100%">
											<Input v-model="modelForm.abnormalSituations" placeholder="请输入" type="textarea" :autosize="{minRows: 2,maxRows: 5}" />
										</FormItem>
									</Col>
									<Col span="24">
										<FormItem label="监督民警" style="width: 100%">
											<user-selector v-model="modelForm.PoliceList" tit="用户选择"
												:text.sync="modelForm.PoliceList"
												@onSelect="selectJd" returnField="id"
												numExp='num>=1' msg="至少选中1人">
											</user-selector>
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="律师是否违规" prop="isLawyerViolation" style="width: 100%">
											<RadioGroup v-model="modelForm.isLawyerViolation">
												<Radio label="1">是</Radio>
												<Radio label="0">否</Radio>
											</RadioGroup>
										</FormItem>
									</Col>
									<Col span="12">
										<FormItem label="律师是否告知在押人员异常行为" prop="isLawyerInformAbnormalBehav" style="width: 100%">
											<RadioGroup v-model="modelForm.isLawyerInformAbnormalBehav">
												<Radio label="1">是</Radio>
												<Radio label="0">否</Radio>
											</RadioGroup>
										</FormItem>
									</Col>
									<Col span="24">
										<FormItem label="律师违规情况" prop="lawyerViolationDetails" style="width: 100%" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]" v-if="modelForm.isLawyerViolation == '1'">
											<Input v-model="modelForm.lawyerViolationDetails" placeholder="请输入" type="textarea" :autosize="{minRows: 2,maxRows: 5}" />
										</FormItem>
									</Col>
									<Col span="24">
										<FormItem label="在押人员异常行为情况" prop="abnormalBehaviorDetails" style="width: 100%" :rules="[  { trigger: 'change', message: '请填写模型名称', required: true}, ]" v-if="modelForm.isLawyerInformAbnormalBehav == '1'">
											<Input v-model="modelForm.abnormalBehaviorDetails" placeholder="请输入" type="textarea" :autosize="{minRows: 2,maxRows: 5}" />
										</FormItem>
									</Col>
								</Row>
							</div>
						</div>
					</div>
				</div>
			</Form>
		</div>
	</div>
	<div class='bsp-base-fotter'>
      <Button @click='goBack'>取 消</Button>
      <Button @click="saveData" type="primary" :loading='loading'>提 交</Button>
    </div>

	<Modal
        v-model="openModalRoom"
        :mask-closable="false"
        :closable="true"
        class-name="select-room-modal"
        width="50%"
        title="空闲审讯室"
        >
        <interrogationRoom v-if='openModalRoom' :curId='curId' @selectRoom='selectRoom' />
        <div slot="footer">
          <Button @click="openModalRoom=false">取消</Button>
          <Button type="primary" @click="submitRoom" >提交</Button>
        </div>
      </Modal>
  </div>
</template>

<script>
import personnelSelector from "@/components/personnel-selector/index.vue"
import { getUuid,removeNullFields,getUserCache,formatDateparseTime } from "@/libs/util.js";
import { mapActions } from 'vuex'
import { sDialog } from 'sd-custom-dialog'
import { userSelector } from 'sd-user-selector'
import interrogationRoom from "./interrogationRoom"
export default {
	components: {
		personnelSelector,
		sDialog,
		userSelector,
		interrogationRoom
	},
	props: {
		saveType: {
			type: String,
			default: ''
		},
		curId: {
			type: String,
			default: ''
		}
	},
	watch: {
		curId: {
			handler(value) {
				// this.getModelForm(value)
			}
		},
		deep: true,
      	immediate: true // 立即执行
	},
	data() {
		return {
			modelForm: {},
			columns: [
                {
                    title: '序号',
                    type: 'index',
                    width: "80",
                    align: "center"
                },
                {
                    title: '*来访人员姓名',
                    slot: 'name',
                    align: "center",
                    align: "center",
                },
                {
                    title: '证件类型',
					slot: 'idType',
                    align: "center",
                },
                {
                    title: '证件号码',
                    slot: 'idNumber',
                    align: "center"
                },
                {
                    title: '操作',
                    slot: 'cz',
                    width: "300",
                    align: "center",
                }
            ],
			loading: false,
			formData: {},
			openModalRoom: false,
			orgCode: this.$store.state.common.orgCode
		}
	},
	methods: {
		...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
		goBack() {
			this.$emit('toback')
		},
		validateForm() {
			let isValid = true;
			this.modelForm.personList.forEach((row) => {
				if (!row.name) {
					isValid = false;
				}
			});
			return isValid;
		},
		saveData() {
			console.log(this.modelForm,'modelForm')
			let url = ''
			this.$refs.addModelForm.validate((valid) => {
				if(valid) {
					console.log(this.modelForm,'modelForm')
					if(this.saveType == 'add') {
						this.submitData()
					} else {
						this.updateEvent()
					}
					
				} else {
					this.$Message.error('请填写完整内容!')
				}
			})
		},
		submitData() {
			this.$store.dispatch('authPostRequest',{
				url: this.$path.acp_legalAssistance_create,
				params: this.modelForm
			}).then(res => {
				if(res.success) {
					// console.log(res,'res')
					this.$Message.success('新增登记成功!')
					this.$nextTick(() => {
						this.goBack()
					})
				} else {
					this.$Modal.error({
						title: '温馨提示',
						content: res.msg || '接口操作失败!'
					})
				}
			})
		},
		updateEvent() {
			this.$store.dispatch('authPostRequest',{
				url: this.$path.acp_legalAssistance_update,
				params: this.modelForm
			}).then(res => {
				if(res.success) {
					this.$Message.success('编辑成功!')
					this.$nextTick(() => {
						this.goBack()
					})
				} else {
					this.$Modal.error({
						title: '温馨提示',
						content: res.msg || '接口操作失败!'
					})
				}
			})
		},
		remove(row,index) {
			if (this.modelForm.personList.length === 1) {
				this.$Message.warning("至少保留一行数据，无法删除");
				return;
			}
			this.modelForm.personList.splice(index, 1);
		},
		addCF(index) {
			this.modelForm.personList.splice(index + 1, 0, {
				name: '',
				idType: '111',
				idNumber: ''
			});
			// 强制更新
			this.$set(this.modelForm.personList, index + 1, { ...this.modelForm.personList[index + 1] });
		},
		getModelForm(id) {
			this.$store.dispatch('authGetRequest',{
				url: this.$path.acp_legalAssistance_get,
				params: {
					id
				}
			}).then(res => {
				if(res.success) {
					console.log(res,'res')
					this.modelForm = res.data
				} else {
					this.$Modal.error({
						title: '温馨提示',
						content: res.msg || '接口操作失败!'
					})
				}
			})
		},
		handlePersonnelChange(personnelData, jgrybm) {
            console.log('选择的人员:', personnelData, jgrybm)
            if (personnelData && jgrybm) {
                // this.modelForm.prison = personnelData
                this.modelForm.jgrybm = jgrybm
                this.modelForm.jgryxm = personnelData.xm
                this.$Message.success(`已选择人员: ${personnelData.xm || '未知'}`)
            } else {
                // 清空人员信息
                this.modelForm.prison = null
                this.modelForm.jgrybm = ''
                this.modelForm.jgryxm = ''
            }
        },
		openRoom(){
			console.log(this.openModalRoom,'this.openModalRoom')
			this.openModalRoom=true
		},
		submitRoom(){
			this.openModalRoom=false
		},
		selectRoom(roomId,roomName){
			this.$set(this.modelForm,'meetingRoomId',roomId)
			this.$set(this.modelForm,'meetingRoomName',roomName)
      	},
		selectJd(data) {
			console.log(data,'监督民警');
			if(data) {
				this.modelForm.supervisingPoliceList = data.map(item => ({
					xm: item.name,
					zjhm: item.idCard
				}));
				// this.modelForm.supervisingPoliceList = JSON.stringify(this.modelForm.supervisingPoliceList)
			}
		}
	},
	mounted() {
		if(!this.modelForm.visitTime){
            this.$set(this.modelForm,'visitTime',formatDateparseTime(new Date()))
        }

		if(this.saveType == 'edit') {
			// this.modelForm = {}
			this.getModelForm(this.curId)
		}
	}
}
</script>

<style lang="less" scoped>
/deep/.list-title {
  line-height: 2.4;
  background: #f2f5fc;
  font-size: 14px;
  padding: 0 0.8em;
  font-weight: bold;
  border: 1px solid #CEE0F0;
  border-bottom: unset;
  margin-bottom: 10px;
}
.flbz-wrap{
	width: 100%;
}
.flbz-content{
	width: 100%;
}
.flbz-wrap{
	padding-top: 20px;
}
.required-field {
  color: red;
  margin-right: 4px;
}
/deep/.ivu-table-cell-slot {
  display: flex !important;
}
.flbz-container{
	width: 100%;
	display: flex;
	.flbz-cont-left{
		width: 23%;
	}
	.flbz-cont-right{
		flex: 1;
		padding-left: 20px;
		border-left: 1px solid #dcdee2;
		.flbz-ls{
			width: 100%;
			margin-bottom: 20px;
			.ls-info {
				width: 50%;
				border: 1px solid #ccc;
				border-radius: 4px;
				.ls-title{
					line-height: 36px;
					font-size: 16px;
					font-weight: bold;
					color: #000;
					text-align: center;
					background: #ccc;
				}
			}
		}
	}
}
// .flbz-cont-top{
// 	width: 100%;
// 	display: flex;
// 	.flbz-topLeft{
// 		width: 25%;
// 	}
// 	.flbz-topRight{
// 		flex: 1;
// 	}
// }
// .flbz-cont-bottom{
// 	width: 100%;
// 	display: flex;
// 	.flbz-bottomLeft{
// 		width: 25%;
// 	}
// 	.flbz-bottomRight{
// 		flex: 1;
// 	}
// }
</style>