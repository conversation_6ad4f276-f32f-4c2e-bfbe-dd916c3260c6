<template>
  <!-- 详情页面 -->
  <div class="detail-wrap">
    <div class="bsp-base-form">
      <div class="bsp-base-tit">
        {{ editTitle }}
      </div>
      <div class="prison-select-center">
        <div class="prison-select-center-top">
          <div class="prison-select-center-top-ryxx">
            <ryxx :jgrybm="jgrybm" v-if="jgrybm" style="margin-top: -16px;"/>
          </div>
          <div class="prison-select-center-top-detail">
            <Row style="margin-top: 1px">
              <Col span="8">
                <span class="Light-blue">被监管人员</span>
                <span class="ligth-gray">{{ jgryxm }}</span>
              </Col>
              <Col span="8">
                <span class="Light-blue">监室号</span>
                <span class="ligth-gray">{{ roomName }}</span>
              </Col>
              <Col span="8">
                <span class="Light-blue">申请时间</span>
                <span class="ligth-gray">{{ formData.applyTime }} </span>
              </Col>
            </Row>
            <Row style="margin-top: 1px">
              <Col span="8">
                <span class="Light-blue">收信人姓名</span>
                <span class="ligth-gray">{{ formData.receiveMailUser }}</span>
              </Col>
              <Col span="8">
                <span class="Light-blue">关系</span>
                <span class="ligth-gray">{{ formData.relationName }}</span>
              </Col>
              <Col span="8">
                <span class="Light-blue">来信地址</span>
                <span class="ligth-gray">{{ formData.receiveAddress }} </span>
              </Col>
            </Row>
            <Row style="margin-top: 1px">
              <Col span="8">
                <span class="Light-blue">审批状态</span>
                <span class="ligth-gray">{{ formData.approverStatusName }}</span>
              </Col>
              <Col span="8">
                <span class="Light-blue">信件状态</span>
                <span class="ligth-gray">{{ formData.statusName }}</span>
              </Col>
              <Col span="8">
                <span class="Light-blue"></span>
                <span class="ligth-gray"></span>
              </Col>
            </Row>
          </div>
        </div>
        <div class="prison-select-center-bottom">
          <div style="background: #fff;width: 100%;margin-right: 1px;">
            <p class="sys-sub-title">流程轨迹</p>
            <record :formData="formData"  />
          </div>
        </div>
      </div>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回</Button>
    </div>
  </div>
</template>

<script>

import viewImg from "_c/main/components/viewImg/viewImg.vue"
import ryxx from "./ryxx.vue"
import headerDetail from "./headerDetail.vue"
import record from "./record.vue"
export default {
  props: {
    jgryxm: {
      default: '',
      type: String
    },
    jgrybm: {
      default: '',
      type: String
    },
    roomName: {
      default: '',
      type: String
    },
    curId: {
      default: '',
      type: String
    }
  },
  components: {headerDetail, ryxx, viewImg, record},
  data() {
    return {
      showattUrl: true,
      formData: {},
      defaultListsuper: [],
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      editTitle: '收信信息'
    }
  },
  methods: {
    handleClose() {
      this.$emit('close', false)
    },
    getData() {
      this.$store.dispatch("authGetRequest", {
        url: this.$path.familyContact_send_get,
        params: {
          id: this.curId
        }
      }).then(res => {
        if (res.success) {
          this.formData = res.data
          this.formData.jgryxm = this.jgryxm
          this.formData.roomName = this.roomName
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败'
          })
        }
      })
    }
  },
  mounted() {
    this.getData()
  }
}
</script>
<style scoped lang="less">

.ligth-gray {
  display: inline-block;
  flex-shrink: 0;
  background-color: #f5f7fa;
  width: 70%;
  line-height: 95px;
  text-align: center; /* 水平居中 */
  vertical-align: middle; /* 垂直居中 */
}

.Light-blue {
  display: inline-block;
  line-height: 95px;
  text-align: center; /* 水平居中 */
  vertical-align: middle; /* 垂直居中 */
  width: 30%;
  flex-shrink: 0;
  background-color: #e4eefc;
  margin: 0 1px;
}

.prison-select-center-top-ryxx {
  width: 30%;
}

.prison-select-center-top-detail {
  width: 70%;
  margin-right: 25px;
}

.prison-select-center {
  border-radius: 6px;
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  margin: 10px;
}

.prison-select-center-top {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
  display: flex;
  margin-bottom: 5px;
}

.prison-select-center-bottom {
  display: flex;
  flex: 1;
  min-height: 0;
  height: 100%;
}
</style>
