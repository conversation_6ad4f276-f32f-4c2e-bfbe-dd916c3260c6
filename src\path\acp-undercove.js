import { acpCom,temCom,pamCom} from './base'
export default {
    //  管教业务-信息员管理
    // 领导审批-管教业务-信息员管理
    acp_undercover_approve: acpCom + '/acp/gj/undercover/approve',
    // 创建实战平台-管教业务-信息员管理
    acp_undercover_create: acpCom + '/acp/gj/undercover/create',
    // 删除实战平台-管教业务-信息员管理
    acp_undercover_delete: acpCom + '/acp/gj/undercover/delete',
    // 获取信息员管理详情
    acp_undercover_get: acpCom + '/acp/gj/undercover/get',
    // 获取信息员记录列表信息
    acp_undercover_getUndercoverRespVOByJgrybm: acpCom + '/acp/gj/undercover/getUndercoverRespVOByJgrybm',
    // 更新实战平台-管教业务-信息员管理
    acp_undercover_update: acpCom + '/acp/gj/undercover/update',
    // 管教业务-信息员管理-登记状态和流程修改
    acp_undercover_updateProcessStatus: acpCom + '/acp/gj/undercover/updateProcessStatus',

    // 撤销
    // 创建实战平台-管教业务-信息员撤销
    acp_undercoverCancel_create: acpCom + '/acp/gj/undercoverCancel/create',
    // 获得实战平台-管教业务-耳目撤销
    acp_undercoverCancel_get: acpCom + '/acp/gj/undercoverCancel/get',
    // 领导审批-管教业务-信息员撤销
    acp_undercoverCancel_approve: acpCom + '/acp/gj/undercoverCancel/approve',
    // 获取个人已结束的谈话教育列表
    tem_getPersonTaskRecordListByJgrybm: temCom + '/tem/talk/talkRecord/getPersonTaskRecordListByJgrybm',

    //  管教业务-禁闭
    // 创建实战平台-管教业务-禁闭登记
    tem_getGjconfinementReg_create: acpCom + '/acp/gj/confinementReg/create',
    // 获得实战平台-管教业务-禁闭监室选择
    tem_getConfinementRoomList: acpCom + '/acp/gj/confinementReg/getConfinementRoomList',
    // 获得实战平台-管教业务-禁闭详情包含登记，呈批，解除
    tem_gj_getDetail: acpCom + '/acp/gj/confinementRemove/getDetail',
    // 获得实战平台-管教业务-当前人员禁闭登记列表
    tem_gj_getJqryConfinementHistory: acpCom + '/acp/gj/confinementReg/getJqryConfinementHistory',
    // 获得实战平台-管教业务-禁闭登记不允许状态
    tem_gj_getNotAllowedRecords: acpCom + '/acp/gj/confinementReg/getNotAllowedRecords',
    // 带入带出登记
    tem_gj_saveInOutRecords: acpCom + '/acp/gj/confinementReg/saveInOutRecords',
    // 创建实战平台-管教业务-延长禁闭呈批
    tem_gj_confinementExtend_create: acpCom + '/acp/gj/confinementExtend/create',
    // 创建实战平台-管教业务-解除禁闭呈批
    tem_gj_confinementRemove_create: acpCom + '/acp/gj/confinementRemove/create',
    // 创建实战平台-管教业务-提前解除登记
    tem_gj_confinementRemove_saveInOutRecords: acpCom + '/acp/gj/confinementRemove/saveInOutRecords',
    // 获得实战平台-管教业务-解除禁闭登记页面初始化(解除原因)
    tem_gj_confinementRemove_initRemoveReason: acpCom + '/acp/gj/confinementRemove/initRemoveReason',
    // 领导审批(呈批)
    tem_gj_confinementReg_approve: acpCom + '/acp/gj/confinementReg/approve',
    // 领导审批(延长)
    tem_gj_confinementExtend_approve: acpCom + '/acp/gj/confinementExtend/approve',
    // 领导审批(解除)
    tem_gj_confinementRemove_approve: acpCom + '/acp/gj/confinementRemove/approve',

    // 监室值班设置
    // 监室人员列表
    acp_duty_prisonerList: pamCom + '/pam/duty/prisonerList',
    // 获取排班记录
    acp_duty_dutyRecords: pamCom + '/pam/duty/dutyRecords',
    // 创建监所事务管理-值班组
    acp_duty_group_create: pamCom + '/pam/duty/group/create',
    // 删除监所事务管理-值班组
    acp_duty_group_delete: pamCom + '/pam/duty/group/delete',
    // 创建监室值班
    acp_duty_create: pamCom + '/pam/duty/create',
    // 更新监室自动排班配置
    acp_duty_updateAutoConfig: pamCom + '/pam/duty/updateAutoConfig',
    // 获取监室自动排班配置
    acp_duty_getAutoConfig: pamCom + '/pam/duty/getAutoShiftConfig',
    // 监室自动排班
    acp_duty_autoShift: pamCom + '/pam/duty/autoShift',
    // 获取监室值班规则配置
    acp_duty_config_get: pamCom + '/pam/duty/config/get',
    // 更新监室值班规则配置
    acp_duty_config_create: pamCom + '/pam/duty/config/create',
    // 获取班次信息
    acp_duty_shift_getShift: pamCom + '/pam/duty/shift/getShift',
    // 监室值班-班次管理-保存编辑
    acp_duty_shift_update: pamCom + '/pam/duty/shift/shiftManageSave',
    // 获取值班签到记录
    acp_duty_getSignRecord: pamCom + '/pam/duty/getSignRecord',

    // 监室值日管理
    // 监室值日-班次管理-保存编辑
    acp_day_duty_shift_shiftManageSave: pamCom + '/pam/day/duty/shift/shiftManageSave',
    // 获取排班记录
    acp_duty_duty_dutyRecords: pamCom + '/pam/day/duty/dutyRecords',
    // 获取班次信息
    acp_day_duty_shift_getShift: pamCom + '/pam/day/duty/shift/getShift',
    // 创建监室值日
    acp_day_duty_create: pamCom + '/pam/day/duty/create',
    // 监室人员列表
    acp_day_duty_prisonerList: pamCom + '/pam/day/duty/prisonerList',
    // 新增监所事务管理-值班组
    acp_day_duty_group_create: pamCom + '/pam/day/duty/group/create',
    // 删除监所事务管理-值班组
    acp_day_duty_group_delete: pamCom + '/pam/day/duty/group/delete',

    // 监室调整
    // 获得实战平台-监管管理-获取监室列表接口
     acp_areaPrisonRoom_page: acpCom + '/base/pm/areaPrisonRoom/page',
    //  人员选择  可根据  jsh监室号，过滤该监室人员
     acp_areaPrisonRoom_getPrisonerSelectCompoment: acpCom + '/base/pm/prisoner/getPrisonerSelectCompoment',
    //  获取监区列表
     acp_areaPrisonRoom_getAreaListByOrgCodeAndAreaType: acpCom + ' /base/area/getAreaListByOrgCodeAndAreaType',
    //  获取监室调整记录
     acp_prisonRoomChange_getPrisonRoomChangeByJgrybm: acpCom + '/acp/gj/prisonRoomChange/getPrisonRoomChangeByJgrybm',
    // 获得实战平台-管教业务--监室调整
     acp_prisonRoomChange_get: acpCom + '/acp/gj/prisonRoomChange/get',
    // 创建实战平台-管教业务--监室调整
     acp_prisonRoomChange_create: acpCom + '/acp/gj/prisonRoomChange/create',
    // 带入带出登记
     acp_prisonRoomChange_bringIntakeOutReg: acpCom + '/acp/gj/prisonRoomChange/bringIntakeOutReg',
    // 领导审批-管教业务-监室调整
     acp_prisonRoomChange_approve: acpCom + '/acp/gj/prisonRoomChange/approve',
    //批量创建实战平台-管教业务--监室调整
    acp_prisonRoomChange_batchCreate :acpCom + '/acp/gj/prisonRoomChange/batchCreate',
    // 领导审批(批量更新)-管教业务-监室调整
    acp_prisonRoomChange_batchApprove:acpCom + '/acp/gj/prisonRoomChange/batchApprove',
    // 管教业务-监室调整-获取轨迹记录
    acp_prisonRoomChange_getApproveTrack:acpCom + '/acp/gj/prisonRoomChange/getApproveTrack',
}