<template>
    <div>
      <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="150" label-colon>
        <div class="subtit"><Icon type="md-list-box" size="24" color="#2b5fda" />案件信息</div>
        <div class="form">
          <Row>
            <!-- <Col span="8">
              <FormItem v-if="!quickRegistration" label="案件类别"  prop="ajlbdm" :rules="[{ trigger: 'blur,change', message: '案件类别为必填', required: true }]">
                <s-dicgrid v-model="formData.ajlbdm" @change="$refs.formData.validateField('ajlbdm')" :multiple="true" :isSearch="true"  dicName="ZD_AJLB" />
              </FormItem>
              <FormItem v-else label="法律文书号" prop="flwsh" :rules="!quickRegistration?[{ trigger: 'blur', message: '法律文书号为必填', required: true }]:[]">
                <Input type="text" v-model="formData.flwsh" placeholder="请填写"/>
              </FormItem>
            </Col> -->
            <!-- <Col span="8">
              <FormItem label="限制会见案件" :label-width="150" prop="xzhjaj" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '请选择是否为限制会见案件', required: true }]:[]">
                <RadioGroup v-model="formData.xzhjaj">
                  <Radio label="1">是</Radio>
                  <Radio label="0">否</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="是否限制会见" :label-width="150" prop="xzhj">
                <RadioGroup v-model="formData.xzhj">
                  <Radio label="1">是</Radio>
                  <Radio label="0">否</Radio>
                </RadioGroup>
              </FormItem>
            </Col> -->
          </Row>
          <Row>
            <Col span="8">
              <FormItem label="同案编号" prop="tabh">
                <Row>
                  <Col span="10">
                    <Input type="text" v-model="formData.tabh" placeholder="请填写"/>
                  </Col>
                  <Col span="4">
                    <Button style="margin: 0 0px 0 19px" type="primary">同案犯管理</Button>
                  </Col>
                </Row>
              </FormItem>
            </Col>
            <Col span="8">
<!--              <FormItem label="收治凭证类型" prop="sypz" :rules="[{ trigger: 'blur', message: '收治凭证类型为必填', required: true }]">-->
<!--                <Input type="text" v-model="formData.sypz" placeholder="请填写"/>-->
<!--              </FormItem>-->

              <FormItem label="收治凭证类型"  prop="sypz" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '收治凭证类型为必填', required: true }]:[]">
                <s-dicgrid v-model="formData.sypz" @change="$refs.formData.validateField('sypz')" :isSearch="true" :multiple="true" dicName="ZD_SYYWSYPZLX" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="收治凭证文书" prop="sypzwsdzUrl" style="width: 100%;">
                <file-upload
                  :defaultList="sypzwsdzUrl"
                  :serviceMark="serviceMark"
                  :bucketName="bucketName"
                  :beforeUpload="beforeUpload"
                  v-if="showFile"
                  @fileSuccess="fileSuccessFile"
                  @fileRemove="fileRemoveFile"
                  @fileComplete="fileCompleteFile" />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="8">
              <FormItem label="回执法律文书号" prop="hzwsh" >
                <Input type="text" v-model="formData.hzwsh" placeholder="请填写"/>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="入所原因"  prop="rsyy" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '入所原因为必填', required: true }]:[]">
                <s-dicgrid v-model="formData.rsyy" @change="$refs.formData.validateField('rsyy')" :isSearch="true"  dicName="ZD_KSS_RSYY" />
              </FormItem>
            </Col>
            <!-- <Col span="8">
              <FormItem label="入所时所处诉讼阶段"  prop="sshj" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '入所时所处诉讼阶段为必填', required: true }]:[]">
                <s-dicgrid v-model="formData.sshj" @change="$refs.formData.validateField('sshj')" :isSearch="true"  dicName="ZD_SSJD" />
              </FormItem>

            </Col> -->
          <!-- </Row>
          <Row> -->
            <!-- <Col span="8">
              <FormItem label="强制措施类型"  prop="qzcslx" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '强制措施类型为必填', required: true }]:[]">
                <s-dicgrid v-model="formData.qzcslx" @change="$refs.formData.validateField('qzcslx')" :isSearch="true"  dicName="ZD_SYYWQZCS" />
              </FormItem>
            </Col> -->
            <Col span="8">

              <FormItem label="送治单位类型"  prop="syjglx" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '送治单位类型为必填', required: true }]:[]">
                <s-dicgrid v-model="formData.syjglx" @change="$refs.formData.validateField('syjglx')" :isSearch="true"  dicName="ZD_SYYWSYDWLX" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="送治单位名称"  prop="syjgmc" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '送治单位名称为必填', required: true }]:[]">
                <s-dicgrid v-model="formData.syjgmc" @change="$refs.formData.validateField('syjgmc')" :isSearch="true"  dicName="ZD_BADW_GAJG" />
              </FormItem>
            </Col>

          <!-- </Row>
          <Row> -->
            <Col span="8">
              <FormItem label="送治人" prop="syr1" :rules="!quickRegistration?[{ trigger: 'blur', message: '送治人为必填', required: true }]:[]">
                <Input type="text" v-model="formData.syr1" placeholder="请填写"/>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="送治人固话" prop="syrgh1" :rules="!quickRegistration?[{ trigger: 'blur', message: '送治人固话为必填', required: true }]:[]">
                <Input type="text" v-model="formData.syrgh1" placeholder="请填写"/>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="送治人手机" prop="syrsj1" :rules="!quickRegistration?[{ trigger: 'blur', message: '送治人手机为必填', required: true }]:[]">
                <Input type="text" v-model="formData.syrsj1" placeholder="请填写"/>
              </FormItem>
            </Col>
          <!-- </Row>
          <Row> -->
            <Col span="8">
              <FormItem label="案事件编号" prop="ajbh" :rules="!quickRegistration?[{ trigger: 'blur', message: '案事件编号为必填', required: true }]:[]">
                <Input type="text" v-model="formData.ajbh" placeholder="请填写"/>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="办案单位"  prop="badw" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '办案单位为必填', required: true }]:[]">
                <s-dicgrid v-model="formData.badw" @change="$refs.formData.validateField('badw')" :isSearch="true"  dicName="ZD_BADW_GAJG" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="办案单位类型"  prop="badwlx" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '办案单位类型为必填', required: true }]:[]">
                <s-dicgrid v-model="formData.badwlx" @change="$refs.formData.validateField('badwlx')" :isSearch="true"  dicName="ZD_KSS_BADWLX" />
              </FormItem>
            </Col>
          <!-- </Row>
          <Row> -->
            <!-- <Col span="8">
              <FormItem label="办案环节"  prop="bahj" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '办案环节为必填', required: true }]:[]">
                <s-dicgrid v-model="formData.bahj" @change="$refs.formData.validateField('bahj')" :isSearch="true"  dicName="ZD_KSS_BAHJ" />
              </FormItem>
            </Col> -->
            <!-- <Col span="8">
              <FormItem label="羁治日期" prop="jyrq" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '羁治日期为必填', required: true }]:[]">
                <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="$refs.formData.validateField('jyrq')"  v-model="formData.jyrq" size="small"  placeholder="请选择" style="width: 100%;" />
              </FormItem>
            </Col> -->
            <!-- <Col span="8">
              <FormItem label="拘留日期" prop="jlrq">
                <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="$refs.formData.validateField('jlrq')"  v-model="formData.jlrq" size="small"  placeholder="请选择" style="width: 100%;" />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="8">
              <FormItem label="逮捕日期" prop="dbrq">
                <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="$refs.formData.validateField('dbrq')"  v-model="formData.dbrq" size="small"  placeholder="请选择" style="width: 100%;" />
              </FormItem>
            </Col> -->
            <Col span="8">
              <FormItem label="送治期限" prop="gyqx" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '送治期限为必填', required: true }]:[]">
                <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="$refs.formData.validateField('gyqx')"  v-model="formData.gyqx" size="small"  placeholder="请选择" style="width: 100%;" />
              </FormItem>
            </Col>
            <!-- <Col span="8" v-if="!quickRegistration">
              <FormItem label="入所日期" prop="rssj" :rules="[{ trigger: 'blur,change', message: '入所日期为必填', required: true }]">
                <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="$refs.formData.validateField('rssj')"  v-model="formData.rssj" size="small"  placeholder="请选择" style="width: 100%;" />
              </FormItem>
            </Col> -->
          <!-- </Row>
          <Row> -->
            <Col span="8" v-if="!quickRegistration">
              <FormItem label="法律文书号" prop="flwsh" :rules="!quickRegistration?[{ trigger: 'blur', message: '法律文书号为必填', required: true }]:[]">
                <Input type="text" v-model="formData.flwsh" placeholder="请填写"/>
              </FormItem>
            </Col>
            <Col span="16">
              <FormItem label="简要案情" prop="jyaq" :rules="!quickRegistration?[{ trigger: 'change', message: '简要案情为必填', required: true }]:[]">
                <Input v-model="formData.jyaq" placeholder="请填写"  type="textarea" :autosize="{minRows: 2,maxRows: 5}"></Input>
              </FormItem>
            </Col>
          </Row>
        </div>
      </Form>
    </div>
</template>

<script>
  import { sImageUploadLocal } from '@/components/upload/image'
  import { fileUpload } from 'sd-minio-upfile'
  export default {
    components:{
      sImageUploadLocal,fileUpload
    },
    props:{
      formData:{
        type: [Array,Object],
        default: {}
      },
      quickRegistration:{
        default: false,
      },
    },
    data(){
      return{
        showFile:false,
        sypzwsdzUrl:[],
        serviceMark: serverConfig.OSS_SERVICE_MARK,
        bucketName: serverConfig.bucketName,
        ruleValidate: {},
      }
    },
    methods:{
      beforeUpload(){},
      fileSuccessFile(){},
      fileRemoveFile(){},
      fileCompleteFile(data,index){
        if(data && data.length>0){
          console.log(data,'sypzwsdz')
          this.$set(this.formData,'sypzwsdz',JSON.stringify(data))
        }
      },
      validate() {
        return new Promise((resolve) => {
          this.$refs.formData.validate(valid => {
            resolve(valid); // valid 是 boolean，表示是否验证通过
          });
        });
      },
      updataSypzwsdzUrl(sypzwsdz){
        if(sypzwsdz){
          this.formData.sypzwsdz = sypzwsdz
          if(this.formData.sypzwsdz){
            this.sypzwsdzUrl = JSON.parse(this.formData.sypzwsdz)
          }
        }
        this.showFile = true
      }
    },
    mounted() {

    }
  }
</script>

<style scoped>
  .bsp-imgminio-container{
    width:100% !important;
  }
</style>
