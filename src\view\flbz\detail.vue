<template>
  <div class="detail-wrap">
	<div class="detail-content">
		<div class="form-container" style="padding: 10px; display: flex; flex-direction: column; flex: 1">
			<Form ref="addModelForm" :model="modelForm" :label-width="190" :label-colon="true">
				<div class="flbz-container">
					<div class="flbz-cont-left">
					<div class="flbz-topLeft">
						<!-- 使用新的人员选择组件 -->
						<personnel-selector v-model="modelForm.jgrybm" title="被拘留人员" placeholder="点击选择在押人员或扫码识别" :show-case-info="true"
							:enable-scan="true" :show-scan-tip="true" @change="handlePersonnelChange" :mode="'info'"/>
					</div>
					</div>
					<div class="flbz-cont-right">
						<div class="list-title">申请信息</div>
						<div class="flbz-sq" style="margin-bottom: 10px;">
							<el-descriptions class="margin-top" :column="2" size="small" border>
								<el-descriptions-item label="申请时间" :labelStyle="{ width: '8em' }">
									{{ modelForm.applyTime }}
								</el-descriptions-item>
								<el-descriptions-item label="申请帮助事项" :labelStyle="{ width: '8em' }">
									{{ modelForm.assistanceMatter }}
								</el-descriptions-item>
								<el-descriptions-item label="是否需要办案机关许可">
									{{ modelForm.isNeedCaseUnitAllowed && modelForm.isNeedCaseUnitAllowed == '0' ? '不需要' : '需要' }}
								</el-descriptions-item>
								<el-descriptions-item label="办案机关类型">
									{{ modelForm.caseUnitType }}
								</el-descriptions-item>
								<el-descriptions-item label="办案机关反馈">
									{{ modelForm.caseUnitFeedback }}
								</el-descriptions-item>
								<el-descriptions-item label="决定时间">
									{{ modelForm.decisionTime }}
								</el-descriptions-item>
								<el-descriptions-item label="登记人">
									{{ userName }}
								</el-descriptions-item>
								<el-descriptions-item label="登记时间">
									{{ modelForm.applyTime }}
								</el-descriptions-item>
							</el-descriptions>
						</div>
						<div class="list-title">值班律师信息/会见信息</div>
						<div class="flbz-ls">
							<div class="ls-info">
								<p class="ls-title">律师</p>
								<div style="padding: 12px; padding-top: 15px;">
									<Row>
										<Col span="12">
											<FormItem label="姓名" prop="lawyerName" style="width: 100%" :label-width="130">
												<Input type="text" v-model="modelForm.lawyerName"  placeholder="请填写" readonly />
											</FormItem>
										</Col>
										<Col span="12">
											<FormItem label="身份证号码" prop="lawyerIdNumber" style="width: 100%" :label-width="130">
												<Input type="text" v-model="modelForm.lawyerIdNumber" placeholder="请填写" readonly />
											</FormItem>
										</Col>
										<Col span="12">
											<FormItem label="执业证号码" prop="lawyerPracticeLicenseNumber" style="width: 100%" :label-width="130">
												<Input type="text" v-model="modelForm.lawyerPracticeLicenseNumber" placeholder="请填写" readonly />
											</FormItem>
										</Col>
										<Col span="12">
											<FormItem label="律师单位" prop="lawyerFirm" style="width: 100%" :label-width="130">
												<Input type="text" v-model="modelForm.lawyerFirm" placeholder="请填写" readonly />
											</FormItem>
										</Col>
									</Row>
								</div>
							</div>
						</div>
						<div class="flbz-lsInfo" style="margin-bottom: 30px;">
							<el-descriptions class="margin-top" :column="2" size="small" border>
								<el-descriptions-item label="会见方式" :labelStyle="{ width: '15em' }">
									{{ modelForm.meetingMethodName }}
								</el-descriptions-item>
								<el-descriptions-item label="会见室" :labelStyle="{ width: '15em' }">
									{{ modelForm.meetingRoomName }}
								</el-descriptions-item>
								<el-descriptions-item label="会见开始时间">
									{{ modelForm.meetingStartTime }}
								</el-descriptions-item>
								<el-descriptions-item label="会见结束时间">
									{{ modelForm.meetingEndTime }}
								</el-descriptions-item>
								<el-descriptions-item label="是否特殊会见">
									{{ modelForm.isSpecialMeeting == '0' ? '否' : '是' }}
								</el-descriptions-item>
								<el-descriptions-item label="带出监室时间">
									{{ modelForm.escortingTime }}
								</el-descriptions-item>
								<el-descriptions-item label="带出安检结果">
									{{ modelForm.inspectionResultName }}
								</el-descriptions-item>
								<!-- <el-descriptions-item label="带出异常情况说明">
									{{ modelForm.decisionTime }}
								</el-descriptions-item> -->
								<el-descriptions-item label="带回监室时间">
									{{ modelForm.returnTime }}
								</el-descriptions-item>
								<el-descriptions-item label="带回安检结果">
									{{ modelForm.returnInspectionResultName }}
								</el-descriptions-item>
								<!-- <el-descriptions-item label="带回异常情况登记">
									{{ modelForm.caseUnitFeedback }}
								</el-descriptions-item> -->
								<el-descriptions-item label="异常情况登记">
									{{ modelForm.abnormalSituations }}
								</el-descriptions-item>
								<el-descriptions-item label="登记时间">
									{{ modelForm.applyTime }}
								</el-descriptions-item>
								<el-descriptions-item :span="2" label="监督民警">
									{{ supervisingPoliceNames }}
								</el-descriptions-item>
								<el-descriptions-item label="律师是否有违规">
									{{ modelForm.isLawyerViolation == '0' ? '否' : '是' }}
								</el-descriptions-item>
								<el-descriptions-item label="律师违规情况">
									{{ modelForm.lawyerViolationDetails }}
								</el-descriptions-item>
								<el-descriptions-item label="律师是否告知在押人员异常行为">
									{{ modelForm.isLawyerInformAbnormalBehav == '0' ? '否' : '是' }}
								</el-descriptions-item>
								<el-descriptions-item label="在押人员异常行为情况">
									{{ modelForm.abnormalBehaviorDetails }}
								</el-descriptions-item>
							</el-descriptions>
						</div>
					</div>
				</div>
			</Form>
		</div>
	</div>
	<div class='bsp-base-fotter'>
      <Button @click='goBack'>返 回</Button>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
export default {
	props: {
		saveType: {
			type: String,
			default: ''
		},
		curId: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			modelForm: {},
			userName: this.$store.state.common.userName || '',
		}
	},
	computed: {
		supervisingPoliceNames() {
			if (!this.modelForm.supervisingPoliceList || !Array.isArray(this.modelForm.supervisingPoliceList)) {
			return ''
			}
			return this.modelForm.supervisingPoliceList.map(item => item.xm || '').filter(name => name).join('，')
		}
	},
	methods: {
		...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
		goBack() {
			this.$emit('toback')
		},
		getModelForm(id) {
			this.$store.dispatch('authGetRequest',{
				url: this.$path.acp_legalAssistance_get,
				params: {
					id
				}
			}).then(res => {
				if(res.success) {
					console.log(res,'res')
					this.modelForm = res.data
					console.log(this.detail,'detail')
				} else {
					this.$Modal.error({
						title: '温馨提示',
						content: res.msg || '接口操作失败!'
					})
				}
			})
		},
		handlePersonnelChange(personnelData, jgrybm) {
            console.log('选择的人员:', personnelData, jgrybm)
            // if (personnelData && jgrybm) {
            //     // this.modelForm.prison = personnelData
            //     this.modelForm.jgrybm = jgrybm
            //     this.modelForm.jgryxm = personnelData.xm
            //     this.$Message.success(`已选择人员: ${personnelData.xm || '未知'}`)
            // } else {
            //     // 清空人员信息
            //     this.modelForm.prison = null
            //     this.modelForm.jgrybm = ''
            //     this.modelForm.jgryxm = ''
            // }
        },
	},
	created() {
		this.getModelForm(this.curId)
	}
}
</script>

<style lang="less" scoped>
/deep/.list-title {
  line-height: 2.4;
  background: #f2f5fc;
  font-size: 14px;
  padding: 0 0.8em;
  font-weight: bold;
  border: 1px solid #CEE0F0;
  border-bottom: unset;
  margin-bottom: 10px;
}
.flbz-wrap{
	width: 100%;
}
.flbz-content{
	width: 100%;
}
.flbz-wrap{
	padding-top: 20px;
}
.required-field {
  color: red;
  margin-right: 4px;
}
/deep/.ivu-table-cell-slot {
  display: flex !important;
}
.flbz-container{
	width: 100%;
	display: flex;
	.flbz-cont-left{
		width: 23%;
	}
	.flbz-cont-right{
		flex: 1;
		padding-left: 20px;
		border-left: 1px solid #dcdee2;
		.flbz-ls{
			width: 100%;
			margin-bottom: 20px;
			.ls-info {
				width: 50%;
				border: 1px solid #ccc;
				border-radius: 4px;
				.ls-title{
					line-height: 36px;
					font-size: 16px;
					font-weight: bold;
					color: #000;
					text-align: center;
					background: #ccc;
				}
			}
		}
	}
}
</style>