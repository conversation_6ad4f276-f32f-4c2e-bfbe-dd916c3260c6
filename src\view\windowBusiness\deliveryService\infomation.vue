<!--详情  -->
<template>
    <div class="fm-content-info">
        <Form ref="formData" :model="formData" inline >
        <div class="fm-content-box">
            <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />基础信息</p>
            <Row>
                <Col span="3" class="col-title"><span>顾送日期</span></Col>
                <Col span="9"><span>{{formData.deliveryDate ? formData.deliveryDate : '-'}}</span></Col>
                <Col span="3"><span>顾送编号</span></Col>
                <Col span="9"><span>{{formData.deliveryNo ? formData.deliveryNo: '-'}}</span></Col>
            </Row>
            <Row>
                <Col span="3" class="col-title"><span>登记时间</span></Col>
                <Col span="9"><span>{{formData.addTime ? formData.addTime : '-'}}</span></Col>
                <Col span="3"><span>办理人</span></Col>
                <Col span="9"><span>{{formData.addUserName ? formData.addUserName : '-'}}</span></Col>
            </Row>
            <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />顾送人</p>
            <Row>
                <Col span="3"><span>姓名</span></Col>
                <Col span="9"><span>{{formData.senderName ? formData.senderName : '-'}}</span></Col>
                <Col span="3"><span>性别</span></Col>
                <Col span="9"><span>{{formData.genderName ? formData.genderName : '-'}}</span></Col>
            </Row>
            <Row>
                <Col span="3"><span>证件类型</span></Col>
                <Col span="9"><span>{{formData.idTypeName ? formData.idTypeName : '-'}}</span></Col>
                <Col span="3"><span>证件号码</span></Col>
                <Col span="9"><span>{{formData.idNumber ? formData.idNumber : '-'}}</span></Col>
            </Row>
            <Row>
                <Col span="3"><span>与被监管人关系</span></Col>
                <Col span="9"><span>{{formData.relationshipName ? formData.relationshipName : '-'}}</span></Col>
                <Col span="3"><span>联系方式</span></Col>
                <Col span="9"><span>{{formData.contact ? formData.contact : '-'}}</span></Col>
            </Row>
            <Row>
                <Col span="3"><span>户籍地址</span></Col>
                <Col span="21"><span>{{formData.householdAddress ? formData.householdAddress : '-'}}</span></Col>
            </Row>
            <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />顾送物品</p>
            <Row>
                <Col span="24" style="padding-right: 0px !important;">
                    <Table :columns="columns" :data="formData.goodsList" border></Table>
                </Col>
            </Row>
            <Row>
                <Col span="3"><span>物品照片</span></Col>
                <Col span="21" style="width: 100%;">
                    <div ref="viewerContainer" style="display: flex;">
                        <div
                          v-if="formData.goodsPhotoPath"
                          style="
                          width: 96px;
                          height: 96px;
                          border: 1px dashed #dcdee2;
                          border-radius: 6px;
                          text-align: center;
                          line-height: 116px;
                          position: relative;
                          margin: 5px;
                        "
                        v-for="(item,index) in photoList" :key="index" 
                        >
                        <!-- <Icon class="ImageUrlDe" @click="preview(index)" size="20" type="md-eye" /> -->
                        <Icon class="ImageUrlDl" @click="downLoad(photoList,item,index)" size="20" type="md-download" />
                        <img
                            :src="item"
                            style="width: 96px; height: 96px; margin-right: 4px;"
                        />
                        </div>
                    </div>
                </Col>
            </Row>
            <Row>
                <Col span="3"><span>物品信息</span></Col>
                <Col span="21"><span>{{formData.goodsInfo}}</span></Col>
            </Row>
            <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />签收信息</p>
            <Row>
                <Col span="3"><span>签收状态</span></Col>
                <Col span="21"><span>{{formData.statusName ? formData.statusName : '-'}}</span></Col>
                <Col span="3"><span>签收时间</span></Col>
                <Col span="21"><span>{{formData.receiptTime ? formData.receiptTime : '-'}}</span></Col>
            </Row>
            <Row>
                <Col span="3"><span>签名</span></Col>
                <!-- <Col span="21"><span>{{formData.signature}}</span></Col> -->
                <div v-if="formData.signature" style="display: flex;">
                  <img :src="formData.signature" alt="" style="width: 200px; height: 100px; margin: 5px; border: 1px solid #ccc;">
                </div>
                <span v-else>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-</span>
            </Row>
            <Row>
                <Col span="3"><span>拒签原因</span></Col>
                <Col span="21"><span>{{formData.rejectionReason ? formData.rejectionReason : '-'}}</span></Col>
            </Row>
        </div>

        </Form>
        
    </div>
</template>
<script>
import Viewer from 'viewerjs';
import 'viewerjs/dist/viewer.css';
export default {
  data(){
    return{
    //   formData:{}
    columns: 
            [
              {
              type: 'index',
              width: 80,
              align: 'center',
              title: '序号'
              },
              {
              title: '*物品名称',
              key: 'goodsName',
              align: 'center',
              // width: 230,
              },
              {
              title: '*物品数量',
              key: 'goodsQuantity',
              align: 'center',
              // width: 200,
              },
              {
              title: '*单位',
              key: 'unitName',
              align: 'center',
              // width: 200,
              },
              {
              title: '备注',
              key: 'remark',
              align: 'center',
              // width: 300,
              }
          ],
          viewerInstance: null,
    }
  },
  props: {
    formData: {
        type: Object,
        default: {}
    }
  },
  computed: {
    photoList() {
      if (!this.formData.goodsPhotoPath) return [];
      return this.formData.goodsPhotoPath.split(',').map(item => item.trim()).filter(item => item);
    }
  },
  methods:{
    clearUrl(data,item,index) {
      console.log(data,item,index,'item,index')
      data.splice(index,1)
      console.log(this.formData,'qcwpdj');
    },
    preview(index) {
      if (this.viewerInstance) {
        this.viewerInstance.show(index);
      }
    },
    // downLoad(data,item,index) {
    //     console.log(data,item,index,'data,item,index');
    //     const link = document.createElement('a');
    //     link.href = item;

    //     // 从图片地址中提取文件名，或者自定义文件名
    //     const fileName = item.substring(item.lastIndexOf('/') + 1) || `image_${index}.jpg`;
    //     link.download = fileName;

    //     document.body.appendChild(link);
    //     link.click();
    //     document.body.removeChild(link);
    // }
    async downLoad(data, item, index) {
        try {
            console.log(data, item, index, 'data,item,index');

            // 1. 通过 fetch 请求图片资源
            const response = await fetch(item, { mode: 'cors' });
            if (!response.ok) throw new Error('图片下载失败');

            // 2. 转成 Blob 对象
            const blob = await response.blob();

            // 3. 创建 Blob URL
            const url = window.URL.createObjectURL(blob);

            // 4. 创建隐藏 a 标签触发下载
            const link = document.createElement('a');
            link.href = url;

            // 提取文件名
            const fileName = item.substring(item.lastIndexOf('/') + 1) || `image_${index}.jpg`;
            link.download = fileName;

            document.body.appendChild(link);
            link.click();

            // 5. 清理
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载失败:', error);
            alert('图片下载失败，请稍后重试');
        }
    },
    
  },
  created() {
    console.log(this.formData,'formData');
  },
  mounted() {
    if (this.$refs.viewerContainer) {
      this.viewerInstance = new Viewer(this.$refs.viewerContainer, {
        toolbar: true,
        navbar: false,
        scalable: true,
        fullscreen: false,
        title: false,
        viewed: () => {
          // 弹窗打开后，聚焦关闭按钮，避免焦点在隐藏元素
          const closeBtn = document.querySelector('.viewer-button.viewer-close');
          if (closeBtn) closeBtn.focus();
        }
      });
    }
  },
  beforeDestroy() {
    // 销毁 viewer 实例，防止内存泄漏
    if (this.viewerInstance) {
      this.viewerInstance.destroy();
      this.viewerInstance = null;
    }
  },
}
</script>

<style lang="less" scoped>
/deep/.ivu-table-header thead tr th{
    background: #eff6ff !important;
}
.ImageUrlDe{
   position: absolute;
   right: 25px;
   top: 0;
   color: red;
   cursor: pointer;
}
.ImageUrlDl{
   position: absolute;
   right: 5px;
   top: 0;
   color: rgb(90, 161, 243);
   cursor: pointer;
}
</style>