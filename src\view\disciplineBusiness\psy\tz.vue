<template>
    <div class="safety-checks-container">
        <div class="table-container" v-if="tableContainer">
            <s-DataGrid ref="grid" funcMark="xlcp-cptz" :customFunc="true" :params="params">
                <template slot="customRowFunc" slot-scope="{ func, row, index }">
                    <Button type="primary"
                        v-if="func.includes(globalAppCode + ':xlcp-cptz:add') && row.filling_status != 1"
                        @click.native="handleEdit(index, row)">填写</Button>
                    <Button v-if="func.includes(globalAppCode + ':xlcp-cptz:detail') && row.filling_status == 1"
                        @click.native="handleDetail(index, row)" style="margin-left: 10px;">详情</Button>
                </template>
            </s-DataGrid>
        </div>

        <div class="add-template" v-if="AddFormContainer">
            <div style="width: 100%;height: 100%;">
                <!-- 答题答题模式 -->
                <div class="preview-page" v-if="normalPreview">
                    <div class="preview-detail-container">
                        <previewDetail :id="tableId" />
                    </div>
                    <div class="topic-container">
                        <tablePreviewCommon :id="tableId" :selectedRadio="!showSubmit ? selectedRadio : {}"
                            :selectedCheckbox="!showSubmit ? selectedCheckbox : {}"
                            :selectedSimple="!showSubmit ? selectedSimple : {}"
                            :previewAnswerInfo="!showSubmit ? previewAnswerInfo : {}" :justPreview="justPreview">
                            <!-- <template v-slot:changeBtnType>
                                <btnChange @handleChangeBtn="handleChangeBtn" />
                            </template> -->
                        </tablePreviewCommon>
                    </div>
                    <div class="bsp-base-fotter">
                        <Button @click="handleClosePriview" style="margin-right: 10px;">取消</Button>
                        <!-- <Button type="primary" @click="handleSubmit('form')">确认</Button> -->
                    </div>
                </div>
                <!-- 极简模式预览 -->
                <div v-if="simplePerview">
                    <div class="preview-detail-container">
                        <previewDetail :id="tableId" />
                        <tablePreviewSimple :id="tableId" :selectedRadio="!showSubmit ? selectedRadio : {}"
                            :selectedCheckbox="!showSubmit ? selectedCheckbox : {}"
                            :selectedSimple="!showSubmit ? selectedSimple : {}">
                            <template v-slot:changeBtnType>
                                <btnChange changeTip="切换常规答题模式" @handleChangeBtn="handleChangeNormal" />
                            </template>
                        </tablePreviewSimple>
                    </div>
                </div>
            </div>

            <div class="bsp-base-fotter">
                <Button @click="handleReset" style="margin-right: 10px;">{{ !showSubmit ? "返回" : "取消" }}</Button>
                <Button type="primary" @click="handleSubmit" v-if="showSubmit">确认</Button>
                <!-- <span>{{ tipMsg }}</span> -->
                <!-- <span class="tip-Msg" :title="messageError">{{ messageError }}</span> -->
            </div>
        </div>
        <div v-if="detailContainer" class="detail-container">

            <div class="dtk-container">
                <div class="left-img">
                    <div class="person-prison">
                        <h2 class="person-name">{{ personInfo.jgryxm }}</h2>
                        <span class="prison-name">{{ personInfo.roomName }}</span>
                    </div>

                </div>
                <div class="right-title">
                    <div class="header-plan-name">
                        <h3>{{ timeStatusInfo.planName }}</h3>
                        <span class="plan-code">计划编号{{ timeStatusInfo.planCode }}</span>
                        <span class="plan-type-name">{{ timeStatusInfo.planTypeName }}</span>
                        <span class="filling-status-name">{{ timeStatusInfo.fillingStatusName }}</span>
                    </div>

                    <div class="tssj-txzt">
                        <div class="tssj-time">
                            <span>推送时间：</span>
                            <span>{{ timeStatusInfo.pushTime }}</span>
                        </div>
                        <ul>
                            <li>
                                <span>填写时间：</span>
                                <span>{{ timeStatusInfo.fillingTime }}</span>
                            </li>
                            <li>
                                <span>计划创建人：</span>
                                <span>{{ timeStatusInfo.planAddUserName }}</span>
                            </li>
                            <li>
                                <span>量表名称：</span>
                                <span>{{ timeStatusInfo.tableName }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="table-container-list">
                <Table height="300" :columns="dtkColumns" :data="pushRecordAnswerList"></Table>
            </div>
            <!-- <div class="header-container">
                    <div class="tianx-status">
                        {{ timeStatusInfo.fillingStatusName }}
                    </div>
                    <ul class="first-ul">
                        <li>{{ headerInfo.jgryxm }}</li>
                        <li>{{ headerInfo.plan_name }}</li>
                        <li>测试编号：{{ headerInfo.eval_no }}</li>
                        <li>{{ headerInfo.plan_typeName }}</li>
                    </ul>

                    <div class="time-status">
                        <ul class="second-ul">
                            <li>
                                <span>填写平台：</span>
                                <span>{{ }}</span>
                            </li>
                            <li>
                                <span>推送时间：</span>
                                <span>{{ timeStatusInfo.pushTime }}</span>
                            </li>
                            <li>
                                <span>填写状态：</span>
                                <span>{{ timeStatusInfo.fillingStatusName }}</span>
                            </li>
                            <li>
                                <span>填写时间：</span>
                                <span>{{ timeStatusInfo.fillingTime }}</span>
                            </li>
                        </ul>
                    </div>
                </div> -->

            <!-- <div class="push-recordAnswer-List">
                    <ul v-for="item in pushRecordAnswerList" :key="item.id">
                        <li>
                            <Icon type="md-alarm" style="font-size: 22px;" />
                            <div style="margin-left: 20px;">
                                <div>{{
                                    `${item.timeSpentAnswer.hour}:${item.timeSpentAnswer.minute}:${item.timeSpentAnswer.nano}`
                                    }}</div>
                                <div>作答用时</div>
                            </div>

                        </li>
                        <li>
                            <h3>{{ item.score }}</h3>
                            <div>测评得分</div>
                        </li>
                        <li>
                            <h3>{{ item.evalResults }}</h3>
                            <div>测评结果</div>
                        </li>
                        <li>
                            <Button type="primary" @click="handleGetDetails(item)">查看测评详情</Button>
                        </li>
                    </ul>
                </div> -->


            <div class="bsp-base-fotter">
                <Button @click="handleResetDetails" style="margin-right: 10px;">取消</Button>
            </div>
        </div>
    </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions, mapState } from 'vuex'
import previewDetail from './previewDetail.vue';
import tablePreviewCommon from './tablePreviewCommon.vue';
import tablePreviewSimple from './tablePreviewSimple.vue';
import btnChange from './btnChange.vue';
import dayjs from 'dayjs';
export default {
    name: "tz",
    data() {
        return {
            tableContainer: true,
            AddFormContainer: false,
            // modalVisible: false,
            params: {},
            detailContainer: false,
            tableId: "",
            normalPreview: true,
            simplePerview: false,
            dtkContainer: false,
            peopleInfo: {},
            startTime: "",
            personInfo: {},
            pushRecordAnswerList: [],
            selectedCheckbox: {},
            selectedRadio: {},
            selectedSimple: {},
            showSubmit: true,
            previewAnswerInfo: {},
            selectNoSelectedTitle: "",
            selectCheckNoSelectedTitle: "",
            selectSimpleNoSelectedTitle: "",
            messageError: "",
            dtkColumns: [
                {
                    title: '序号',
                    key: 'idx',
                },
                {
                    title: '提交时间',
                    key: 'submitTime',
                },
                {
                    title: '作答用时',
                    key: 'useTime',
                },
                {
                    title: '测评得分',
                    key: 'score',
                },
                {
                    title: '测评结果',
                    key: 'evalResults',
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 120,
                    render: (h, params) => {
                        return h('div', [
                            h('Button', {
                                props: { type: 'primary' },
                                on: { click: () => this.handleGetDetails(params.row) }
                            }, '详情')
                        ])

                    }
                }
            ],
            justPreview: false,
        }
    },

    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        handleClosePriview() {
            this.tableContainer = true;
            this.AddFormContainer = false
        },
        handleChangeBtn() {
            this.normalPreview = false
            this.simplePerview = true
        },
        handleChangeNormal() {
            this.normalPreview = true
            this.simplePerview = false
        },
        handleCommonQuestion(slectedItem) {
            let arrList = Object.entries(slectedItem).map(([key, value]) => ({
                questionId: key,     // 原对象的键 → 新属性 a
                answerOptionCode: Array.isArray(value) ? value.join(',') : value,
                score: 0   // 原对象的值 → 新属性 b
            }))
            return arrList
        },

        getObjectDifference(arrA, arrB) {
            const idSetB = new Set(arrB.map(obj => obj.questionId));
            return arrA.filter(obj => !idSetB.has(obj.questionId));
        },
        // 提交答案
        handleSubmit(name) {
            const { eval_no, id, jgrybm, jgryxm } = this.peopleInfo;
            let radioList = this.handleCommonQuestion(this.RadioSelected)
            let checkBoxList = this.handleCommonQuestion(this.CheckboxSelected)
            let simpleList = this.handleCommonQuestion(this.SimpleSelected)
            

            if (this.normalPreview) {
                let newArrlist = []
                if (this.dxt.numItems) {
                    newArrlist = [...this.dxt.numItems]
                } else if (this.dxt.numItems && this.fxt.numItems) {
                    newArrlist = [...this.dxt.numItems, ...this.fxt.numItems]
                } else if (this.dxt.numItems && this.fxt.numItems && this.jdt.numItems) {
                    newArrlist = [...this.dxt.numItems, ...this.fxt.numItems, ...this.jdt.numItems]
                }
                // let newArrlist = [...this.dxt.numItems, ...this.fxt.numItems, ...this.jdt.numItems]
                const deepCopy = JSON.parse(JSON.stringify(newArrlist));
                //处理没有填写的数据的索引值
                let missingIndices = deepCopy
                    .map((item, index) => ({ item, index }))     // 关联元素与索引[1,6](@ref)
                    .filter(({ item }) => item.selectedRio !== true) // 过滤非 true 值[4,7](@ref)
                    .map(({ index }) => index) // 3. 提取索引
                // 索引值每一项加1
                let NumAddmissingIndices = missingIndices.map(item => { return Number(item) + 1 })
                if (NumAddmissingIndices.length > 0) {
                    const result = NumAddmissingIndices.map(num => `第${num}题`).join(',')
                    this.errorModal({ content: `【以下题目尚未填写，请补充完整：${result}】` })
                    return
                }
            }

            if (this.simplePerview) {
                let selectedTopicList = [...radioList, ...checkBoxList, ...simpleList]
                let newTopicList = [...this.dxt['questions'], ...this.fxt['questions'], ...this.jdt['questions']]
                let filterArr = this.getObjectDifference(newTopicList, selectedTopicList)
                let filterNum = filterArr.map(item => { return item.th })
                let result = filterNum.map(num => `第${num}题`).join(',')
                this.errorModal({ content: `【以下题目尚未填写，请补充完整：${result}】` })
                return
            }

            let params = {
                answerCardDTOS: [...radioList, ...checkBoxList, ...simpleList],
                startTime: this.startTime,
                endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                evalNo: eval_no,
                id,
                jgrybm,
                jgryxm,
                fillingPlatform: 1
            }
            this.authPostRequest({ url: this.$path.evalPlanPushRecordAnswer_create, params }).then(res => {
                if (res.success) {
                    this.$Message.success('填写完成')
                    this.AddFormContainer = false
                    this.tableContainer = true
                } else {                    
                    this.$Message.error(res.message)
                }
            })
        },
        handleReset() {
            this.AddFormContainer = false
            this.tableContainer = true

            // 切换到常规答题模式
            this.normalPreview = true
            this.simplePerview = false
        },
        handleResetDetails() {
            this.detailContainer = false
            this.tableContainer = true
        },
        // 填写按钮
        handleEdit(index, row) {
            this.tableId = row.table_id
            this.AddFormContainer = true
            this.tableContainer = false
            this.showSubmit = true
            this.peopleInfo = row
            this.startTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
        },
        // 详情按钮
        handleDetail(index, row) {
            this.personInfo = row
            console.error(row, '哪一行的数据');

            // this.personInfo = params
            this.tableId = row.table_id
            this.showSubmit = false

            Promise.all([this.authGetRequest({ url: this.$path.evalPlanPushRecord_get, params: { id: row.id } }), this.authPostRequest({ url: this.$path.evalPlanPushRecordAnswer_list, params: { evalNo: row.eval_no } })]).then(res => {
                this.detailContainer = true
                this.tableContainer = false
                this.timeStatusInfo = res[0].data
                console.error(res[0].data);

                this.pushRecordAnswerList = res[1].data
                this.pushRecordAnswerList.forEach((item, index) => {
                    item.idx = index + 1
                    item.useTime = `${item.timeSpentAnswer.hour}:${item.timeSpentAnswer.minute}:${item.timeSpentAnswer.nano}`
                })
            })
        },
        getIntersectionOptimized(arr1, arr2) {
            const map = new Map(arr2.map(obj => [obj.questionId, true]));
            return arr1.filter(obj => map.has(obj.questionId));
        },

        handletablePreviewSimpleAndAnswer(answerId) {
            this.postRequest({ url: this.$path.tablePreviewSimpleAndAnswer, params: { tableId: this.tableId, answerId, sortOrder: 1 } }).then(res => {
                if (res.success) {
                }
            })
        },

        // 量表预览-常规模式-带答案
        handletablePreviewCommonAndAnswer(answerId) {
            this.postRequest({ url: this.$path.tablePreviewCommonAndAnswer, params: { tableId: this.tableId, answerId } }).then(res => {

                if (res.success) {
                    setTimeout(() => {
                        this.selectedRadio = res.data.dxt.questions.reduce((obj, item) => {
                            obj[item.questionId] = item.answer;
                            return obj;
                        }, {})

                        if (res.data.fxt) {
                            this.selectedCheckbox = res.data.fxt.questions.reduce((obj, item) => {
                                obj[item.questionId] = item.answer.split(',');
                                return obj;
                            }, {})
                        }

                        if (res.data.jdt) {
                            this.selectedSimple = res.data.jdt.questions.reduce((obj, item) => {
                                obj[item.questionId] = item.answer;
                                return obj;
                            }, {})
                        }
                    }, 1000)
                    this.AddFormContainer = true;
                    this.detailContainer = false
                }
            })

        },
        handleGetDetails(item) {
            this.previewAnswerInfo = item
            this.justPreview = true
            this.handletablePreviewCommonAndAnswer(item.id)
            this.handletablePreviewSimpleAndAnswer(item.id)

        },
        on_refresh_table() {
            this.$refs.grid.query_grid_data(1)
        }
    },

    components: {
        sDataGrid,
        previewDetail,
        tablePreviewCommon,
        tablePreviewSimple,
        btnChange
    },

    created() {

    },

    computed: {
        ...mapState({
            dxt: state => state.tableTz.dxt,
            fxt: state => state.tableTz.fxt,
            jdt: state => state.tableTz.jdt,
            RadioSelected: state => state.tableTz.RadioSelected,
            CheckboxSelected: state => state.tableTz.CheckboxSelected,
            SimpleSelected: state => state.tableTz.SimpleSelected,
            // preSimRadioSelect: state => state.tableTz.preSimRadioSelect,
            // preSimCheckboxSelect: state => state.tableTz.preSimCheckboxSelect,
            // preSimSimpleSelect: state => state.tableTz.preSimSimpleSelect
        })
    },
    watch: {
        RadioSelected: {
            handler(newVal) {
            },
            immediate: true
        }
    },

}

</script>

<style scoped lang="less">
@import "~@/assets/style/formInfo.css";


.safety-checks-container {
    width: 100%;
    height: 100%;

    .detail-container {
        width: 100%;
        height: 100%;
        background-color: #F1F5F6;

        .header-container {
            padding: 20px 0 15px 30px;
            border-radius: 4px;
            background-color: #fff;
            position: relative;

            .tianx-status {
                position: absolute;
                right: 0;
                top: 0;
                background-color: rgb(75, 121, 2);
                border-radius: 5px 0px 0px 5px;
                font-weight: 700;
                font-size: 17px;
                color: rgb(255, 255, 255);
                padding: 10px 30px;
            }

            ul {
                list-style: none;
            }

            .first-ul {
                display: flex;
                align-items: center;

                li:nth-child(1),
                li:nth-child(2) {
                    font-size: 18px;
                    font-weight: 700;
                }

                li:nth-child(2) {
                    margin-left: 30px;
                }

                li:nth-child(3) {
                    background-color: rgb(242, 242, 242);
                    border: none;
                    border-radius: 5px;
                    box-shadow: none;
                    font-size: 18px;
                    color: rgb(127, 127, 127);
                    margin-left: 20px;
                    padding: 5px 10px;
                }

                li:nth-child(4) {
                    background-color: rgb(250, 205, 145);
                    border: none;
                    border-radius: 5px;
                    box-shadow: none;
                    font-size: 18px;
                    color: rgb(123, 77, 18);
                    margin-left: 5px;
                    padding: 5px 10px;
                }

                // margin: ;
            }

            .second-ul {
                li {
                    span:last-child {
                        margin-left: 5px;
                    }
                }
            }
        }

        .push-recordAnswer-List {
            margin-top: 20px;

            ul {
                list-style: none;
                margin-bottom: 20px;
                padding: 20px 0 15px 30px;
                border-radius: 4px;
                background-color: #fff;
                position: relative;
                display: flex;
                align-items: center;
                text-align: center;

                li {
                    margin-left: 100px;

                    &:first-child {
                        display: flex;
                        align-items: center;
                        margin-left: 0;
                    }
                }
            }
        }
    }
}


.fm-content-wrap-title {
    border-bottom: 1px solid #cee0f0;
    background: #eff6ff;
    line-height: 40px;
    padding-left: 10px;
    font-family: Source Han Sans CN;
    font-weight: 700;
    font-size: 16px;
    color: #00244a;
    margin-bottom: 16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.tip-Msg {
    position: absolute;
    white-space: nowrap;
    /* 强制文本不换行 */
    overflow: hidden;
    /* 隐藏溢出内容 */
    text-overflow: ellipsis;
    /* 超出部分显示省略号 */
    right: 20px;
    top: 16px;
    width: 700px;
    color: #ed4014;
}

.dtk-container {
    background: #FFFFFF;
    box-shadow: 0px 2px 6px 1px rgba(0, 34, 84, 0.12);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #E4EAF0;
    display: flex;
    // justify-content: space-between;
    padding: 16px;
    margin: 16px;

    .left-img {
        width: 320px;
        height: 136px;
        background: url('../../../assets/images/person-info-bg.png') no-repeat center;

        .person-prison {
            margin: 24px 84px 86px 136px;
        }
    }

    .right-title {
        margin-left: 16px;

        .header-plan-name {
            display: flex;
            align-items: center;

            .plan-code {
                width: 180px;
                height: 26px;
                background: #F5F7FA;
                border-radius: 4px 4px 4px 4px;
                padding: 4px;
                font-weight: normal;
                font-size: 14px;
                color: #7F8C9A;
                margin-left: 16px;
            }

            .plan-type-name {
                width: 64px;
                height: 26px;
                background: #FDF4E7;
                border-radius: 4px 4px 4px 4px;
                padding: 4px;
                font-weight: normal;
                font-size: 14px;
                color: #F09115;
                margin-left: 12px;
            }

            .filling-status-name {
                width: 64px;
                height: 26px;
                background: #E4FCEA;
                border-radius: 4px 4px 4px 4px;
                font-weight: normal;
                font-size: 14px;
                color: #00B42A;
                padding: 4px 11px;
                margin-left: 12px;
            }
        }

        .tssj-txzt {
            font-size: 16px;
            color: #8D99A5;

            .tssj-time {
                margin-top: 16px;
            }

            ul {
                margin-top: 16px;
                display: flex;
                justify-content: space-between;

                li {
                    margin-left: 20px;

                    &:first-child {
                        margin-left: 0;
                    }
                }
            }
        }

    }

    ul {
        list-style: none;
    }
}

.table-container-list {
    margin: 0 16px;
}
</style>
