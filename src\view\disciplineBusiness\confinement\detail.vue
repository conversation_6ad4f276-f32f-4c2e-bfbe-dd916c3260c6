<template>
    <!-- 详情页面 -->
    <div class="detail-wrap">
      <div
      class="fm-content-wrap sdInfo"
      style="padding: 2px; border: none; border-top: none"
      >
        <Form
          ref="formData"
          :model="formData"
          :label-colon="true"
          label-position="right"
          :label-width="140"
        >
          <div class="fm-content-box">
            <div class="fm-content-item" v-if="formData && formData.regRespVO">
              <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />禁闭呈批信息</p>
              <Row>
                <Col span="3">禁闭天数</Col>
                <Col span="5">{{formData.regRespVO.confinementDays}}</Col>
                <Col span="3">禁闭监室</Col>
                <Col span="5">{{formData.regRespVO.roomName}}</Col>
                <Col span="3">原监室</Col>
                <Col span="5">{{formData.regRespVO.originalRoomName ? formData.regRespVO.originalRoomName : '-'}}</Col>
              </Row>
              <Row>
                <Col span="3">禁闭原因</Col>
                <Col span="5">{{formData.regRespVO.confinementReasonName}}</Col>
                <Col span="3">详细理由</Col>
                <Col span="5">{{formData.regRespVO.detailedReason}}</Col>
                <Col span="3">备注</Col>
                <Col span="5">{{formData.regRespVO.remarks}}</Col>
              </Row>
              <Row>
                <Col span="3">呈批人</Col>
                <Col span="5">{{formData.regRespVO.addUserName}}</Col>
                <Col span="3">呈批时间</Col>
                <Col span="5">{{formData.regRespVO.addTime}}</Col>
                <Col span="3">是否关联惩罚</Col>
                <Col span="5">{{formData.regRespVO.reporterName}}</Col>
              </Row>
              <Row>
                <Col span="3">惩罚措施</Col>
                <Col span="21">{{formData.regRespVO.isRollcall?'是':'否'}}</Col>
              </Row>
            </div>
            <div class="fm-content-item" v-if="formData && formData.regInOutRecordsRespVOList && formData.regInOutRecordsRespVOList.length">
              <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />禁闭登记信息</p>
              <!-- <div v-for="(item,index) in formData.regInOutRecordsRespVOList" :key="index"> -->
                <!-- {{ item }} -->
                <Row>
                  <Col span="3">禁闭开始日期</Col>
                  <Col span="5">{{formData.reportContent}}</Col>
                  <Col span="3">禁闭结束日期</Col>
                  <Col span="5">{{formData.reportTimeType}}</Col>
                  <Col span="3">带出原监室时间</Col>
                  <Col span="5">{{formData.reportTime}}</Col>
                </Row>
                <Row>
                  <Col span="3">带入禁闭监室时间</Col>
                  <Col span="5">{{formData.regInOutRecordsRespVOList.inoutTime}}</Col>
                  <Col span="3">带出民警</Col>
                  <Col span="5">{{formData.regInOutRecordsRespVOList.inoutPolice}}</Col>
                  <Col span="3">检查结果</Col>
                  <Col span="5">{{formData.regInOutRecordsRespVOList.inspectionResult}}</Col>
                </Row>
                <Row>
                  <Col span="3">检查人</Col>
                  <Col span="5">{{formData.regInOutRecordsRespVOList.inspector}}</Col>
                  <Col span="3">登记人</Col>
                  <Col span="5">{{formData.remark}}</Col>
                  <Col span="3">登记时间</Col>
                  <Col span="5">{{formData.reporterName}}</Col>
                </Row>
              <!-- </div> -->
            </div>
            <div class="fm-content-item" v-if="formData && formData.extendRespVOList && formData.extendRespVOList.length">
              <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />延长禁闭呈批信息</p>
              <Row>
                <Col span="3">延长天数</Col>
                <Col span="5">{{formData.extendRespVOList.extendDay}}</Col>
                <Col span="3">禁闭结束日期</Col>
                <Col span="5">{{formData.extendRespVOList.reportTimeType}}</Col>
                <Col span="3">审批结果</Col>
                <Col span="5">{{formData.extendRespVOList.approvalResult}}</Col>
              </Row>
              <Row>
                <Col span="3">审批意见</Col>
                <Col span="5">{{formData.extendRespVOList.approvalComments}}</Col>
                <Col span="3">审批人</Col>
                <Col span="5">{{formData.extendRespVOList.approvalAutograph}}</Col>
                <Col span="3">审批时间</Col>
                <Col span="5">{{formData.extendRespVOList.approvalAutographTime}}</Col>
              </Row>
            </div>
            <div class="fm-content-item" v-if="formData && formData.removeRespVO">
              <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />提前解除禁闭呈批信息</p>
              <Row>
                <Col span="3">剩余天数</Col>
                <Col span="5">{{formData.removeRespVO.reportContent}}</Col>
                <Col span="3">提前解除理由</Col>
                <Col span="5">{{formData.removeRespVO.removeReason}}</Col>
                <Col span="3">呈批人</Col>
                <Col span="5">{{formData.removeRespVO.addUserName}}</Col>
              </Row>
              <Row>
                <Col span="3">呈批时间</Col>
                <Col span="5">{{formData.removeRespVO.addTime}}</Col>
                <Col span="3">审批结果</Col>
                <Col span="5">{{formData.removeRespVO.approvalResult}}</Col>
                <Col span="3">审批意见</Col>
                <Col span="5">{{formData.removeRespVO.approvalComments}}</Col>
              </Row>
              <Row>
                <Col span="3">审批人</Col>
                <Col span="5">{{formData.removeRespVO.approvalAutograph}}</Col>
                <Col span="3">审批时间</Col>
                <Col span="5">{{formData.removeRespVO.approvalAutographTime}}</Col>
              </Row>
            </div>
            <div class="fm-content-item" v-if="formData && formData.removeInOutRecordsRespVO">
              <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />禁闭解除信息</p>
              <Row>
                <Col span="3">解除理由</Col>
                <Col span="5">{{formData.removeInOutRecordsRespVO.reportContent}}</Col>
                <Col span="3">执行情况</Col>
                <Col span="5">{{formData.removeInOutRecordsRespVO.reportTimeType}}</Col>
                <Col span="3">登记时间</Col>
                <Col span="5">{{formData.removeInOutRecordsRespVO.reportTime}}</Col>
              </Row>
              <Row>
                <Col span="3">登记人</Col>
                <Col span="21">{{formData.removeInOutRecordsRespVO.isRollcall?'是':'否'}}</Col>
              </Row>
            </div>
          </div>
        </Form>
      </div>
    </div>
</template>

<script>
export default {
    props:{
        formData:Object,
        saveType: String
    },
    watch: {
      formData: {
        handler(value) {
          if(value){

          }
        },
        deep: true,
        immediate: true
      }
    },
    created(){
      console.log(this.formData ,'formData');
      console.log(this.saveType,'type');
    }

}
</script>

<style >
.detail-wrap{
    width: 100%;
    margin: 16px;
}

</style>
<style scoped>
@import "~@/assets/style/formInfo.css";
</style>
<style scoped>
.fm-content-info-title{
    background: #eff6ff;
    line-height: 40px;
    padding-left: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
     font-weight: bold;
     font-size: 16px;
     color: #00244A;
     /* margin-bottom: 16px ; */
     display: flex;
     align-items: center;
     border-bottom: 1px solid #CEE0F0;
     border-top: 1px solid #CEE0F0;
 }
 .fm-content-box{
  /* border: none !important; */
 }
 .fm-content-wrap{
  padding: 0px !important;
  border-left: 1px solid #cee0f0;
  border-right: 1px solid #cee0f0;
}
</style>
