<template>
  <div>
	<div class="">
		<div class="bsp-base-content">
			<div class="prison-room-duty">
				<room-header class="duty-header" ref="roomHeader" @change-room="changeRoom"  @init-room="initRoom" :roomData="roomData"></room-header>
				<div :class="['duty-content', {'history': !currentWeek[0] && currentWeek[1]}]">
					<div :class="['duty-person', ]">
						<!-- {{ dutyTimes }} -->
						<board-person
							class="person-board"
							:times="dutyTimes"
							:list.sync="dutyPersonList"
							@on-group="val => isSort = val"
							@on-group-rule="personCheck"
							@on-drag="personDrag"
							ref="boardPerson"
							:roomData="roomData"
						></board-person>
					</div>
					<div class="duty-arrange">
						<div class="arrange-header">
							<div class="arrange-date">
								<Button @click="changeWeek(1)"><i class="icon arrow-left mr-10"></i>上一周</Button>
								<!-- {{ dutyDate }} -->
								<DatePicker type="date" placeholder="Select date" @on-change="changeDate" v-model="dutyDate" :clearable="false" class="date"></DatePicker>
								<Button  @click="changeWeek(0)">下一周<i class="icon arrow-right ml-10"></i></Button>
								<!-- {{ currentWeek[0] }} -->
								<Button @click="goBackWeek()" v-if="!(currentWeek[0] && currentWeek[1])" class="back-btn">返回本周<i class="icon back ml-10"></i></Button>
							</div>
							<div class="arrange-opt">
								<!-- <Tooltip :max-width="300" placement="top">
									<div class="opt-btn auto-btn">
										<el-switch  v-model="roomLoop" :before-change="triggerLoop"></el-switch>&nbsp;&nbsp;循环自动
									</div>
									<div slot="content">{{roomTip.loop}}</div>
								</Tooltip> -->
								<!-- <Button class="opt" type="" @click="handleClass">班次管理</Button> -->
								<div style="display: flex;">
									<div style="margin-left: 8px;" class="opt actionBtns" type="" @click="handleClass"><p>班次管理</p></div>
									<Tooltip :max-width="300"	placement="left">
										<!-- <Button class="opt" type="" @click="handleAuto" :disabled="!currentWeek[0] && currentWeek[1]">自动排班</Button> -->
										<div style="margin-left: 8px;" class="opt actionBtn" type="" :disabled="!currentWeek[0] && currentWeek[1]">
											<!-- <p>自动排班</p> -->
											<div class="actionBtn-text" @click="autoShift">自动排班</div>
											<el-switch v-model="isEnabled" @change="handleAuto" />
											<div class="vertical-line"></div>
											<div class="img-container">
												<img @click.stop="automaticbedEvent" src="@/assets/images/roomManage/shezhitianchong.svg" alt=""></div>
											</div>
										<div slot="content">{{roomTip.auto}}</div>
									</Tooltip>
								</div>
							</div>
						</div>
						<div class="arrange-content">
							<board-arrange @change-times="changeTimes" class="arrange-board"  ref="boardArrange" :roomData="roomData"></board-arrange>
							<div class="mask" v-show="isSort"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="bsp-base-fotter">
			<Button @click="handleClear" class="save" style="margin-right: 10px;">清空</Button>
			<Button type="primary" @click="handleSubmit" class="save">提交</Button>
		</div>
		<!-- <addForm v-if="showData && saveType=='add' " @close="showData=false" :curId="curId" saveType="add" />
	  	<reportReview v-if="showData && (saveType=='edit' || saveType=='info' || saveType=='extend' || saveType=='relieve' || saveType == 'approve')" :formData="formData"  @close="showData=false" :jgrybm="jgrybm" :curId="curId" :saveType="saveType" :status="status" /> -->
	</div>

	<!-- 班次管理 -->
	<room-shift ref="roomShift" @change-shift="changeShift" :roomData="roomData"></room-shift>

	 <!-- 自动排班配置 -->
	 <Modal
		v-model="automaticBedModal"
		:mask-closable="false"
		:closable="false"
		width="860"
		class-name="automaticTip"
	>
		<div class="flow-modal-title" slot="header">
			<span style="font-size: 17px;">自动排班规则</span>
			<span @click="automaticBedCancel" style="position: absolute; right: 6px;cursor: pointer;">
				<i class="ivu-icon ivu-icon-ios-close"></i>
			</span>
		</div>
		<div class="select-use" style="color: #2b2b2b;">
			<!-- <RadioGroup v-model="vertical" vertical v-for="(e,i) in zdpbList" :key="i">
				<Radio :label="e.code">
					<span>{{ e.name }}</span>
				</Radio>
			</RadioGroup> -->
			<CheckboxGroup v-model="vertical"  v-for="(e,i) in zdpbList" :key="i">
				<Checkbox :label="e.code">
					<span>{{ e.name }}</span>
				</Checkbox>
			</CheckboxGroup>
			<p style="line-height: 35px; position: absolute; bottom: 0; color: #8D99A5;">注：开启自动排班后，后台将在每周开始时，根据自动排班规则，自动安排当周值班，同时根据监室人员调整自动适应安排</p>
		</div>
		<div slot="footer">
			<Button type="primary" @click="automaticBedOk()" class="sure_btn">确 定</Button>
			<Button @click="automaticBedCancel" class="cancle_btn">取 消</Button>
		</div>
	</Modal>

	<!-- 自动排班提示 -->
	<Modal
		v-model="automaticTipModal"
		:mask-closable="false"
		:closable="false"
		width="400"
		class-name="roomDutyTip"
	>
		<div class="flow-modal-title" slot="header">
			<span style="font-size: 17px;">提示</span>
			<span @click="automaticTipCancel" style="position: absolute; right: 6px;cursor: pointer;">
				<i class="ivu-icon ivu-icon-ios-close"></i>
			</span>
		</div>
		<div class="select-use" style="color: #2b2b2b;">
			<!-- <RadioGroup v-model="vertical" vertical v-for="(e,i) in zdpbList" :key="i">
				<Radio :label="e.code">
					<span>{{ e.name }}</span>
				</Radio>
			</RadioGroup> -->
			<!-- <CheckboxGroup v-model="vertical"  v-for="(e,i) in zdpbList" :key="i">
				<Checkbox :label="e.code">
					<span>{{ e.name }}</span>
				</Checkbox>
			</CheckboxGroup> -->
			<p style="font-size: 18px; line-height: 30px;">开启自动排班后，后台将在每周开始时，根据自动排班规则，自动安排当周值班，同时根据监室人员调整自动适应安排</p>
		</div>
		<div slot="footer">
			<Button type="primary" @click="automaticTipOk()" class="sure_btn">确 定</Button>
			<Button @click="automaticTipCancel" class="cancle_btn">取 消</Button>
		</div>
	</Modal>
  </div>
</template>

<script>
import roomHeader from "./roomHeader";
import roomShift from "./roomShift";
import boardPerson from "./boardPerson";
import boardArrange from "./boardArrange";
import {getWeekRange} from "@/util";
export default {
	name: "prisonRoomDutyHome",
	components: {
		roomHeader,
		roomShift,
		boardPerson,
		boardArrange
	},
	data() {
		const roomTip = {
			loop: "开启后本监室若出现人员变动的情况, \n后台将自动为变动人员安排床位",
			auto: "• 值班小组每天切换至下一个班次\n• 新入所、风险人员、病号不参与排班\n• 其他人员随机排班"
		};
		return {
			modalTitle: '',
			saveType: 'add',
			roomId: "",
			roomLoop: false,
			roomTip,
			dutyTimes: {},
			dutyPersonList: [],
			dutyDate: new Date().Format("yyyy-MM-dd"),
			isSort: false,
			shiftModal: false,
			openModal: false,
			automaticBedModal: false,
			zdpbList: [],
			vertical: [],
			automaticTipModal: false,
			isEnabled: 0,
			orgCode: this.$store.state.common.orgCode,
			page: {
				pageNo: 1,
				pageSize: 12,
				roomName: ''
			},
			searchForm:{
				mainAssistantManager:true
			},
			roomData: {},
		}
	},
	computed: {
		currentWeek() {
			let [firstDay, lastDay] = getWeekRange(new Date());
			return [firstDay <= this.dutyDate,  this.dutyDate <= lastDay];
		}

	},
	activated() {
		if (this.openModal) {
		this.openShiftModal();
		}
		this.openModal = false;
	},
	created() {
		this.getRoomData()
		console.log(this.$store.state.common,'common');
		// this.listenShiftChange();
		// this.$store.commit("REGISTER_PAGE_TAB_EVENT", {name: "prisonRoomDuty", event: this.leaveEvent});
	},
	mounted() {
		this.initDefaultParams()
	},
	watch: {
		'roomData.roomCode': {
			handler(newVal) {
				if (newVal) {
					this.roomId = newVal;
					this.getAutoConfig();
				}
			},
			immediate: true
		}
	},
	methods: {
		initDefaultParams() {
			// const defaultRoomId = '1100001130100030001'; // 你的默认roomId
			const defaultRoomId = this.roomId
			const [firstDay, lastDay] = getWeekRange(new Date());
			
			// 初始化dutyParam
			this.dutyParam = {
			roomId: defaultRoomId,
			orgCode: this.orgCode,
			dutyDate: new Date().Format("yyyy-MM-dd"),
			dutyWeek: [firstDay, lastDay],
			currentWeek: true // 根据你的业务逻辑设置
			};
			
			// 触发子组件加载数据
			this.$nextTick(() => {
			if (this.$refs.boardArrange) {
				this.$refs.boardArrange.changeRoom(this.dutyParam);
			}
			});
		},
		personCheck(obj) {
			console.log(obj,'objjjjjjjjj');
			let num = obj.type === "add" ? 1 : -1;
			this.$refs.roomHeader.changeGroupNum(num);
			this.$refs.boardArrange.checkSettleList(obj);
		},
		personDrag(dragData) {
			this.$refs.boardArrange.onOutsideDrag(dragData);
		},
		changeRoom(data) {
			this.handleTip(
				async (list) => {
				await this.submitArrange(list);
				this.$refs.roomHeader.initRoom(data);
				},
				() =>  this.$refs.roomHeader.initRoom(data)
			);
		},
		isPersonChange() {
			return  roomDutyPersonCheck(this.roomId).then(res => res.data.change === 1);
		},
		getLoopStatus() {
			roomDutyLoopStatus(this.roomId).then(res => {
				this.roomLoop = res.data.auto === 1;
			});
		},
		async initRoom(roomId) {
			this.roomId = roomId;
			this.dutyParam = {
				roomId: this.roomId,
				orgCode: this.orgCode,
				dutyDate: this.dutyDate,
				dutyWeek: getWeekRange(this.dutyDate),
				currentWeek: this.currentWeek[0] && this.currentWeek[1]
			};
			this.$nextTick(() => {
				// this.$refs.boardArrange.changeRoom(this.dutyParam);
				this.initData();
			});
			// this.$Modal.confirm({
			// 	title: "提示",
			// 	content: "监测到监室在押人员发生变化,是否自动填补值班空缺",
			// 	confirmText: "是",
			// 	cancelText: "否",
			// 	onOk: async () => {
			// 	this.initPersonData();
			// 	this.initArrangeData(false, true);
			// 	},
			// 	cancel: () => {
			// 	this.initData();
			// 	}
			// });
		},
		initData() {
			console.log('initData-----------------------------')
			this.initPersonData();
			this.initArrangeData();
			this.getAutoConfig();
		},
		initPersonData() {
			this.$refs.boardPerson.changeRoom(this.roomId);
		},
		initArrangeData(timeChange = false, isAuto = false) {
			let param = {
				dutyDate: this.dutyDate,
				dutyWeek: getWeekRange(this.dutyDate),
				roomId: this.roomId,
				currentWeek: this.currentWeek[0] && this.currentWeek[1]
			};
			if (timeChange) {
				this.$refs.boardArrange.changeWeek(param);
			} else {
				this.$refs.boardArrange.changeRoom(param, isAuto);
			}
		},
		handleClear() {
			this.$refs.boardArrange.clearAll();
			},
			async handleSubmit() {
			let list = this.$refs.boardArrange.getSettleArray();
			if (list.length === 0)  return this.$Message.info("当前不存在待提交的值班安排");
			await this.submitArrange(list);
			this.initData();
		},
		submitArrange(dutyList) {
			let param = {
				roomId: this.roomId, 
				// roomId: '1100001130100030001', 
				orgCode: this.orgCode,
				dutyList
			};
			// return  roomDutyArrange(param).then(() => { this.$messageSuccess("值班安排提交成功"); }).catch(err => this.$messageInfo(err));
			return this.$store.dispatch('authPostRequest',{
				url: this.$path.acp_duty_create,
				params: param
			}).then(res => {
				if(res.success){
					this.$Message.success('值班安排提交成功')
				} else {
					this.$Message.error(res.msg || '值班安排提交失败')
				}
			})
		},
		triggerLoop() {
			let roomLoop = this.roomLoop;
			return new Promise((resolve, reject) => {
				if (roomLoop) {
				resolve();
				this.handleLoop(roomLoop);
				} else {
				this.$Modal({
					title: "提示",
					content: "开启自动循环后，后台将在每周开始时自动安排当周值班，同时根据监室人员调整自动适应安排，是否确认开启自动循环功能？",
					confirm: () => {
					resolve();
					this.handleLoop(roomLoop);
					},
					cancel: () => {
					reject();
					}
				});
				}
			});
		},
		handleLoop(roomLoop) {
			let param = {
				roomId: this.roomId,
				auto: roomLoop ? 0 : 1,
			};
			roomDutyLoopOpen(param).then(() => {
				if (!roomLoop) {
				let isEmpty = this.$refs.boardArrange.emptyCheck();
				isEmpty && this.$refs.boardArrange.autoArrange();
				}
			});
		},
		handleAuto(value) {
			if (value) {
				this.automaticTipModal = value
			} else {
				this.automaticTipOk()
			}
			// this.$refs.boardArrange.autoArrange();
		},
		toPage(page) {
			this.$toPage(`/prisonRoomDuty/${page}`);
		},
		handleClass() {
			this.$refs.roomShift.open();
		},
		changeTimes(dutyTimes) {
			this.dutyTimes = Object.assign({}, dutyTimes);
		},
		changeWeek(flag) {
			let nowDate = new Date(this.dutyDate).getTime();
			let day7 = 7 * 24 * 60 * 60 * 1000;
			nowDate = flag ? nowDate - day7 : nowDate + day7;
			this.dutyDate = new Date(nowDate).Format("yyyy-MM-dd");
			this.changeDate();
			// this.dutyParam = {
			// 	...this.dutyParam,
			// 	dutyDate: this.dutyDate,
			// 	dutyWeek: getWeekRange(this.dutyDate)
			// };
		},
		goBackWeek() {
			this.dutyDate = new Date().Format("yyyy-MM-dd");
			this.changeDate();
		},
		changeDate() {
			this.$nextTick(() => {
				this.initArrangeData(true);
			});
		},
		changeShift() {
			this.dutyDate = new Date().Format("yyyy-MM-dd");
			this.initData();
		},
		handleTip(submitCb, noSubmitCb) {
			// let list = this.$refs.boardArrange.getSettleArray();
			// if (list.length === 0)  return noSubmitCb();
			// this.$Modal.confirm({
			// 	title: "提示",
			// 	content: "是否保存提交排班的改动",
			// 	confirmText: "是",
			// 	cancelText: "否",
			// 	onOk: async () => {
			// 	submitCb(list);
			// 	},
			// 	onCancel: () => {
			// 	noSubmitCb();
			// 	}
			// });
			let list = this.$refs.boardArrange.getSettleArray();
			if (list.length === 0)  {
				return noSubmitCb();
			} else {
				this.$Modal.info({
				title: '温馨提示',
				content: '请先保存提交值日事务的改动'
				})
			}
		},
		leaveEvent(from, to, type) {
			return new Promise(resolve => {
				this.handleTip(
				async (list) => {
					await this.submitArrange(list);
					type === "change" && this.initData();
					resolve();
				},
				() => resolve(),
				);
			});
		},
		openShiftModal() {
			this.shiftModal = true;
			this.$Modal({
				title: "信息提示",
				content: "班次发生更改，需刷新页面",
				cancelBtn: false,
				confirmText: "确认",
				confirm: () => {
				this.shiftModal = false;
				this.changeShift();
				},
				cancel: () => {
				this.shiftModal = false;
				this.changeShift();
				}
			});
		},
		listenShiftChange() {
			let socketIo = this.$socketIo.getIo();
			socketIo && socketIo.on("topic-keep-alive", data => {
				let noticeJson = JSON.parse(data);
				if (noticeJson.action === "web_dutyShiftChange" && !this.shiftModal) {
				if (this.$route.name === "prisonRoomDutyHome") {
					this.openShiftModal();
				} else {
					this.openModal = true;
				}
				}
			});
		},
		automaticbedEvent() {
			this.getZdpbList()
			this.automaticBedModal = true
		},
		automaticBedCancel() {
			this.automaticBedModal = false
			this.isEnabled = false
		},
		getZdpbList(){
			this.$store.dispatch('authGetRequest',{
				url: `/bsp-com/static/dic/acp/ZD_ZBGL_ZDPBGZ.js`
			}).then(res => {
				let arr = []
				let numTon = eval('(' + res + ')')
				arr = numTon()
				this.zdpbList = arr
				this.$nextTick(() => {
					this.automaticBedModal = true
				})
			})
		},
		automaticBedOk() {
			// return;
			this.$store.dispatch('authPostRequest',{
				url: this.$path.acp_duty_updateAutoConfig,
				params:{
					// roomId: '1100001130100030001', 
					roomId: this.roomId,
					orgCode: this.orgCode,
					schedulingRule: this.vertical.join(','),
					isEnabled: this.isEnabled ? 1 : 0
				}
			}).then(res => {
				if(res.success) {
					this.$Message.success(res.msg || '自动排班配置成功!')
					this.$nextTick(() => {
						this.automaticBedModal = false
					})
				} else{
					this.$Message.error(res.msg || '接口操作失败!')
					this.$nextTick(() => {
						this.automaticBedModal = false
					})
				}
			})
		},
		automaticTipCancel() {
			this.automaticTipModal = false
		},
		automaticTipOk() {
			this.$store.dispatch('authPostRequest',{
				url: this.$path.acp_duty_updateAutoConfig,
				params:{
					// roomId: '1100001130100030001', 
					roomId: this.roomId,
					orgCode: this.orgCode,
					schedulingRule: this.vertical.join(','),
					isEnabled: this.isEnabled ? 1 : 0
				}
			}).then(res => {
				if(res.success) {
					this.$Message.success(this.isEnabled ? '开启自动排班!' : '关闭自动排班!')
					this.$nextTick(() => {
						this.automaticTipModal = false
					})
				} else{
					this.$Message.error(res.msg || '接口操作失败!!')
					this.$nextTick(() => {
						this.automaticTipModal = false
					})
				}
			})
		},
		getAutoConfig() {
			this.$store.dispatch('authGetRequest',{
				url: this.$path.acp_duty_getAutoConfig,
				params:{
					roomId: this.roomId,
					orgCode: this.orgCode,
				}
			}).then(res => {
				if(res.success) {
					this.isEnabled = res.data.isEnabled === 1
					this.vertical = res.data.schedulingRule.split(',')
				} else{
					this.$Message.error(res.msg || '接口操作失败!!')
				}
			})
		},
		autoShift() {
			const [firstDay, lastDay] = getWeekRange(this.dutyDate);
			let param = {
				roomId: this.roomId,
				orgCode: this.orgCode,
				startDate: firstDay,
				endDate: lastDay
			}

			// 触发子组件加载数据
			this.$nextTick(() => {
				if (this.$refs.boardArrange) {
					this.$refs.boardArrange.autoShift(param);
				}
			});
		},
		getRoomData(item){
			// this.spinShow = true
			let params = {
				orgCode: this.$store.state.common.orgCode,
				roomName: this.page.roomName,
				pageNo: this.page.pageNo,
				pageSize: this.page.pageSize,
			}

			//监区过滤
			if(item){
				params.areaId = item.areaCode
			}

			//主协管过滤
			params = Object.assign({}, params, this.searchForm)

			this.$store.dispatch('authPostRequest', {
				url: '/acp-com/base/pm/areaPrisonRoom/page',
				params: params
			}).then(resp => {
				if (resp.code == 0) {
					if(resp.data.list.length > 0) {
						this.roomData = resp.data && resp.data.list ? resp.data.list[0] : {}
						this.roomId = this.roomData.roomCode
					} else {
						this.getPrison()
					}
					
				} else {
				this.$Notice.error({
					title: '错误提示',
					desc: resp.msg
				})
				// this.spinShow = false
				}
			})
		},
		getPrison(){
			let params = {
				orgCode: this.$store.state.common.orgCode
			}
			this.$store.dispatch('authGetRequest', {
				url: '/acp-com/base/area/getAreaListByOrgCode',
				params: params
			}).then(resp => {
				if (resp.code == 0) {
					if(resp.data.length > 0) {
						resp.data.forEach(element => {
							if(element.children){
								element.children.forEach(i => {
									this.roomData = i
									this.roomId = i.roomCode
								})
							}
						});
					} else {
						this.$Notice.error({
							title: '温馨提示',
							desc: '暂无监室数据'
						})
					}
				} else {
				this.$Notice.error({
					title: '错误提示',
					desc: resp.msg
				})
				}
			})
		},
	}
}
</script>

<style lang="less" scoped>
.operate-box {
  height: 70px;
  border-top: 1px solid #E4EAF0;
  display: flex;
  justify-content: center;
  align-items: center;
  >button {
    margin: 0 8px;
  }
}
  .prison-room-duty{
    width: 100%;
    height: calc(~'100vh - 180px');
    .duty-header{
      .header-opt{
        display: flex;
        align-items: center;
        .auto-btn{
          font-size: 16px;
          color: #415060;
        }
        .opt-btn{
          margin-left: 15px;
        }
      }
    }
    .duty-content {
      width: 100%;
      overflow: hidden;
      height: calc(~'100% - 60px');
      padding-left: 548px;
      position: relative;
      transition: padding-left 0.3s linear;
      &.history{
        padding-left: 0;
        .duty-person {
          left: -548px;
        }
      }
    }
  }
  .duty-person {
    transition: all 0.3s linear;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 548px;
    height: 100%;
    background: #FFFFFF;
    box-shadow: 4px 0px 10px 0px rgba(189,193,199,0.5);
    border-radius: 0px 0px 0px 8px;
  }
  .duty-arrange{
    height: 100%;
    width: 100%;
    overflow-y: auto;
    .arrange-header {
      width: 100%;
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 16px;
      .arrange-date{
        display: flex;
        .date {
          width:160px;
          margin: 0 8px;
        }
        .back-btn{
          margin-left: 8px;
        }
        .icon {
          display:inline-block;
          width:20px;
          height:20px;
          vertical-align: middle;
          background-size:100% 100%;
          &.arrow-left {
            background-image:url(../../../assets/images/prisonRoomDuty/btn_arrow_left.png);
          }
          &.arrow-right {
            background-image:url(../../../assets/images/prisonRoomDuty/btn_arrow_right.png);
          }
          &.back {
            background-image:url(../../../assets/images/prisonRoomDuty/btn_back.png);
          }
        }
      }
      .arrange-opt{
		display: flex;
		align-items: center;
        .opt{
          margin-right: 12px;
          &:nth-last-child(1){
            margin-right: 0px;
          }
        }
      }
    }
    .arrange-content {
      width: 100%;
      height: calc(~'100% - 70px');
      position: relative;
	  margin-left: 10px;
      .mask {
        z-index: 10;
        left: 0;
        bottom: 0;
        right: 0;
        top: 0;
        position: absolute;
        background: #FFFFFF;
        opacity: 0.6;
      }
    }
  }
  @media screen and (max-width: 1599px){
    .prison-room-duty{
      .duty-content{
        padding-left: 288px;
      }
    }
    .duty-person {
      width: 288px;
    }
  }
  .actionBtn{
		width: 150px;
		height: 38px;
		background: #FFFFFF;
		border-radius: 4px;
		border: 1px solid #2390FF;
		display: flex;
		align-items: center;
		cursor: pointer;
		.actionBtn-text {
			width: 130px;
			line-height: 38px;
			font-size: 16px;
			color: #2390FF;
			text-align: center;
			margin-right: 8px;
			cursor: pointer;
		}
		p{
			width: 108px;
			// height: 21px;
			line-height: 38px;
			font-family: MicrosoftYaHei;
			font-weight: normal;
			font-size: 16px;
			color: #2390FF;
			text-align: center;
			font-style: normal;
			text-transform: none;
		}
		.vertical-line {
			width: 1px;
			height: 100%;
			background-color: #2390FF;
			margin: 0 5px; /* 竖线与两侧元素的间距 */
		}
		.img-container {
			width: 50px;
			display: flex;
			align-items: center;
			justify-content: center; /* 水平居中 */
			// margin-left: 5px; /* 调整与竖线的间距 */
		}
		img{
			width: 20px;
			height: 20px;
			// margin-left: 5px;
			// border: 2px solid #2390FF;
		}
	}
	.actionBtns{
		width: 108px;
		// height: 21px;
		line-height: 38px;
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 16px;
		color: #2390FF;
		text-align: center;
		font-style: normal;
		text-transform: none;
		border: 1px solid #2390FF;
		border-radius: 4px;
		cursor: pointer;
	}
	/deep/.ivu-modal-body{
		min-height: 230px !important;
		overflow: auto;
		position: relative !important;
	}
	.select-use{
		display: flex;
		flex-direction: column;
	}
	.roomDutyTip /deep/.ivu-modal-body{
		min-height: 200px !important;
	}
</style>