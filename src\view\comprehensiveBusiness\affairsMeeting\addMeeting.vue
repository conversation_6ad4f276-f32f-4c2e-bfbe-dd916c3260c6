<template>

    <div>
        <div class="add-meeting">
            <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="160">
                <p class="subtit">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />所务会议
                </p>
                <div class="form">
                    <Row>
                        <Col span="6">
                        <FormItem label="会议开始时间" prop="meetingStartTime"
                            :rules="[{ trigger: 'blur', message: '会议开始时间为必填', required: true }]">
                            <DatePicker v-model="formData.meetingStartTime" type="datetime" format="yyyy-MM-dd HH:mm"
                                placeholder="请输入会议开始时间" @on-change="changeStartTime"></DatePicker>
                        </FormItem>
                        </Col>
                        <Col span="6">
                        <FormItem label="会议结束时间" prop="meetingEndTime"
                            :rules="[{ trigger: 'blur', message: '会议结束时间为必填', required: true }]">
                            <DatePicker v-model="formData.meetingEndTime" type="datetime" format="yyyy-MM-dd HH:mm"
                                placeholder="请输入会议结束时间" @on-change="changeEndTime"></DatePicker>
                        </FormItem>
                        </Col>
                        <Col span="6">
                        <FormItem label="会议分类" prop="categoryId">
                            <!-- <Input v-model="formData.categoryId" placeholder="请输入会议分类" /> -->
                            <s-dicgrid v-model="formData.categoryId"
                                @change="$refs.formData.validateField('categoryId')" :isSearch="true"
                                dicName="ZD_JLS_HYLX" />
                        </FormItem>
                        </Col>
                        <Col span="6">
                        <FormItem label="会议地点" prop="location">
                            <Input v-model="formData.location" placeholder="请输入会议地点" />
                        </FormItem>
                        </Col>
                        <Col span="24">
                        <FormItem label="会议主题" prop="subject"
                            :rules="[{ trigger: 'blur', message: '会议主题为必填', required: true }]">
                            <Input v-model="formData.subject" placeholder="请输入" />
                        </FormItem>
                        </Col>
                        <Col span="24">
                        <FormItem label="会议内容" prop="content"
                            :rules="[{ trigger: 'blur', message: '会议内容为必填', required: true }]">
                            <Input v-model="formData.content" placeholder="请输入" type="textarea"
                                :autosize="{ minRows: 5, maxRows: 8 }"></Input>
                        </FormItem>
                        </Col>

                    </Row>
                    <Row>
                        <Col span="8">
                        <FormItem label="参会人员" prop="participantsSfzh"
                            :rules="[{ trigger: 'blur', message: '参会人员为必填', required: true }]">
                            <!-- <Input v-model="formData.participants" placeholder="请输入" /> -->
                            <user-selector v-model="formData.participantsSfzh" tit="用户选择"
                                :text.sync="formData.participants"
                                @onSelect="$refs.formData.validateField('participantsSfzh')" returnField="id"
                                numExp='num>=1' msg="至少选中1人">
                            </user-selector>
                        </FormItem>
                        </Col>
                        <Col span="8">
                        <FormItem label="记录人" prop="recorder"
                            :rules="[{ trigger: 'blur', message: '记录人为必填', required: true }]">
                            <Input v-model="formData.recorder" placeholder="请输入" />
                        </FormItem>
                        </Col>
                        <Col span="24">
                        <FormItem label="会议纪要" prop="meetingSummary">
                            <Input v-model="formData.meetingSummary" placeholder="请输入" />
                        </FormItem>
                        </Col>
                        <Col span="24">
                        <FormItem label="附件" prop="attachment">
                            <file-upload :defaultList="fileList" :serviceMark="serviceMark" :bucketName="bucketName"
                                :beforeUpload="beforeUpload" @fileSuccess="fileSuccessFile" @fileRemove="fileRemoveFile"
                                @fileComplete="fileCompleteFile"  :key="fileList.length" />
                        </FormItem>
                        </Col>
                        <Col span="24">
                        <FormItem label="会议结论" prop="conclusion">
                            <Input v-model="formData.conclusion" placeholder="请输入" type="textarea"
                                :autosize="{ minRows: 2, maxRows: 5 }"></Input>
                        </FormItem>
                        </Col>
                    </Row>

                </div>
            </Form>
        </div>

        <div class="bsp-base-fotter">
            <Button @click="onCancel">取消</Button>
            <Button @click="onSubmit" type="primary">提交</Button>
        </div>
    </div>
</template>

<script>
import { fileUpload } from 'sd-minio-upfile'
import { mapActions } from 'vuex'
import { getUserCache } from "@/libs/util.js"
import { userSelector } from 'sd-user-selector'
import dayjs from 'dayjs'
export default {
    name: 'addMeeting',
    components: {
        fileUpload,
        userSelector
    },
    props: {
        type: {
            type: String,
            default: 'add'
        },
        dataMsg: {
            type: Object,
            default: {}
        }

    },
    data() {
        return {
            formData: {
                recorder: getUserCache.getUserName(),
                dataSources: '1',
                meetingStartTime: '',
                meetingEndTime: ''
            },
            ruleValidate: {},
            // 文件上传
            showFile: false,
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            fileList: [ ],

        }
    },
    created() { 
        if (this.type == 'edit') {
            this.getDetailsData(this.dataMsg.id)
        }else { 
            this.showFile = true
        }

    },
    mounted() {
        console.log(this.type,'sssssss');
        
    },
    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        onCancel() {
            this.$refs.formData.resetFields()
            this.$emit('on_show_table')
        },
        onSubmit() {
            if (this.type == 'edit') {
                this.formData.meetingStartTime = dayjs(this.formData.meetingStartTime).format("YYYY-MM-DD HH:mm")
                this.formData.meetingEndTime = dayjs(this.formData.meetingEndTime).format("YYYY-MM-DD HH:mm")
            }
            console.log(this.type);
            this.$refs.formData.validate(valid => {
                if (valid) {
                    if (this.type == 'edit') {
                        this.formData.dataSources = '1',
                            this.authPostRequest({ url: this.$path.zh_MeetingUpdate, params: this.formData }).then(res => {
                                if (res.success) {
                                    this.$Message.success('修改成功')
                                    this.onCancel()
                                } else {
                                    this.$Message.error(res.message)
                                }
                            })
                    } else {
                        this.authPostRequest({ url: this.$path.zh_MeetingCreate, params: this.formData }).then(res => {
                            if (res.success) {
                                this.$Message.success('新增成功')
                                this.onCancel()
                            } else {
                                this.$Message.error(res.message)
                            }
                        })
                    }


                }
            })
        },
        fileCompleteFile(data) {
            this.fileList = data || []
            this.formData.attachment = data && data.length > 0 ? JSON.stringify(data) : '' 
        },
        changeStartTime(data, type) {
            this.formData.meetingStartTime = data
            this.$refs.formData.validateField('meetingStartTime')
        },
        changeEndTime(data, type) {
            this.formData.meetingEndTime = data
            this.$refs.formData.validateField('meetingEndTime')
        },
        getDetailsData(id) {
            this.authGetRequest({ url: this.$path.zh_MeetingGet, params: { id: id } }).then(res => {
                if (res.success) {
                    this.formData = res.data
                    if (res.data.attachment) {
                        this.fileList = JSON.parse(res.data.attachment)
                        this.showFile = true
                        console.log(this.fileList);

                    }




                }
            })
        },
        /**
* 文件上传成功回调
*/
        fileSuccessFile(data) {
            // 文件上传成功处理
        },

        /**
         * 文件删除回调
         */
        fileRemoveFile(data) {
            // 文件删除处理
        },
        beforeUpload() { },
    }
}
</script>

<style scoped lang="less">
.add-meeting {

    // width: 80%;
    // margin: 0 auto;
    border: 1px solid #CEE0F0;

    .form {
        margin: 15px;
    }

    .subtit {
        background: #F2F6FC;
        font-size: 17px;
        border-bottom: 1px solid #CEE0F0;
        padding: 1px 15px 0px 15px;
        line-height: 35px;
    }
}

.bsp-base-form {
    padding: 15px;
}
</style>