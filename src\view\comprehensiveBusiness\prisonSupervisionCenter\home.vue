<template>
    <div>
        <Card title="监所督导" dis-hover :bordered="false">
            <p class="detail-title">收押信息</p>
            <div class="com-content-wrapper">
                <statisticalAnalysis :mark="globalAppCode + ':benriyiqiandao'" />
                <statisticalAnalysis :mark="globalAppCode + ':benriweiqiandao'" />
                <statisticalAnalysis :mark="globalAppCode + ':benribuqian'" />
            </div>
            <p class="detail-title">业务办理</p>
            <Tabs v-model="curTab" class="com-module-tabs">
                <TabPane v-for="item in tabs" :key="item.id" :name="item.id" :label="(h) => labelRender(h, item)">
                     <!-- <component :ref="item.component" :is="item.component" :id="item.id" /> -->
                </TabPane>
            </Tabs>
        </Card>
    </div>
</template>
<script>
import { statisticalAnalysis } from 'sd-statistical-analysis'
export default {
    components: {
        statisticalAnalysis
    },
    data() {
        return {
            labelRender: (h, item) => {
                return h('div', [
                    h('span', item.label),
                    item.badge > 0 && h('Badge', {
                        props: {
                            count: item.badge
                        }
                    })
                ])
            },
            curTab: "1",
            tabs: [
                {
                    id: "1",
                    label: "待整改督导单",
                    badge: 2,
                    component: "toSupervisionList",
                },
                {
                    id: "2",
                    label: "待发送申诉单",
                    badge: 0,
                    component: "toComplainList",
                },
                {
                    id: "3",
                    label: "待发送反馈单",
                    badge: 1,
                    component: "toFeedBackList",
                },
            ],
        }
    }
}

</script>

<style scoped lang="less">
.com-content-wrapper {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;

}

.com-module-tabs {
    /deep/.ivu-badge-count {
        // min-width: 15px;
        // height: 15px;
        // line-height: 13px;
        // border-radius: 50%;
    }
}
</style>