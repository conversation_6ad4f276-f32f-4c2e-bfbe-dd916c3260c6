<template>
  <div>
    <div class="table-container" v-if="tableContainer">
      <s-DataGrid ref="grid" funcMark="wjpgl" :customFunc="true" :params="params">
        <template slot="customHeadFunc" slot-scope="{ func }">
          <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':wjpgl:add')"
            @click.native="handleAdd('add')">违禁品登记</Button>
        </template>
        <template slot="customRowFunc" slot-scope="{ func, row, index }">
          <Button type="primary" v-if="func.includes(globalAppCode + ':wjpgl:detail')"
            @click.native="handleDetail(index, row)">详情</Button>
          <Button type="primary" v-if="func.includes(globalAppCode + ':wjpgl:edit')"
            @click.native="handleEdit(index, row)" style="margin-left: 10px;">编辑</Button>
          <Button type="error" v-if="func.includes(globalAppCode + ':wjpgl:delete')"
            @click.native="handleDelete(index, row)" style="margin-left: 10px;">删除</Button>
        </template>
      </s-DataGrid>
    </div>
    <div v-if="addFormInfo">
      <!-- 违禁品登记表单 -->
      <div class="contraband-form-container">
        <!-- 左侧：被监管人员选择 -->
        <div class="contraband-form-left">
          <personnel-selector
            v-model="formData.jgrybm"
            mode="edit"
            title="被监管人员"
            placeholder="点击选择被监管人员"
            personnel-type="ZS"
            :show-case-info="true"
            :enable-scan="true"
            :show-scan-tip="true"
            @change="handlePersonnelChange"
          />
        </div>

        <!-- 右侧：违禁品登记信息 -->
        <div class="contraband-form-right">
          <div class="fm-content-wrap">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />{{ formTitle }}
            </p>

            <Form ref="formData" :model="formData" :label-colon="true"
                  label-position="right" :label-width="120">
              <div class="form-content">
                <Row>
                  <Col span="24">
                    <FormItem prop="checkTime" label="检查时间"
                              :rules="[{ type: 'date', trigger: 'change', message: '请选择检查时间', required: true }]">
                      <DatePicker v-model="formData.checkTime" type="datetime"
                                  placeholder="请选择检查时间" format="yyyy-MM-dd HH:mm:ss"
                                  :options="datePickerOptions"
                                  @on-change="handleCheckTimeChange"
                                  @on-ok="handleDatePickerOk"
                                  style="width: 100%;" />
                    </FormItem>
                  </Col>
                </Row>
                <Row>
                  <Col span="24">
                    <FormItem prop="contrabandCategory" label="违禁品类型"
                              :rules="[{
                                trigger: 'change',
                                message: '至少选择一个违禁品类型',
                                required: true,
                                type: 'array',
                                min: 1
                              }]">
                      <CheckboxGroup v-model="formData.contrabandCategory" class="contraband-types">
                        <Row>
                          <Col span="8"><Checkbox label="管制刀具">管制刀具</Checkbox></Col>
                          <Col span="8"><Checkbox label="毒品及相关物品">毒品及相关物品</Checkbox></Col>
                          <Col span="8"><Checkbox label="尖锐、硬质物品">尖锐、硬质物品</Checkbox></Col>
                        </Row>
                        <Row>
                          <Col span="8"><Checkbox label="通信设备">通信设备</Checkbox></Col>
                          <Col span="8"><Checkbox label="现金及贵重物品">现金及贵重物品</Checkbox></Col>
                          <Col span="8"><Checkbox label="淫秽及有害物品">淫秽及有害物品</Checkbox></Col>
                        </Row>
                        <Row>
                          <Col span="8"><Checkbox label="可能用于逃脱的工具">可能用于逃脱的工具</Checkbox></Col>
                          <Col span="8"><Checkbox label="与案件相关物品">与案件相关物品</Checkbox></Col>
                          <Col span="8"><Checkbox label="其他">其他</Checkbox></Col>
                        </Row>
                      </CheckboxGroup>
                    </FormItem>
                  </Col>
                </Row>
                <Row>
                  <Col span="24">
                    <FormItem prop="contrabandImgPath" label="违禁品照片" >
                      <file-upload
                        :defaultList="contrabandImgList"
                        :serviceMark="serviceMark"
                        :bucketName="bucketName"
                        :beforeUpload="beforeUpload"
                        @fileSuccess="fileSuccessFile"
                        @fileRemove="fileRemoveFile"
                        @fileComplete="fileCompleteFile"
                        v-if="showFile"
                      />
                    </FormItem>
                  </Col>
                </Row>

                <Row>
                  <Col span="24">
                    <FormItem prop="handlingSituatio" label="处理情况"
                              :rules="[{ trigger: 'blur,change', message: '请输入处理情况', required: true }]">
                      <Input v-model="formData.handlingSituatio" type="textarea" :rows="4"
                             placeholder="请详细描述违禁品的处理情况，如：已收缴、已销毁、已移交等" />
                    </FormItem>
                  </Col>
                </Row>
              </div>
            </Form>
          </div>
        </div>
      </div>

      <div class="bsp-base-fotter">
        <Button @click="handleCancel" style="margin-right: 10px;">取消</Button>
        <Button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ operationType === 'add' ? '确认登记' : '确认修改' }}
        </Button>
      </div>


    </div>
    <div v-if="detailInfo">
      <!-- 详情页面区域 -->
      <div class="contraband-detail">
        <div class="contraband-detail-left">
          <personnel-selector
            :value="formData.jgrybm"
            mode="detail"
            title="被监管人员"
            :show-case-info="true"
          />
          <record :jgrybm="formData.jgrybm" />
        </div>
        <div class="contraband-detail-right">
          <contrabandInfo :formData="formData" />
        </div>
      </div>
      <div class='bsp-base-fotter'>
        <Button @click='handleBackToList'>返 回</Button>
      </div>
    </div>
  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions, mapState } from 'vuex'
import { fileUpload } from 'sd-minio-upfile'
import personnelSelector from "@/components/personnel-selector"
import record from './record.vue'
import contrabandInfo from './contrabandInfo.vue'

export default {
  name: "prohibitedManage",

  data() {
    return {
      tableContainer: true,    // 控制列表显示
      addFormInfo: false,      // 控制新增/编辑表单显示
      detailInfo: false,       // 控制详情页面显示
      params: {},              // 列表查询参数
      formTitle: '新增违禁品', // 表单标题
      currentRow: null,        // 当前操作的行数据
      currentIndex: null,      // 当前操作的行索引
      operationType: 'add',    // 操作类型：add/edit
      submitLoading: false,    // 提交按钮loading状态

      // 表单数据
      formData: {
        checkTime: null,            // 检查时间
        contrabandCategory: [],     // 违禁品类型（多选）
        contrabandImgPath: '',      // 违禁品照片路径
        handlingSituatio: '',       // 处理情况
        jgrybm: '',                // 监管人员编码
        jgryxm: '',                // 监管人员姓名
        prison: null,              // 选择的人员信息
        dataSources: '1'           // 数据来源，默认为1
      },

      // 人员选择相关
      defaultImg: require('@/assets/images/main.png'),
      http: serverConfig.severHttp,

      // 文件上传相关
      showFile: true,
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      contrabandImgList: [],     // 违禁品照片列表

      // 日期选择器配置
      datePickerOptions: {
        disabledDate(date) {
          // 禁用比当前时间大的日期
          return date && date.valueOf() > Date.now()
        },
        // 设置默认时间为当前时间
        selectableRange: '00:00:00 - 23:59:59',
        // 当选择今天时，默认时间为当前时间
        onPick: (date) => {
          if (date && this.isSameDay(date, new Date())) {
            const now = new Date()
            date.setHours(now.getHours())
            date.setMinutes(now.getMinutes())
            date.setSeconds(now.getSeconds())
          }
          return date
        }
      }
    }
  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),

    /**
     * 统一处理null值显示
     */
    formatValue(value) {
      if (value === null || value === undefined || value === '' || value === 'null') {
        return '-'
      }
      return value
    },

    /**
     * 格式化日期时间为字符串
     */
    formatDateTime(date) {
      if (!date) return null
      if (typeof date === 'string') return date

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    /**
     * 处理检查时间变化
     */
    handleCheckTimeChange() {
      // 这个方法在用户选择日期时触发，暂时不做处理
      // 主要逻辑在 handleDatePickerOk 中处理
    },

    /**
     * 处理日期选择器确定按钮点击事件
     */
    handleDatePickerOk() {
      // 当用户点击确定按钮后，检查是否需要自动设置当前时间
      if (this.formData.checkTime && typeof this.formData.checkTime === 'object') {
        const now = new Date()
        const selectedDate = new Date(this.formData.checkTime)

        // 检查是否选择的是今天且时间为00:00:00
        if (this.isSameDay(selectedDate, now) &&
            selectedDate.getHours() === 0 &&
            selectedDate.getMinutes() === 0 &&
            selectedDate.getSeconds() === 0) {

          // 创建一个新的日期对象，保持今天的日期，但使用当前时间
          const todayWithCurrentTime = new Date()

          // 延迟设置，确保DatePicker完成内部处理
          this.$nextTick(() => {
            this.formData.checkTime = todayWithCurrentTime
          })
        }
      }
    },

    /**
     * 判断两个日期是否为同一天
     */
    isSameDay(date1, date2) {
      return date1.getFullYear() === date2.getFullYear() &&
             date1.getMonth() === date2.getMonth() &&
             date1.getDate() === date2.getDate()
    },

    /**
     * 处理新增操作
     */
    handleAdd(type) {
      this.operationType = type
      this.formTitle = '新增违禁品'
      this.currentRow = null
      this.currentIndex = null
      this.resetForm()
      // 新增时默认设置检查时间为当前时间
      this.formData.checkTime = new Date()
      this.tableContainer = false
      this.addFormInfo = true
      this.detailInfo = false
    },

    /**
     * 处理编辑操作
     */
    handleEdit(index, row) {
      this.operationType = 'edit'
      this.formTitle = '编辑违禁品'
      this.currentRow = row
      this.currentIndex = index

      // 先获取数据，再处理表单状态
      this.authGetRequest({
        url: this.$path.contraband_get,
        params: { id: row.id }
      }).then(res => {
        if (res.success && res.data) {
          this.loadFormData(res.data)
        } else {
          this.loadFormData(row)
        }

        // 数据加载完成后，等待DOM更新
        this.$nextTick(() => {
          // 数据已加载完成，表单应该正确显示
        })
      }).catch(err => {
        console.error('获取详情失败:', err)
        this.loadFormData(row)

        // 数据加载完成后，等待DOM更新
        this.$nextTick(() => {
          // 数据已加载完成，表单应该正确显示
        })
      })

      this.tableContainer = false
      this.addFormInfo = true
      this.detailInfo = false
    },

    /**
     * 处理详情查看
     */
    handleDetail(index, row) {
      this.currentRow = row
      this.currentIndex = index

      // 检查是否有详情接口
      if (this.$path.contraband_get) {
        // 获取详情数据
        this.authGetRequest({
          url: this.$path.contraband_get,
          params: { id: row.id }
        }).then(res => {
          if (res.success && res.data) {
            this.formData = res.data
          } else {
            // 如果接口调用失败，使用列表数据
            this.formData = { ...row }
          }
          this.processDetailData()
          this.showDetailPage()
        }).catch(err => {
          console.error('获取详情失败:', err)
          // 降级处理，使用列表数据
          this.formData = { ...row }
          this.processDetailData()
          this.showDetailPage()
        })
      } else {
        // 如果没有详情接口，直接使用列表数据
        this.formData = { ...row }
        this.processDetailData()
        this.showDetailPage()
      }
    },

    /**
     * 处理详情数据
     */
    processDetailData() {
      // 处理违禁品类型数组
      if (this.formData.contrabandCategory && typeof this.formData.contrabandCategory === 'string') {
        this.formData.contrabandCategory = this.formData.contrabandCategory.split(',')
      }
    },

    /**
     * 显示详情页面
     */
    showDetailPage() {
      this.tableContainer = false
      this.addFormInfo = false
      this.detailInfo = true
    },

    /**
     * 处理删除操作
     */
    handleDelete(index, row) {
      this.$Modal.confirm({
        title: '确认删除',
        content: '确定要删除这条违禁品记录吗？删除后不可恢复。',
        onOk: () => {
          this.authGetRequest({
            url: this.$path.contraband_delete,
            params: { ids: row.id }
          }).then(res => {
            if (res.success) {
              this.$Message.success('删除成功')
              this.refreshList()
            } else {
              this.$Message.error(res.message || '删除失败')
            }
          }).catch(err => {
            this.$Message.error('删除失败')
            console.error('删除违禁品记录失败:', err)
          })
        }
      })
    },

    /**
     * 处理取消操作
     */
    handleCancel() {
      this.resetForm()
      this.tableContainer = true
      this.addFormInfo = false
      this.detailInfo = false
      this.currentRow = null
      this.currentIndex = null
    },

    /**
     * 处理提交操作
     */
    handleSubmit() {
      if (!this.formData.jgrybm) {
        this.$Message.error('请选择被监管人员！')
        return
      }

      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.submitLoading = true
          this.saveDataForm()
        } else {
          this.$Message.error('请完善表单信息')
          // 输出验证错误详情
          if (this.$refs.formData && this.$refs.formData.fields) {
            this.$refs.formData.fields.forEach(field => {
              if (field.validateState === 'error') {
                console.log('验证失败字段:', field.prop, field.validateMessage)
              }
            })
          }
        }
      })
    },



    /**
     * 保存表单数据
     */
    saveDataForm() {
      // 准备提交数据，只包含表单中实际存在的字段
      const submitData = {
        checkTime: this.formatDateTime(this.formData.checkTime), // 将Date对象转为字符串
        contrabandCategory: this.formData.contrabandCategory.join(','), // 将数组转为字符串
        contrabandImgPath: this.formData.contrabandImgPath,
        handlingSituatio: this.formData.handlingSituatio,
        jgrybm: this.formData.jgrybm,
        jgryxm: this.formData.jgryxm,
        dataSources: this.formData.dataSources || '1'
      }

      // 编辑时需要包含ID
      if (this.operationType === 'edit' && this.formData.id) {
        submitData.id = this.formData.id
      }

      const url = this.operationType === 'add' ? this.$path.contraband_create : this.$path.contraband_update
      const message = this.operationType === 'add' ? '违禁品登记成功' : '违禁品修改成功'

      this.authPostRequest({
        url: url,
        params: submitData
      }).then(res => {
        if (res.success) {
          this.submitLoading = false
          this.$Message.success(message)
          this.handleCancel()
          this.refreshList()
        } else {
          this.submitLoading = false
          this.$Message.error(res.message || '操作失败')
        }
      }).catch(err => {
        this.submitLoading = false
        this.$Message.error('操作失败')
        console.error('提交违禁品记录失败:', err)
      })
    },

    /**
     * 返回列表
     */
    handleBackToList() {
      this.tableContainer = true
      this.addFormInfo = false
      this.detailInfo = false
      this.currentRow = null
      this.currentIndex = null
    },

    /**
     * 刷新列表数据
     */
    refreshList() {
      if (this.$refs.grid) {
        this.$refs.grid.query_grid_data(1)
      }
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.formData = {
        checkTime: null,
        contrabandCategory: [],
        contrabandImgPath: '',
        handlingSituatio: '',
        jgrybm: '',
        jgryxm: '',
        prison: null,
        dataSources: '1'
      }
      this.contrabandImgList = []
      if (this.$refs.formData) {
        this.$refs.formData.resetFields()
      }
    },

    /**
     * 加载表单数据（编辑时使用）
     */
    loadFormData(row) {
      // 只加载表单中实际存在的字段
      this.formData = {
        id: row.id,
        checkTime: row.checkTime ? new Date(row.checkTime) : null, // 转换为Date对象
        contrabandCategory: row.contrabandCategory ? row.contrabandCategory.split(',') : [],
        contrabandImgPath: row.contrabandImgPath,
        handlingSituatio: row.handlingSituatio,
        jgrybm: row.jgrybm,
        jgryxm: row.jgryxm,
        prison: row.prison || null, // 直接使用后端返回的prison对象
        dataSources: row.dataSources || '1'
      }

      // 如果有人员编码，调用接口获取完整的人员信息
      if (row.jgrybm) {
        this.getPrisonerSelectCompomenOne(row.jgrybm)
      } else {
        // 如果没有人员编码，初始化为空对象
        this.formData.prison = {}
      }

      // 设置照片列表
      if (row.contrabandImgPath) {
        try {
          const imgData = JSON.parse(row.contrabandImgPath)
          this.contrabandImgList = Array.isArray(imgData) ? imgData : []
        } catch (e) {
          console.error('解析图片数据失败:', e)
          this.contrabandImgList = []
        }
      } else {
        this.contrabandImgList = []
      }

      // 强制更新文件上传组件
      this.$nextTick(() => {
        this.showFile = false
        this.$nextTick(() => {
          this.showFile = true
        })
      })
    },





    /**
     * 获取人员详细信息（参考人员信息组件）
     */
    getPrisonerSelectCompomenOne(jgrybm) {
      let params = {
        jgrybm: jgrybm,
        ryzt: 'ZS'
      }

      this.authGetRequest({
        url: '/acp-com/base/pm/prisoner/getPrisonerSelectCompomenOne',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.formData.prison = resp.data
        } else {
          console.error('获取人员信息失败:', resp.msg)
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg || '获取人员信息失败'
          })
        }
      }).catch(err => {
        console.error('获取人员信息接口调用失败:', err)
      })
    },
    /**
     * 人员选择变化处理
     */
    handlePersonnelChange(personnelData, jgrybm) {
      console.log('选择的人员:', personnelData, jgrybm)
      if (personnelData && jgrybm) {
        this.formData.prison = personnelData
        this.formData.jgrybm = jgrybm
        this.formData.jgryxm = personnelData.xm
        //this.$Message.success(`已选择人员: ${personnelData.xm || '未知'}`)
      } else {
        // 清空人员信息
        this.formData.prison = null
        this.formData.jgrybm = ''
        this.formData.jgryxm = ''
      }
    },



    /**
     * 文件上传前处理
     */
    beforeUpload(file) {
      return true
    },

    /**
     * 文件上传成功回调
     */
    fileSuccessFile(data) {
      // 文件上传成功处理
    },

    /**
     * 文件删除回调
     */
    fileRemoveFile(data) {
      // 文件删除处理
    },

    /**
     * 文件上传完成回调
     */
    fileCompleteFile(data) {
      this.contrabandImgList = data || []
      this.formData.contrabandImgPath = data && data.length > 0 ? JSON.stringify(data) : ''
    }
  },

  components: {
    sDataGrid,
    fileUpload,
    personnelSelector,
    record,
    contrabandInfo
  },

  created() {
    // 组件创建时的初始化逻辑
    // 可以在这里进行一些初始化操作，比如获取字典数据等
  },

  computed: {
    ...mapState(['common'])
  }
}
</script>

<style scoped lang="less">
@import "~@/assets/style/formInfo.css";

// 标题样式
/deep/.fm-content-wrap-title {
  border-bottom: 1px solid #cee0f0;
  background: #eff6ff;
  line-height: 40px;
  padding-left: 10px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: bold;
  font-size: 16px;
  color: #00244A;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-bottom: 1px solid #CEE0F0;
  margin-bottom: 20px;
}

// 表格容器样式
.table-container {
  width: 100%;
  height: 100%;
}

// 表单容器样式
.form-container {
  padding: 20px;
}

// 详情容器样式
.detail-container {
  padding: 20px;
}

// 表单头部样式
.add-form-header {
  padding: 20px;
}

// 详情内容样式
.detail-content {
  padding: 20px;
}

// 底部按钮样式
.bsp-base-fotter {
  text-align: center;
  padding: 20px;
  border-top: 1px solid #e8eaec;
  background: #f8f8f9;
}

// 违禁品表单容器样式
.contraband-form-container {
  display: flex;
  gap: 20px;
  padding: 20px;
  min-height: 600px;
}

// 左侧人员选择区域
.contraband-form-left {
  width: 400px;
  flex-shrink: 0;
}

// 右侧表单区域
.contraband-form-right {
  flex: 1;
  min-width: 0;
}

// 人员选择区域样式
.personnel-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .detail-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin: 0;
    }
  }

  .personnel-placeholder {
    border: 2px dashed #ddd;
    border-radius: 8px;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    background: #f8f9fa;

    &:hover {
      border-color: #2b5fda;
      background: #f0f7ff;
    }

    .placeholder-icon {
      position: relative;
      width: 80px;
      height: 80px;
      background: #2b5fda;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;

      .add-icon {
        position: absolute;
        bottom: -5px;
        right: -5px;
        background: #52c41a;
        border-radius: 50%;
        padding: 2px;
      }
    }

    .placeholder-text {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
  }

  .personnel-info {
    border: 1px solid #e8eaec;
    border-radius: 8px;
    overflow: hidden;

    .personnel-basic {
      display: flex;
      padding: 20px;
      background: #fff;
      border-bottom: 1px solid #f0f0f0;

      .personnel-photo {
        width: 80px;
        height: 100px;
        border-radius: 6px;
        object-fit: cover;
        margin-right: 15px;
        border: 1px solid #e8eaec;
      }

      .personnel-details {
        flex: 1;

        .personnel-name {
          font-size: 18px;
          font-weight: bold;
          color: #2b5fda;
          margin: 0 0 8px 0;
        }

        .personnel-room {
          font-size: 14px;
          color: #52c41a;
          margin: 0 0 10px 0;
          font-weight: 500;
        }

        p {
          margin: 4px 0;
          font-size: 13px;
          color: #666;

          span:first-child {
            color: #999;
            margin-right: 5px;
          }
        }
      }
    }

    .personnel-case {
      background: #f8f9fa;
      padding: 15px 20px;

      .case-label {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #e8eaec;
      }

      .case-details {
        p {
          margin: 4px 0;
          font-size: 13px;
          color: #666;

          span:first-child {
            color: #999;
            margin-right: 5px;
            min-width: 70px;
            display: inline-block;
          }
        }
      }
    }
  }
}

// 表单内容样式
.form-content {
  padding: 20px;

  .contraband-types {
    /deep/ .ivu-checkbox-wrapper {
      margin-bottom: 10px;
      width: 100%;
    }
  }

  /deep/ .ivu-checkbox-group {
    width: 100%;
  }

  /deep/ .ivu-form-item {
    margin-bottom: 18px !important; 
    position: relative;
  }

  // 错误提示样式优化
  /deep/ .ivu-form-item-error-tip {
    position: absolute;
    top: 100%;
    left: 0;
    line-height: 1.5;
    padding-top: 4px;
    color: #ed4014;
    font-size: 12px;
    animation: fadeInUp 0.3s ease;
    z-index: 10;
  }

  // 特殊字段的间距调整
  /deep/ .ivu-form-item:has(.ivu-checkbox-group) {
    margin-bottom: 40px; // 多选框需要更多空间
  }

  /deep/ .ivu-form-item:has(.ivu-input[type="textarea"]) {
    margin-bottom: 40px; // 文本域需要更多空间
  }
}

// 详情页面样式
.contraband-detail {
  width: 100%;
  display: flex;

  .contraband-detail-left {
    margin-left: 16px;
    width: 400px;
    border-right: 1px solid #dcdee2;
  }

  .contraband-detail-right {
    width: calc(~'100% - 400px');
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
