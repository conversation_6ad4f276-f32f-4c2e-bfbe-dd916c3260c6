<template>
  <div>
    <ui-condition-box v-model="param"  :instance="$refs['uiCardTable']">
      <ui-condition-item label="申诉标题" prop="appealTitle">
        <ui-input v-model="param.appealTitle"    class="com-condition-width-1"></ui-input>
      </ui-condition-item>
      <ui-condition-item label="申诉人" prop="appealName">
        <ui-input v-model="param.appealName"    class="com-condition-width-1"></ui-input>
      </ui-condition-item>
      <ui-condition-item label="申诉时间" :prop="['appealStartTime','appealEndTime']">
        <ui-date-picker
          class="com-condition-width-2"
          type="datetimerange"
          v-model="param.appealStartTime"
          :end-time="param.appealEndTime"
          @end-change="param.appealEndTime = $event"
        ></ui-date-picker>
      </ui-condition-item>
    </ui-condition-box>
    <com-hr/>
    <div class="com-table-wrapper">
      <ui-card-table
        :height="$getTableHeight()"
        :get-data-method="operate"
        :get-data-param="param"
        :data.sync="tableData"
        ref="uiCardTable"
        @on-change="changeSelect"
      >
        <div slot="operateBox">
          <ui-button :disabled="selectData.length === 0"  @click="handleSend(selectData)">提交</ui-button>
          <ui-button :disabled="selectData.length === 0" type="danger" @click="handleDelete(selectData)">删除</ui-button>
        </div>
        <ui-table slot="table" full  :columns="columns" :data="tableData" @on-selection-change="tableSelect">
          <template slot="operate"   slot-scope="{row}" >
            <span class="com-table-btn"  @click="toPage(row)">信息补充</span>
          </template>
        </ui-table>
        <div slot="card" class="table-card-container">
          <div class="table-card-item" v-for="item in tableData" :key="item.id">
            <div class="table-card-head-box">
              <ui-checkbox  class="head-check" v-model="checkData[item.id]" @on-change="cardSelect"></ui-checkbox>
              <div class="head-item">
                <label>申诉标题：</label>
                <span>{{item.title}}</span>
              </div>
            </div>
            <div class="table-card-content-box">
              <div class="content-row">
                <div class="content-item">
                  <label>申诉人：</label>
                  <span>{{item.representUserName}}</span>
                </div>
                <div class="content-item">
                  <label>申诉时间：</label>
                  <span>{{item.representTime}}</span>
                </div>
                <div class="content-item">
                  <label>关联督导数：</label>
                  <span>{{item.countNumber}}</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-item">
                  <label>申诉理由：</label>
                  <span>{{item.representReason}}</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-item w100">
                  <label>附件：</label>
                  <ui-multi-uploader :value="trainsFromData(item.files)" readonly/>
                </div>
              </div>
              <div class="content-operate">
                <div class="operate">
                  <ui-button @click="toPage(item)" class="mr-10">信息补充</ui-button>
                  <ui-button type="danger" @click="handleDelete([item])">删除</ui-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ui-card-table>
    </div>
  </div>
</template>

<script>
import {fileTranslate} from "@/util";
import { toBeRepresentList, sendRepresentForm, delRepresentForm } from "@/axios/zhjgBranchWork";
import {selectData} from "@/mixins";
export default {
  data() {
    return {
      userInfo: this.$store.state.userInfo,
      columns: [
        {
          type: "selection",
          width: 60,
          align: "center"
        },
        {
          title: "序号",
          key: "index",
          width: 80,
        },
        {
          title: "申诉单位",
          key: "unitName",
        },
        {
          title: "标题",
          key: "title",
        },

        {
          title: "申诉人",
          key: "representUserName",
        },
        {
          title: "申诉理由",
          key: "representReason",
        },
        {
          title: "申诉时间",
          key: "representTime",
          width: 220,
        },
        {
          title: "操作",
          slot: "operate",
          width: 180,
        },
      ],
      tableData: [],
      param: {},
    };
  },
  mixins: [selectData],
  methods: {
    toPage(item) {
      this.$toPage({
        name: this.$hasAuth("JSDD_SPSS") ? "prisonApprovalComplain" : "prisonSendComplain",
        params: {row: item}
      });
    },
    handleSend(arr) {
      let roleList = this.userInfo.roleList;
      let userJobs = roleList.map(item => item.rolename).join(",");
      let id = arr.map(item => item.id).join(",");
      let param = {
        id,
        userJobs,
        processType: "1", // 流程类型（0：支队，1：监所)
        userName: this.userInfo.name,
        userId: this.userInfo.userid,
      };
      this.$Modal({
        title: "信息提醒",
        content: "是否确认发送申诉单至支队?",
        confirm: () => {
          sendRepresentForm(param).then(() => {
            this.$emit("refreshNum");
            this.$refs.uiCardTable.changePageNumber(1);
            this.$operateSuccessMessage();
          }).catch(err => this.$messageError(err));
        },
      });
    },
    handleDelete(arr) {
      this.$Modal({
        title: "信息提醒",
        content: "是否确定要删除申诉单吗?",
        confirm: () => {
          let id = arr.map(item => item.id).join(",");
          delRepresentForm(id).then(() => {
            this.$emit("refreshNum");
            this.$refs.uiCardTable.changePageNumber(1);
            this.$operateSuccessMessage();
          }).catch(err => this.$messageError(err));
        },
      });
    },
    operate(param) {
      param.unitId = this.userInfo.prisonId;
      param.unitName = this.userInfo.prisonName;
      param.userId =  "";
      param.status = "0";
      param.type = "1";
      this.resetSelect();
      return toBeRepresentList(param);
    },
    trainsFromData(data) {
      return fileTranslate(data);
    },
  },
};
</script>
<style lang="less">
  @import "../../assets/styles/component/tableCard.less";
</style>