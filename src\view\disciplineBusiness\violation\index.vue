<template>

    <div>
        <s-DataGrid ref="grid" funcMark="wgdj" :customFunc="true" v-if="!vioModal">
            <template slot="customHeadFunc" slot-scope="{ func }">
                <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':wgdj:add')"
                    @click.native="handleAdd('add')">新增</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func,row,index }">
                <Button type="primary" @click.native="handleDetails(row)" size="small"
                    v-if="func.includes(globalAppCode + ':wgdj:details')">详情</Button>
            </template>
        </s-DataGrid>
        <div v-if="vioModal" style="height: 100%" class="vioModal">
            <Form ref="formData" :model="formData" :label-width="120" :rules="rules">
                <Row type="flex" justify="center">
                    <Col span="8">
                    <FormItem label="监室号" prop="roomName">
                        <Input v-model="formData.roomName" readonly placeholder="请选择监室号">
                        <template slot="append">
                            <Button @click="openPrison = true">选择</Button>
                        </template>
                        </Input>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="被监管人员姓名" :label-width="140" prop="jgryxm">
                        <Input v-model="formData.jgryxm" readonly placeholder="请输入被监管人员姓名"></Input>
                    </FormItem>
                    </Col>
                    <Col span="16">
                    <FormItem label="违规内容" prop="violationContent">
                        <Poptip placement="bottom" style="width: 100%" :word-wrap="true"  ref="poptip">
                            <editor-vue v-if="vioModal" class="editor" ref="editorVue" :content="content"
                                @changeData="hChangeData" />
                            <div slot="content" class="tipContent">
                                <div class="tipContent-item" v-for="(item, index) in tipText" :key="item.code"
                                    @click="tipClick(item.name)">{{ item.name }}
                                </div>
                            </div>
                        </Poptip>
                    </FormItem>
                    </Col>
                    <Col span="16">
                    <FormItem label="处置情况">
                        <Input v-model="formData.disposalSituation" type="textarea" :row="3" placeholder="请输入"></Input>
                    </FormItem>
                    <FormItem label="是否岗位协同" prop="isPostCoordination">
                        <RadioGroup v-model="formData.isPostCoordination" @on-change="changeIsPostCoordination">
                            <Radio label="1">是</Radio>
                            <Radio label="0">否</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem label="岗位协同" v-if="formData.isPostCoordination == '1'">
                        <CheckboxGroup v-model="formData.coordinationPosts" @on-change="changecoordinationPosts">
                            <Checkbox v-for="(item, index) in coordinationPostsList" :key="item.code"
                                :label="item.code">{{
                                    item.name }}</Checkbox>
                        </CheckboxGroup>
                    </FormItem>
                    <FormItem label="推送对象"
                        v-if="formData.coordinationPosts.includes('06') && formData.isPostCoordination == '1'">
                        <user-selector v-model="formData.pushTargetIdCards" tit="用户选择" @onSelect="onSelectUs"
                            :text.sync="formData.pushTarget" returnField="id">
                        </user-selector>
                    </FormItem>
                    <FormItem label="推送内容" v-if="formData.isPostCoordination == '1'">
                        <Input placeholder="请输入推送内容" v-model="formData.pushContent"></Input>
                    </FormItem>
                    <FormItem label="上传附件">
                        <file-upload :defaultList="fileList" :serviceMark="serviceMark" :bucketName="bucketName"
                            :beforeUpload="beforeUpload" @fileSuccess="fileSuccessFile" @fileRemove="fileRemoveFile"
                            @fileComplete="fileCompleteFile" v-if="showFile" />
                    </FormItem>
                    </Col>

                </Row>
                <Row type="flex" justify="center">
                    <Col span="5">
                    <FormItem label="巡控室" prop="patrolRoomId">
                        <Select v-model="formData.patrolRoomId" @on-change="patrolChange" :label-in-value="true">
                            <Option v-for="item in patrolRoomList" :value="item.id" :key="item.id">{{ item.areaName
                            }}
                            </Option>
                        </Select>
                    </FormItem>
                    </Col>
                    <Col span="5">
                    <FormItem label="登记人">
                        <Input disabled v-model="formData.operatorXm"></Input>
                    </FormItem>
                    </Col>
                    <Col span="6">
                    <FormItem label="登记时间">
                        <DatePicker v-model="formData.operatorTime" type="datetime" placeholder="请选择"></DatePicker>
                    </FormItem>
                    </Col>
                </Row>
            </Form>
            <div class="bsp-base-fotter">
                <Button @click="refreshFrom">返 回</Button>
                <Button type="primary" :loading="btnloading" @click="handleSubmit">提 交</Button>
            </div>
            <Modal v-model="openPrison" :mask-closable="false" :closable="true" class-name="select-use-modal"
                width="1360" title="人员列表">
                <div class="select-use">
                    <prisonSelect ref="prisonSelect" ryzt="ALL" :isMultiple='false' :selectUseIds="formData.jgrybm" />
                </div>
                <div slot="footer">
                    <Button type="primary" @click="useSelect" class="save">确 定</Button>
                    <Button @click="openPrison = false" class="save">关 闭</Button>
                </div>
            </Modal>
        </div>


        <!-- <div v-if="vioModaldetails" style="height: 100%" class="vioModaldetails fm-content-info">
            
            <div class="bsp-base-fotter">
                <Button @click="vioModaldetails = false; detailsMsg = {}">返 回</Button>
            </div>
        </div> -->
        <Modal v-model="vioModaldetails" width="60%" title="违规登记详情">
            <div class="fm-content-info">
                <div class="fm-content-box">
                    <!-- <p class="fm-content-info-title">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />违规登记详情
                </p> -->
                    <Row>
                        <Col span="3"><span>监室号</span></Col>
                        <Col span="9"><span>{{ detailsMsg.roomName }}</span></Col>
                        <Col span="4"><span>被监管人员姓名</span></Col>
                        <Col span="8"><span>{{ detailsMsg.jgryxm }}</span></Col>
                        <Col span="3"><span>违规内容</span></Col>
                        <Col span="21"><span>{{ detailsMsg.violationContent }}</span></Col>
                        <Col span="3"><span>是否岗位协同</span></Col>
                        <Col span="21"><span>{{ detailsMsg.isPostCoordination == '1' ? '是' : '否'
                            }}</span></Col>
                    </Row>
                    <Row v-if="detailsMsg.isPostCoordination == '1'">
                        <Col span="3"><span>岗位协同</span></Col>
                        <Col span="21"><span>
                            <el-tag v-for="(item, index) in (detailsMsg?.coordinationPostsName || '').split(',')"
                                :key="index" style="margin-right: 8px;">{{ item }}</el-tag>
                        </span></Col>
                    </Row>
                    <Row
                        v-if="(detailsMsg?.coordinationPosts || '').split(',').includes('06') && detailsMsg.isPostCoordination == '1'">
                        <Col span="3"><span>推送对象</span></Col>
                        <Col span="21"><span>{{ detailsMsg.pushTarget }}</span></Col>
                    </Row>
                    <Row v-if="detailsMsg.isPostCoordination == '1'">
                        <Col span="3"><span>推送内容</span></Col>
                        <Col span="21"><span>{{ detailsMsg.pushContent }}</span></Col>
                    </Row>
                    <Row>
                        <Col span="3"><span>上传附件</span></Col>
                        <Col span="21"><span>
                            <file-upload :defaultList="detailsMsg.attachment" :serviceMark="serviceMark"
                                :bucketName="bucketName" :isDetail="true" v-if="detailsMsg.attachment" />
                        </span></Col>
                        <Col span="3"><span>巡控室</span></Col>
                        <Col span="5"><span>{{ detailsMsg.patrolRoomName }}</span></Col>
                        <Col span="3"><span>登记人</span></Col>
                        <Col span="4"><span>{{ detailsMsg.operatorXm }}</span></Col>
                        <Col span="3"><span>登记时间</span></Col>
                        <Col span="6"><span>{{ detailsMsg.operatorTime }}</span></Col>
                    </Row>
                </div>
            </div>
            <div slot="footer">
                <Button @click="vioModaldetails = false; detailsMsg = {}" class="save">关 闭</Button>
            </div>
        </Modal>

    </div>

</template>

<script>
import { mapActions } from 'vuex'
import Cookies from "js-cookie";
import editorVue from "@/components/wangEditor/index.vue";
import { sDataGrid } from 'sd-data-grid'
import { prisonSelect } from "sd-prison-select"
import { formatDateparseTime, getUserCache } from "@/libs/util.js"
import { userSelector } from 'sd-user-selector'
import { fileUpload } from 'sd-minio-upfile'
export default {
    components: {
        sDataGrid,
        prisonSelect,
        editorVue,
        userSelector,
        fileUpload
    },
    data() {
        return {
            btnloading: false,
            vioModal: false,
            vioModaldetails: false,
            openPrison: false,
            formData: {
                operatorTime: new Date(),
                isPostCoordination: '0',
                operatorXm: getUserCache.getUserName(),
                operatorSfzh: getUserCache.getIdCard(),
                coordinationPosts: [],
                jgrybm: '',
                jgryxm: '',
                pushTarget: '',
                pushTargetIdCard: '',
                pushTargetIdCards: '',
                roomId: '',
                pushContent: '',
                disposalSituation: '',
                attachment: '',
                violationContent: '',
                roomName: '',
                patrolRoomId: ''
            },
            content: '',
            tipText: [],
            contentItems: [],
            coordinationPostsList: [],
            patrolRoomList: [],
            rules: {
                violationContent: [
                    { required: true, message: '请输入违规内容', trigger: 'blur' },
                ],
                isPostCoordination: [
                    { required: true, message: '请选择', trigger: 'change' }
                ],
                roomName: [{ required: true, message: '请选择监室号', trigger: 'change' }],
                jgryxm: [
                    { required: true, message: '请选择被监管人员姓名', trigger: 'change' }
                ],
                patrolRoomId: [
                    { required: true, message: '请选择巡察室', trigger: 'change' }
                ],
            },

            // 文件上传
            showFile: true,
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            fileList: [],     // 违禁品照片列表


            detailsMsg: {},

        }
    },
    mounted() {
        this.handleGetZD_XSGKXTGW()
        this.getAreaByAreaType()
        this.handleGetZD_WGDJWGNR()
        

    },
    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        handleAdd() {
            this.vioModal = true
            this.$nextTick(() => {
            // 强制重新计算 Poptip 宽度
             
            
            if (this.$refs.poptip) {
               console.log("强制重新计算 Poptip 宽度",this.$refs.poptip);
                this.$refs.poptip.updatePopper();
            }
        });
        },
        handleDetails(row) {
            this.vioModaldetails = true
            console.log(row);
            this.getDetailsData(row.id)

        },
        // 获取巡控室
        getAreaByAreaType() {
            this.authPostRequest({ url: this.$path.represent_getAreaByAreaType, params: {} }).then(res => {
                if (res.success) {
                    this.patrolRoomList = res.data
                }
            }
            )
        },
        patrolChange(e) {
            this.formData.patrolRoomName = e.label

        },
        useSelect() {
            if (this.$refs.prisonSelect.checkedUse && this.$refs.prisonSelect.checkedUse.length > 0) {
                let people = this.$refs.prisonSelect.checkedUse[0]
                console.log(people.roomName, people.xm, people.jsh, people.jgrybm)
                this.formData.roomName = people.roomName
                this.formData.jgryxm = people.xm
                this.formData.jgrybm = people.jgrybm
                this.formData.roomId = people.jsh
                // this.formData.pushContent = people.roomName + people.xm
                this.openPrison = false
            } else {
                this.$Notice.warning({
                    title: '提示',
                    desc: '请选择人员!'
                })
            }
        },
        tipClick(item) {

            this.contentItems.push(item);
            this.content = this.contentHtml()
        },
        contentHtml() {
            return `<p>${this.contentItems.join('，')}</p>`;
        },
        hChangeData(editDataHtml, editData) {
            this.formData.violationContentHtml = editDataHtml
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = editDataHtml; // 将 HTML 内容赋值给临时元素
            const pureText = tempDiv.innerText || tempDiv.textContent; // 提取纯文本
            // 将纯文本赋值给 infoContentText
            this.formData.violationContent = pureText;

            // const result = this.coordinationPostsList.filter(item => this.formData.coordinationPosts.includes(item.code)).map(item => item.name);
            // if (pureText) {
            //     this.formData.pushContent = (this.formData.roomName || '') + (this.formData.jgryxm || '') + pureText + ',请' + (result ? result.join(",") : '') + '查看情况'
            // }

            // 获取最新的html数据
            // this.content = editDataHtml;
            console.log(this.formData);
            console.log(pureText, this.formData.pushContent);
        },
        handleGetZD_XSGKXTGW() {
            this.$store
                .dispatch("authGetRequest", {
                    url: "/bsp-com/static/dic/acp/ZD_XSGKXTGW.js",
                })
                .then((res) => {
                    let scales = eval("(" + res + ")");
                    this.coordinationPostsList = scales();
                });
        },
        handleGetZD_WGDJWGNR() {
            this.authGetRequest({ url: "/bsp-com/static/dic/acp/ZD_WGDJWGNR.js" }).then(res => {
                let scales = eval('(' + res + ')')
                this.tipText = scales()
            })
        },
        handleSubmit() {
            console.log(this.formData, this.$path.represent_violationCreate);

            this.$refs.formData.validate((valid) => {
                if (valid) {
                    const params = { ...this.formData };
                    delete params.pushTargetIdCards;
                    // delete params.roomName;
                    const result = this.coordinationPostsList.filter(item => this.formData.coordinationPosts.includes(item.code)).map(item => item.name);
                    params.coordinationPostsName = result.join(",");
                    params.coordinationPosts = this.formData.coordinationPosts.join(",");
                    params.operatorTime = formatDateparseTime(this.formData.operatorTime)
                    params.pushTargetIdCard = JSON.stringify(this.formData.pushTargetIdCard)
                    console.log(params);
                    this.btnloading = true
                    this.authPostRequest({ url: this.$path.represent_violationCreate, params: params }).then(res => {
                        if (res.success) {
                            this.$Message.success('添加成功')
                            this.refreshFrom()
                            this.btnloading = false
                            this.on_refresh_table()
                        } else {
                            this.$Message.error(res.message)
                            this.btnloading = false
                        }
                        // this.btnloading = false
                    }
                    )
                }
            });


        },
        onSelectUs(data) {
            const res = data.map(item => {
                return {
                    orgCode: item.orgCode,
                    idCard: item.idCard
                }
            }
            )
            this.formData.pushTargetIdCard = res;
            this.pushText()
        },
        fileCompleteFile(data) {
            this.fileList = data || []
            this.formData.attachment = data && data.length > 0 ? JSON.stringify(data) : ''
        },
        refreshFrom() {
            this.vioModal = false
            this.btnloading = false
            this.formData = {

                operatorTime: new Date(),
                isPostCoordination: '0',
                operatorXm: getUserCache.getUserName(),
                operatorSfzh: getUserCache.getIdCard(),
                coordinationPosts: [],
                jgrybm: '',
                jgryxm: '',
                pushTarget: '',
                pushTargetIdCard: '',
                pushTargetIdCards: '',
                roomId: '',
                pushContent: '',
                disposalSituation: '',
                attachment: '',
                violationContent: '',
                roomName: ''

            }
            this.fileList = []
            this.content = ''
        },

        getDetailsData(id) {
            this.authGetRequest({ url: this.$path.represent_violationGet, params: { id: id } }).then(res => {
                if (res.success) {
                    this.detailsMsg = res.data;
                    this.detailsMsg.attachment = JSON.parse(this.detailsMsg.attachment)
                    console.log(this.detailsMsg.attachment);

                }

            })
        },

        on_refresh_table() {
            this.$refs.grid.query_grid_data(1)
        },
        changeIsPostCoordination(val) {
            this.formData.coordinationPosts = []
            this.formData.pushTarget = ''
            this.formData.pushTargetIdCard = ''
            this.formData.pushContent = ''
        },
        changecoordinationPosts() {
            this.pushText()
        },
        pushText() {
            if (this.formData.violationContent && this.formData.isPostCoordination == '1') {

                const result = this.coordinationPostsList.filter(item => this.formData.coordinationPosts.includes(item.code)).map(item => item.name);
                this.formData.pushContent = (this.formData.roomName || '') + (this.formData.jgryxm || '') + (this.formData.violationContent || '') + ',请' + (result ? result.join(",") : '') + '查看情况'
            }

        },




        /**
    * 文件上传成功回调
    */
        fileSuccessFile(data) {
            // 文件上传成功处理
        },

        /**
         * 文件删除回调
         */
        fileRemoveFile(data) {
            // 文件删除处理
        },
        beforeUpload() { },

    }
}


</script>

<style scoped lang="less">
.vioModal {
    padding-bottom: 40px;
    /deep/.ivu-poptip-popper {
        left: 0 !important;
    }
    /deep/.ivu-poptip-body-content {
        height: 160px;
    }
    /deep/.tipContent {
        display: flex;
        flex-wrap: wrap;

        .tipContent-item {
            margin-right: 20px;
            margin-bottom: 10px;
            height: 30px;
            cursor: pointer;
            line-height: 30px;
            padding: 0 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;

            &:hover {
                box-shadow: 0px 1px 4px 0px #cdd0d5;
            }
        }

    }

    /deep/.ivu-poptip-rel {
        display: block;
    }

    /deep/.ivu-input-group-append {
        color: #fff;
        background-color: #2b5fd9;
    }
}

.vioModaldetails {
    width: 65%;
    margin: auto;
    padding-bottom: 30px;

    /deep/.item-content {
        line-height: 1;
        padding: 10px 0;
        font-size: 16px;
    }

    .item-tag {
        /deep/.ivu-tag-text {
            max-width: 120px !important;
        }

    }

    /deep/.ivu-form-item-label {
        font-weight: bold;
    }


}
</style>