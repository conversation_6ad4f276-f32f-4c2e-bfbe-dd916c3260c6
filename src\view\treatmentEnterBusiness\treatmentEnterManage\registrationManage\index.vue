<template>
  <div>
    <div class="treatmentEnterBoxData" v-if="!showData">
      <statisticalAnalysis mark='acpkssjyywrsbrsys'  />
      <statisticalAnalysis mark='acpkssjyywrsbzsys'  />
      <statisticalAnalysis mark='acpkssjyywrsbysys'  />
      <statisticalAnalysis mark='acpkssjyywrsbjdsys'  />
      <statisticalAnalysis mark='acpkssjyywrsbnsys'  />
      <statisticalAnalysis mark='acpkssjyywrslssys'  />
    </div>
    <div class="bsp-base-form" :class="!showData ? 'data-content' : ''" style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData && !showDetail">
        {{ modalTitle }}
      </div>

      <div class="bsp-base-content" v-if="!showData">

        <s-DataGrid ref="grid" funcMark="syrskss" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">
            <Button type="primary" v-if="func.includes(globalAppCode+':syrskss:addzcrs')" @click.native="addEvent(false)">正常收治</Button>
            <Button type="primary" v-if="func.includes(globalAppCode+':syrskss:ksrs')" style="margin-left: 20px;" @click.native="addEvent(true)">快速收治</Button>
          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Dropdown>
              <a href="javascript:void(0)">
                操作
                <Icon type="ios-arrow-down"></Icon>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem v-if="func.includes(globalAppCode+':syrskss:sydj') && row.current_step == '01'" @click.native="editEvent(row,false)">
                  {{row.status == "01" ? '正常收治':'编辑'}}
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode+':syrskss:jkjc') && row.current_step == '02'" @click.native="editEvent(row,false)">
                  {{row.jkjcstatus == "01" ? '检查登记':'编辑'}}
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode+':syrskss:swxxcj') && row.current_step == '03'" @click.native="editEvent(row,false)">
                  {{row.xxcjstatus == "01" ? '采集':'编辑'}}
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode+':syrskss:sswpdj') && row.current_step == '04'" @click.native="editEvent(row,false)">
                  {{row.wpglstatus == "01" ? '存物':'编辑'}}
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode+':syrskss:ldsp') && row.current_step == '05'" @click.native="editEvent(row,false)">
                  审批
                </DropdownItem>
                <!--                <DropdownItem v-if="func.includes(globalAppCode+':syrskss:sydj') && row.current_step != '06'" @click.native="editEvent(row,false)">-->
                <!--                  {{getStepName(row)}}-->
                <!--                </DropdownItem>-->
                <DropdownItem v-if="func.includes(globalAppCode+':syrs:xq') && row.current_step == '06'" @click.native="showInfo(row)">
                  详情
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode+':syrs:wsdy') && row.current_step == '06'" @click.native="archive(row)">
                  文书打印
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode+':syrskss:ksrs') && row.current_step == '01' && row.rslx != '01' && !(row.rslx == '02' && row.current_step == '01' && row.status != '01')" @click.native="editEvent(row,true)">
                  快速收治
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode+':syrskss:xxbl') && ((row.rslx == '02'  && (row.current_step != '01' && row.current_step != '05' && row.current_step != '06')) || row.rslx == '02' && row.current_step == '01' && row.act_inst_id)" @click.native="supplementInfo(row)">
                  信息补录
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>

          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>
<!--      <addForm v-if="showData" @close="showData=false" :saveType="saveType" :quickRegistration="quickRegistration" :rowData="rowData"/>-->
      <treatmentEnterMain v-if="showData && !showDetail" @close="closeEvent()" :saveType="saveType" :quickRegistration="quickRegistration" :rowData="rowData"/>
    </div>
    <allDetail v-if="showData && showDetail" @close="closeEvent()" :rowData="rowData"/>
  </div>
</template>

<script>
  import  {sDataGrid}  from  'sd-data-grid'
  import addForm from "./addForm.vue"
  import treatmentEnterMain from '../treatmentEnterMain'
  import {statisticalAnalysis }  from 'sd-statistical-analysis'
  import {mapActions,mapMutations} from "vuex";
  import allDetail from "../recordManage/allDetail"
  export default {
    components: {
      sDataGrid,
      addForm,
      statisticalAnalysis,
      treatmentEnterMain,
      allDetail
    },
    data() {
      return {
        params:{},
        showData: false,
        modalTitle: '收治登记',
        saveType: 'add',
        quickRegistration:false,
        rowData:{},
        stepName:"收治登记",
        showDetail: false,
      }
    },
    methods: {
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      ...mapMutations([ 'setTagNavList',]),
      addEvent(quickRegistration) {
        this.modalTitle = "收治登记"
        this.saveType='add'
        this.quickRegistration = quickRegistration
        this.showData=true
        this.showDetail = false
        this.rowData={}
      },
      editEvent(row,quickRegistration){
        this.modalTitle = "收治登记"
        this.rowData = row;
        this.showDetail = false
        this.showData=true
        this.saveType='edit'
        if(row.rslx == "02" && row.current_step == "05"){
          quickRegistration = true  // 快速入所审批时，不允许显示全部流程   快速入所其他流程时要显示全部流程进行信息补录
        }else  if(row.rslx == '02' && row.current_step == '01' && row.status != '01'){
          quickRegistration = true  // 快速入所登记编辑状态，再次进入编辑，不允许显示全部流程   快速入所其他流程时要显示全部流程进行信息补录
        }
        if(row.rslx == "02" && row.current_step == '01' && row.act_inst_id){
          quickRegistration = false  // 快速入所必填项减少了，所以需要重新进行基础信息的补录  补录时显示全部流程
        }
        this.quickRegistration = quickRegistration
      },
      getStepName(row){

        if(row.current_step == "01"){
          if(row.status == "01"){
            return "收押登记"
          }else{
            return "编辑"
          }
        }else if(row.current_step == "02"){
          if(row.jkjcstatus == "01"){
            return "检查登记"
          }else{
            return "编辑"
          }
        }else if(row.current_step == "03"){
          if(row.xxcjstatus == "01"){
            return "采集"
          }else{
            return "编辑"
          }
        }else if(row.current_step == "04"){
          if(row.wpglstatus == "01"){
            return "存物"
          }else{
            return "编辑"
          }
        }else if(row.current_step == "05"){
          return "审批"
        }else{
          return "收押登记"
        }
      },
      supplementInfo(row){
        this.rowData = row;
        this.showData=true
        this.saveType='edit'
        this.quickRegistration = false  // 信息补录时需要显示全部流程,所以设置为false
      },
      showInfo(row){
        this.modalTitle = "详情"
        this.rowData = row;
        this.showData=true
        this.showDetail = true
      },
      closeEvent(){
        this.showData=false
        this.showDetail = false
      },
      archive(row) {
        this.$router.push({
          path:'/archive',
          query: {
            id:row.id,
            dicCode:"ZD_SYRSWSDY",
            formId:"1933800910857834496"
          },
        })
        let res={
          path:'/archive',
          name: "archive",
          meta: {hideInMenu: true, title: "收治台账-文书打印", notCache: true, icon: ""}
        }
        let arr=localStorage.getItem('tagNaveList')?JSON.parse(localStorage.getItem('tagNaveList')):[]
        arr.unshift(res)
        this.setTagNavList(arr)
      },
      getDetail(){
        let rybh = this.$route.query.eventCode;
        this.rowData = {}
        this.rowData.current_step = "05"
        this.rowData.rybh = rybh
        this.showDetail = false
        this.showData=true
        this.saveType='edit'
        this.modalTitle = "收治登记"
        this.$store.dispatch('authPostRequest',{
          url: this.$path.app_detainRegKssGetList,
          params: {
            rybh:rybh
          }
        }).then(resp => {
          if(resp.code == 0){
            // this.formData = resp.data
            if(resp.data && resp.data.length > 0){
              this.modalTitle = "收治登记"
              this.rowData = resp.data[0]
              this.rowData.current_step = this.rowData.currentStep
              this.rowData.act_inst_id = this.rowData.actInstId
              this.showDetail = false
              this.showData=true
              this.saveType='edit'
              let quickRegistration = false
              if(this.rowData.rslx == "02" && this.rowData.current_step == "05"){
                quickRegistration = true  // 快速入所审批时，不允许显示全部流程   快速入所其他流程时要显示全部流程进行信息补录
              }else  if(this.rowData.rslx == '02' && this.rowData.current_step == '01' && this.rowData.status != '01'){
                quickRegistration = true  // 快速入所登记编辑状态，再次进入编辑，不允许显示全部流程   快速入所其他流程时要显示全部流程进行信息补录
              }
              this.quickRegistration = quickRegistration
            }

          }else{
            this.$Modal.error({
              title: '温馨提示',
              content: resp.msg || '操作失败'
            })
          }
        })
      },
      isToApproval(){
        const eventCode = this.$route.query.eventCode;
        if (eventCode) {
          return true
        } else {
          return false
        }
      },
    },
    mounted(){
      console.log("页面加载----");
      if(this.isToApproval()){
        this.getDetail()
      }
    }
  }
</script>

<style scoped>
  .treatmentEnterBoxData{
    display: flex;
    align-content: center;
  }

</style>
