<template>
  <div class="max-container">
    <div style="width: 100%;height: 100%;">
      <div class="list-container" v-if="listContainer">
        <div class="bsp-base-tit">{{ modalTitle }}</div>
        <div class="tableDetail">
          <div class="header-top">
            <div class="scales-list">
              <span>量表分类：</span>
              <RadioGroup v-model="scalesModel" type="button" button-style="solid" @on-change="changeScalesType">
                <Radio v-for="item in scalesList" :label="item.code" :key="item.code" style="margin-left: 15px;"
                  @dblclick.native="handleDoubleScalesClick(item.code)">{{
                    item.name
                  }}</Radio>
              </RadioGroup>
            </div>
            <div class="useStatus-list">
              <span>使用状态：</span>
              <RadioGroup v-model="useStatusModel" type="button" button-style="solid" @on-change="changeUseStatus">
                <Radio v-for="item in useStatusList" :label="item.code" :key="item.code" style="margin-left: 15px;"
                  @dblclick.native="handleDoubleUseStatusClick(item.code)">
                  {{
                    item.name }}</Radio>
              </RadioGroup>
            </div>
          </div>
          <div class="add-psy-list">
            <div v-for="(item, index) in psyList" class="add-psy-list-container" :key="item.id">
              <div v-if="index == 0" class="add-psy-img">
                <div style="text-align: center;" @click="handleAddFromStep">
                  <img src="@/assets/images/addTable.png" alt="">
                  <h4>创建量表</h4>
                </div>
              </div>
              <div class="card-info" v-else>
                <Card style="width:100%;height: 100%;">
                  <template #title>
                    <div class="title-name">
                      <div>
                        <img v-if="item.tableType == 1" src="../../../assets/images/yyTableIcon.png" alt="">
                        <img v-if="item.tableType == 2" src="../../../assets/images/blTableIcon.png" alt="">
                        <img v-if="item.tableType == 3" src="../../../assets/images/tableTypeIcon.png" alt="">
                      </div>
                      <div class="name-id">
                        <Tooltip :content="item.name" theme="light" max-width="200">
                          <h4>{{ item.name }}</h4>
                        </Tooltip>

                        <p>
                          <span>ID：</span><span style="margin-left: 5px;">{{
                            item.tableNo
                          }}</span>
                        </p>
                      </div>
                    </div>
                  </template>
                  <template #extra>
                    <span v-if="item.usageStatus == '02'" style="color: #2390FF;">{{
                      item.usageStatusName }}</span>
                    <span v-if="item.usageStatus == '03'">{{ item.usageStatusName }}</span>
                    <div v-if="item.usageStatus == '01'" class="unfinished-img"></div>
                    <i-Switch v-if="item.usageStatus != '01'" v-model="item.usageStatusNum" :true-value="1"
                      :false-value="0" @on-change="handleUpdateEableStatu(item)"></i-Switch>
                  </template>
                  <div class="rate-demo">
                    <div class="rate-demo-type">
                      <span>量表类型：</span><span style="margin-left: 5px;">{{ item.tableTypeName
                      }}</span>
                    </div>
                    <div class="rate-demo-type description">
                      <div>量表描述：</div>
                      <Tooltip :content="item.description" theme="light" max-width="400">
                        <span style="margin-left: 5px;">{{
                          item.description
                        }}</span>
                      </Tooltip>
                    </div>
                    <div class="rate-demo-type">
                      <span>题量数量：</span><span style="margin-left: 5px;">{{
                        item.totalQuestionNumber
                      }}</span>
                    </div>
                    <div class="rate-demo-type">
                      <span>预计时常：</span><span style="margin-left: 5px;">{{
                        item.estimatedDuration
                      }}分钟</span>
                    </div>

                  </div>
                  <div class="last-time">
                    <div class="icon-container">
                      <div class="look-icon" @click="handleEditTable(item)">
                        <img src="../../../assets/images/look.png" alt="">
                        <span>编辑</span>
                      </div>
                      <div class="look-icon" v-if="item.usageStatus != '01'" @click="handleDetailsTable(item)">
                        <img src="../../../assets/images/edit.png" alt="">
                        <span>详情</span>
                      </div>
                      <div class="look-icon" @click="handleDeleteTable(item)">
                        <img src="../../../assets/images/delete.png" alt="">
                        <span>删除</span>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="add-form-step" v-if="addFormStep">
        <div class="left-step">
          <h4>创建量表</h4>
          <el-steps direction="vertical" :active="active" class="step-container">
            <el-step title="基础信息配置"></el-step>
            <el-step title="题目配置"></el-step>
            <el-step title="计分规则配置"></el-step>
          </el-steps>
        </div>
        <div class="right-tab-form right-tab-form-first" v-if="firstStep">
          <!-- <h4>基础信息配置</h4> -->
          <div class="add-form-header">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />基础信息配置
            </p>
            <Form ref="baseForm" :model="baseFrom" :rules="baseFromValidate" :label-width="150">
              <Row>
                <Col span="23">
                <FormItem label="量表名称" prop="name" :rules="{ required: true, message: '量表名称不能为空', trigger: 'blur' }">
                  <Input v-model="baseFrom.name" placeholder="请输入量表名称" type="textarea"
                    :autosize="{ minRows: 2, maxRows: 5 }" maxlength="50"></Input>
                </FormItem>
                </Col>
                <Col span="23">
                <FormItem label="量表类型" prop="tableType"
                  :rules="{ required: true, message: '请选择检查时间', trigger: 'change' }">
                  <RadioGroup v-model="baseFrom.tableType">
                    <Radio :label="item.code" v-for="item in scalesList" :key="item.code" style="margin-left: 15px;">
                      {{
                        item.name
                      }}
                    </Radio>
                  </RadioGroup>
                </FormItem>
                </Col>
                <Col span="23">
                <FormItem label="量表描述" prop="description"
                  :rules="{ required: true, message: '请输入量表描述', trigger: 'blur' }">
                  <Input v-model="baseFrom.description" placeholder="请输入量表描述" type="textarea"
                    :autosize="{ minRows: 5, maxRows: 5 }" maxlength="500"></Input>
                </FormItem>
                </Col>
                <Col span="23">
                <FormItem label="测评限时(分钟)" prop="estimatedDuration"
                  :rules="{ required: true, type: 'number', message: '请输入测评限时', trigger: 'blur,change' }">
                  <Input v-model.number="baseFrom.estimatedDuration" placeholder="请输入测评限时(分钟)" type="number"></Input>
                </FormItem>

                </Col>
              </Row>
            </Form>
          </div>
          <div class="bsp-base-fotter">
            <Button @click="handleBaseFormCancel('baseForm')">取消</Button>
            <Button @click="handleBaseFormNext('baseForm')">下一步</Button>
            <Button type="primary" @click="handleBaseFormSubmit('baseForm')">保存</Button>
          </div>
        </div>
        <div class="right-tab-form right-tab-form-second" v-if="secondStep">
          <p class="fm-content-wrap-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />
            题目配置
          </p>


          <Tabs :value="tabName" class="tab-container" @on-click="handleChangeQuestionType">
            <TabPane label="全部" name="allQuestion">
              <addTopicType :topicTypeName="radioTopicName"
                :topicNumber="allQuestionList['1'] ? allQuestionList['1'].length : 0"
                @handleAddTopic="handleAddRadioTopic">
                <template v-slot:defaultTopicList v-if="allQuestionList['1'] ? allQuestionList['1'].length > 0 : false">
                  <topicList :data="allQuestionList['1'] ? allQuestionList['1'] : []"
                    @handleUpateQuestion="handleUpateRadioQuestion" @handleDeleteQuestion="handleDeleteRadioQuestion" />
                </template>
              </addTopicType>
              <addTopicType style="margin-top: 20px;" :topicTypeName="checkboxTopicName"
                :topicNumber="allQuestionList['2'] ? allQuestionList['2'].length : 0"
                @handleAddTopic="handleAddCheckboxTopic">
                <template v-slot:defaultTopicList v-if="allQuestionList['2'] ? allQuestionList['2'].length > 0 : false">
                  <topicList :data="allQuestionList['2'] ? allQuestionList['2'] : []"
                    @handleDeleteQuestion="handleDeleteCheckBoxQuestion"
                    @handleUpateQuestion="handleUpateCheckBoxQuestion" />
                </template>
              </addTopicType>
              <addTopicType style="margin-top: 20px;margin-bottom: 20px;" :topicTypeName="shortTopicName"
                :topicNumber="allQuestionList['3'] ? allQuestionList['3'].length : 0"
                @handleAddTopic="handleAddShortTopic">
                <template v-slot:defaultTopicList v-if="allQuestionList['3'] ? allQuestionList['3'].length > 0 : false">
                  <topicList :data="allQuestionList['3'] ? allQuestionList['3'] : []"
                    @handleDeleteQuestion="handleDeleteSimpleQuestion"
                    @handleUpateQuestion="handleUpateSimpleQuestion" />
                </template>
              </addTopicType>
            </TabPane>
            <TabPane label="单选题" name="oneQuestion">
              <addTopicType :topicTypeName="'单选题'" :topicNumber="questionList.length"
                @handleAddTopic="handleAddRadioTopic">
                <template v-slot:defaultTopicList v-if="questionList.length > 0">
                  <topicList :data="questionList" @handleUpateQuestion="handleUpateRadioQuestion"
                    @handleDeleteQuestion="handleDeleteRadioQuestion" />
                </template>
              </addTopicType>
            </TabPane>
            <TabPane label="多选题" name="moreQuestion">
              <addTopicType :topicTypeName="'多选题'" :topicNumber="questionList.length"
                @handleAddTopic="handleAddCheckboxTopic">
                <template v-slot:defaultTopicList v-if="questionList.length > 0">
                  <topicList :data="questionList" @handleDeleteQuestion="handleDeleteCheckBoxQuestion"
                    @handleUpateQuestion="handleUpateCheckBoxQuestion" />
                </template>
              </addTopicType>
            </TabPane>
            <TabPane label="简答题" name="simpleQuestion">
              <addTopicType :topicTypeName="'简答题'" :topicNumber="questionList.length"
                @handleAddTopic="handleAddShortTopic">
                <template v-slot:defaultTopicList v-if="questionList.length > 0">
                  <topicList :data="questionList" @handleDeleteQuestion="handleDeleteSimpleQuestion"
                    @handleUpateQuestion="handleUpateSimpleQuestion" />
                </template>
              </addTopicType>
            </TabPane>
          </Tabs>
          <div class="bsp-base-fotter">
            <Button @click="handleTopicSetPrevious('formValidate')" style="margin-right: 10px;">上一步</Button>
            <Button type="primary" @click="handleTopicSetNext('formValidate')">下一步</Button>
            <Button type="primary" @click="handleTopicSave('formValidate')">保存</Button>
            <Button type="primary" v-if="!questionIsChioced" @click="handlePreview('formValidate')">预览</Button>
          </div>
        </div>
        <div class="right-tab-form right-tab-form-third" v-if="thirdStep">
          <div class="add-form-header">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />计分规则配置
            </p>
            <Form ref="rulesForm" :model="rulesForm" :rules="baseFromValidate" :label-width="150">
              <Row>
                <Col span="23">
                <FormItem label="总分计分规则" prop="scoreRule"
                  :rules="{ required: true, message: '请选择总分计分规则', trigger: 'change' }">
                  <RadioGroup v-model="rulesForm.scoreRule">
                    <Radio :label="'1'">累加总分</Radio>
                    <Radio :label="'3'">无计分</Radio>
                    <Radio :label="'2'">自定义</Radio>
                  </RadioGroup>
                </FormItem>
                </Col>
                <Col span="23" v-if="rulesForm.scoreRule == '2'">
                <FormItem label="自定义计分规则" prop="customScoreRule"
                  :rules="{ required: false, message: '请输入自定义计分规则', trigger: 'blur' }">
                  <Input v-model="rulesForm.customScoreRule" placeholder="请输入自定义计分规则" type="textarea"
                    :autosize="{ minRows: 2, maxRows: 5 }" maxlength="50"></Input>
                </FormItem>
                </Col>
                <Col span="23" v-if="rulesForm.scoreRule == '1'">
                <template v-for="(item, index) in rulesForm.resultInterpRules">
                  <div class="form-list-rules" :key="index">
                    <div class="form-content">
                      <div class="header-form">
                        <div class="time-chioce">
                          <FormItem :label="'结果解释规则'" :prop="'resultInterpRules.' + index + '.min'"
                            :rules="{ required: false }">
                            <Input v-model="item.min" placeholder="分数" style="width: 50px" />
                          </FormItem>
                          <!-- :rules="{ required: true, type: 'date', message: '请选择时间', trigger: 'blur' }" -->
                          <div style="margin-top: -30px;margin-left: 10px;">-
                          </div>
                          <FormItem style="margin-left: -140px;" :prop="'resultInterpRules.' + index + '.max'"
                            :label="''">
                            <Input v-model="item.max" placeholder="分数" style="width: 50px" />
                          </FormItem>
                          <FormItem style="margin-left: -110px;" :prop="'resultInterpRules.' + index + '.result'"
                            :label="''">
                            <Input v-model="item.result" placeholder="结果" style="width: 600px;" />
                          </FormItem>
                        </div>
                      </div>
                    </div>
                    <div class="del-icon" v-if="index >= 1">
                      <i class="el-icon-delete" @click="handleDeleteRulesItem(index)"></i>
                    </div>
                  </div>
                </template>
                </Col>
                <Col span="6" v-if="rulesForm.scoreRule == '1'">
                <FormItem>
                  <Button type="dashed" long @click="handleAddRulesItem" icon="md-add">添加</Button>
                </FormItem>
                </Col>
              </Row>
            </Form>
          </div>
          <div class="bsp-base-fotter">
            <Button @click="handleRulesFormCancel('rulesForm')">上一步</Button>
            <Button @click="handleRulesFormBack('rulesForm')">取消</Button>
            <Button type="primary" @click="handleRulesFormSubmit('rulesForm')">提交</Button>
            <Button type="primary" @click="handlePreview">预览</Button>
          </div>
        </div>
      </div>
    </div>
    <!-- 添加单选框的弹窗 -->
    <Modal v-model="radioTopicVisible" :title="radioTopicName" width="1100">
      <div class="radio-Topic-Visible">
        <div class="fm-content-wrap" style="border-top:1px solid #cee0f0 ;padding: 0">
          <p class="fm-content-wrap-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />添加{{ radioTopicName }}
          </p>

          <div class="radio-Topic-Visible">
            <Form ref="radioTopicForm" :model="radioTopicForm" :label-width="150">
              <Row>
                <Col span="23">
                <FormItem label="题目内容" prop="questionTextHtml"
                  :rules="[{ trigger: 'blur', message: '请填写', required: true }]">
                  <editorVue class="editor" ref="radioEditor" :content="radioTopicForm.questionTexcontent"
                    @changeData="hChangeData" />
                </FormItem>
                </Col>
                <Col span="23">
                <FormItem label="单题计分规则" prop="scoreRule"
                  :rules="{ required: true, message: '请选择单题计分规则', trigger: 'change' }">
                  <RadioGroup v-model="radioTopicForm.scoreRule" vertical>
                    <Radio v-for="item in useRadioTypeList" :label="item.code" :key="item.code">{{
                      item.name
                    }}
                    </Radio>
                  </RadioGroup>
                </FormItem>
                </Col>
                <Col span="23">
                <template v-for="(item, index) in radioTopicForm.options">
                  <div class="form-list" :key="index">
                    <div class="form-content">
                      <div class="header-form">
                        <div class="time-chioce">
                          <FormItem :label="item.optionCodeName" :prop="'options.' + index + '.optionText'"
                            :rules="{ required: false, message: '请输入选项值', trigger: 'blur' }">
                            <editorVue class="editor" ref="editorVue" :content="item.optionText"
                              @changeData="(content) => handleEditorChange(content, index)" />
                          </FormItem>

                          <FormItem v-if="radioTopicForm.scoreRule == '01'" :prop="'options.' + index + '.score'"
                            :rules="{ required: true, type: 'number', message: '请输入分值', trigger: 'change' }"
                            :label="'分值'">
                            <InputNumber v-model="item.score" controls-outside :min="0" :max="100" />
                          </FormItem>
                        </div>
                      </div>
                    </div>
                    <div class="icon-list">
                      <Icon type="ios-add-circle" @click="handleAddRadioIcon(index, radioTopicForm)" />
                      <i class="el-icon-delete" @click="handleDeleteRadioIcon(index, radioTopicForm)"></i>
                      <Icon type="md-arrow-dropup-circle"
                        @click="moveItem(index, 'up', radioTopicForm, item.optionText)" :disabled="index === 0" />
                      <Icon type="md-arrow-dropdown-circle"
                        @click="moveItem(index, 'down', radioTopicForm, item.optionText)"
                        :disabled="index === radioTopicForm.options.length - 1" />
                    </div>
                  </div>
                </template>
                </Col>
              </Row>
              <Col span="23" v-if="radioTopicForm.scoreRule == '02'">
              <FormItem label="分值" prop="score"
                :rules="{ required: true, type: 'number', message: '请选择单题计分规则', trigger: 'change' }">
                <InputNumber v-model="radioTopicForm.score" controls-outside :min="0" :max="100" />
              </FormItem>
              </Col>
            </Form>
          </div>
        </div>
      </div>
      <template #footer>
        <Button type="default" @click="handleRadioTopicReset('radioTopicForm')">取消</Button>
        <Button type="primary" @click="handleRadioTopicSubmit('radioTopicForm')">确认</Button>
      </template>
    </Modal>
    <!-- 添加多选框的弹窗 -->
    <Modal v-model="checkboxTopicVisible" :title="checkboxTopicName" width="1100">
      <div class="radio-Topic-Visible">
        <div class="fm-content-wrap" style="border-top:1px solid #cee0f0 ;padding: 0">
          <p class="fm-content-wrap-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />
            添加{{ checkboxTopicName }}
          </p>

          <div class="radio-Topic-Visible">
            <Form ref="checkboxTopicForm" :model="checkboxTopicForm" :label-width="150">
              <Row>
                <Col span="23">
                <FormItem label="题目内容" prop="questionTextHtml"
                  :rules="[{ trigger: 'blur', message: '请填写', required: true }]">
                  <editorVue class="editor" ref="checkboxEditor" :content="checkboxTopicForm.questionTexcontent"
                    @changeData="hChangeDataCkeckbox" />
                </FormItem>
                </Col>
                <Col span="23">
                <FormItem label="单题计分规则" prop="scoreRule"
                  :rules="{ required: true, message: '请选择单题计分规则', trigger: 'change' }">
                  <RadioGroup v-model="checkboxTopicForm.scoreRule" vertical>
                    <Radio v-for="item in useRadioTypeList" :label="item.code" :key="item.code">{{
                      item.name
                    }}
                    </Radio>
                  </RadioGroup>
                </FormItem>
                </Col>
                <Col span="23">
                <template v-for="(item, index) in checkboxTopicForm.options">
                  <div class="form-list" :key="index">
                    <div class="form-content">
                      <div class="header-form">
                        <div class="time-chioce">
                          <FormItem :label="item.optionCodeName" :prop="'options.' + index + '.optionText'"
                            :rules="{ required: false, message: '请输入选项值', trigger: 'blur' }">
                            <editorVue class="editor" ref="editorVue" :content="item.optionText"
                              @changeData="(content) => handleEditorChangeCheckBox(content, index)" />
                          </FormItem>
                          <FormItem v-if="checkboxTopicForm.scoreRule == '01'" :prop="'options.' + index + '.score'"
                            :rules="{ required: true, type: 'number', message: '请输入分值', trigger: 'change' }"
                            :label="'分值'">
                            <InputNumber v-model="item.score" controls-outside :min="0" :max="100" />
                          </FormItem>
                        </div>
                      </div>
                    </div>
                    <div class="icon-list">
                      <Icon type="ios-add-circle" @click="handleAddRadioIcon(index, checkboxTopicForm)" />
                      <i class="el-icon-delete" @click="handleDeleteRadioIcon(index, checkboxTopicForm)"></i>
                      <Icon type="md-arrow-dropup-circle"
                        @click="moveItem(index, 'up', checkboxTopicForm, item.optionText)" :disabled="index === 0" />
                      <Icon type="md-arrow-dropdown-circle"
                        @click="moveItem(index, 'down', checkboxTopicForm, item.optionText)"
                        :disabled="index === checkboxTopicForm.options.length - 1" />
                    </div>
                  </div>
                </template>
                </Col>
                <Col span="23" v-if="checkboxTopicForm.scoreRule == '02'">
                <FormItem label="分值" prop="score"
                  :rules="{ required: true, type: 'number', message: '请选择单题计分规则', trigger: 'change' }">
                  <InputNumber v-model="checkboxTopicForm.score" controls-outside :min="0" :max="100" />
                </FormItem>
                </Col>
                <Col span="23" v-if="checkboxTopicForm.scoreRule != '03'">
                <FormItem label="参考答案" prop="answerArr"
                  :rules="{ required: true, type: 'array', message: '请选择单题计分规则', trigger: 'change' }">
                  <CheckboxGroup v-model="checkboxTopicForm.answerArr">
                    <Checkbox v-for="(item, index) in checkboxTopicForm.options" :label="item.optionCode"
                      :key="item.optionCode">
                    </Checkbox>
                  </CheckboxGroup>
                </FormItem>
                </Col>
              </Row>
            </Form>
          </div>
        </div>
      </div>
      <template #footer>
        <Button type="default" @click="handleCheckBoxTopicReset('checkboxTopicForm')">取消</Button>
        <Button type="primary" @click="handleCheckBoxTopicSubmit('checkboxTopicForm')">确认</Button>
      </template>
    </Modal>
    <!-- 添加简答题的弹框 -->
    <Modal v-model="shortTopicVisible" :title="shortTopicName" width="1100">
      <div class="radio-Topic-Visible">
        <div class="fm-content-wrap" style="border-top:1px solid #cee0f0 ;padding: 0">
          <p class="fm-content-wrap-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />添加{{ shortTopicName }}
          </p>

          <div class="radio-Topic-Visible">
            <Form ref="shortTopicForm" :model="shortTopicForm" :label-width="150">
              <Row>
                <FormItem label="单题计分规则" prop="scoreRule"
                  :rules="{ required: true, message: '请选择单题计分规则', trigger: 'change' }">
                  <RadioGroup v-model="shortTopicForm.scoreRule" vertical>
                    <Radio :label="'02'">计 分：需录入该模块分值</Radio>
                    <Radio :label="'03'">不 计 分：仅做录入，不计分</Radio>
                  </RadioGroup>
                </FormItem>
              </Row>
              <Row>
                <Col span="23">
                <FormItem label="题目内容" prop="questionTextHtml"
                  :rules="[{ trigger: 'blur', message: '请填写', required: true }]">
                  <editorVue class="editor" ref="shortEditor" :content="shortTopicForm.questionTexcontent"
                    @changeData="hChangeDataShort" />
                </FormItem>
                </Col>
                <Col span="23" v-if="shortTopicForm.scoreRule == '02'">
                <FormItem label="分值" prop="score"
                  :rules="{ required: true, type: 'number', message: '请选择单题计分规则', trigger: 'change' }">
                  <InputNumber v-model="shortTopicForm.score" controls-outside :min="1" :max="100" />
                </FormItem>
                </Col>
              </Row>
            </Form>
          </div>
        </div>
      </div>
      <template #footer>
        <Button type="default" @click="handleShortTopicReset('shortTopicForm')">取消</Button>
        <Button type="primary" @click="handleShortTopicSubmit('shortTopicForm')">确认</Button>
      </template>
    </Modal>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { Steps, Step } from 'element-ui'
import addTopicType from './addTopicType.vue'
import editorVue from "@/components/wangEditor/index.vue";
import topicList from './topicList.vue';

export default {
  components: {
    'el-steps': Steps,
    'el-step': Step,
    addTopicType,
    editorVue,
    topicList
  },
  data() {
    return {
      params: {},
      showData: true,
      modalTitle: '测评量表',
      tableType: '',
      scalesModel: "",
      useStatusModel: "",
      scalesList: [],
      useStatusList: [],
      useRadioTypeList: [],
      psyList: [],
      addFormStep: false,
      listContainer: true,
      tabName: 'allQuestion',
      firstStep: true,
      secondStep: false,
      thirdStep: false,
      dtkContainer: false,
      baseFrom: {
        name: "",
        tableType: "",
        description: "",
        id: "",
        usageStatus: '01',
        estimatedDuration: 0
      },
      baseFromValidate: {},
      active: 1,
      radioTopicName: '单选题',
      checkboxTopicName: "多选题",
      shortTopicName: "简答题",
      radioTopicVisible: false,
      checkboxTopicVisible: false,
      shortTopicVisible: false,
      formValidate: {},
      indexNum: 0,
      sortNum: 1,
      radioTopicForm: {
        questionTexcontent: "",
        questionTextHtml: "",
        questionText: "",
        scoreRule: "01",
        questionType: "1",
        score: "0",
        sortOrder: 1,
        // tableId: "",
        options: [
          {
            id: "",
            content: "",
            optionCodeName: "选项A",
            optionCode: "A",
            optionTextHtml: "",
            optionText: "",
            score: 0,
            sortOrder: 0
          }
        ]
      },
      checkboxTopicForm: {
        questionTextHtml: "",
        questionText: "",
        scoreRule: "02",
        questionType: "2",
        score: 0,
        sortOrder: "0",
        answerArr: [],
        // tableId: "",
        options: [
          {
            id: "",
            content: "",
            optionCodeName: "选项A",
            optionCode: "A",
            score: 0,
            optionTextHtml: "",
            optionText: "",
            sortOrder: 0
          }
        ]
      },
      shortTopicForm: {
        questionTextHtml: "",
        questionText: "",
        score: 0,
        scoreRule: "02",
        questionType: "3",
        sortOrder: "0",
      },
      optionCodeList: ['A', "B", "C", "D", "E", "F", "G", "H", "I", "J", "K"],
      questionTextRules: [
        { required: true, validator: this.validateQuestionText, trigger: 'blur,change' } // 使用blur触发验证
      ],
      rulesForm: {
        scoreRule: "3",
        customScoreRule: "",
        resultInterpRules: [
          {
            min: 0,
            max: 0,
            result: ""
          }
        ]
      },
      // 第一步返回的id值
      firstTableId: "",
      topicNumber: 0,
      btnTypeList: false,
      questionList: [],
      allQuestionList: {},
      baseFromId: "",
      uptateId: "",
      lastClickTime: 0, // 记录上次点击时间戳
      questionIsChioced: false

    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    getTableTypeByUrl() {
      if (this.$route.query.tableType) {
        this.tableType = this.$route.query.tableType
      } else {
        this.tableType = ''
      }
      this.handleGetZD_XLPC_LBLX()
      this.handleGetZD_PSY_SYZT()
      this.handleGetZD_DTJFGZ()
      this.handleGetPsyList(this.tableType)
      if (this.tableType) {
        this.modalTitle = '调查量表'
      } else {
        this.modalTitle = '测评量表'
      }
    },
    getTagType(status) {
      if (status === '已启用') return 'success'
      if (status === '已停用') return 'danger'
      if (status === '未完成') return 'info'
      return ''
    },
    handleStatusChange(item, value) {
      item.status = value ? '已启用' : '已停用'
      console.log('状态变化：', item)
    },
    sortBy(key, order = "asc") {
      return (a, b) => {
        const valA = a[key];
        const valB = b[key];
        if (typeof valA === "number") {
          return order === "asc" ? valA - valB : valB - valA;
        }
        return order === "asc"
          ? valA.localeCompare(valB)
          : valB.localeCompare(valA);
      };
    },
    handleGetZD_XLPC_LBLX() {
      this.authGetRequest({ url: "/bsp-com/static/dic/acp/ZD_XLPC_LBLX.js" }).then(res => {
        let scales = eval('(' + res + ')')
        this.scalesList = scales()
        this.scalesList = this.scalesList.filter(item => {
          if (this.tableType) {
            return this.tableType === item.code;
          } else {
            return true
          }
        }).sort(this.sortBy('code', 'asc'))
      })
    },
    handleGetZD_PSY_SYZT() {
      this.authGetRequest({ url: "/bsp-com/static/dic/acp/ZD_PSY_SYZT.js" }).then(res => {
        let useStatus = eval('(' + res + ')')
        this.useStatusList = useStatus()

      })
    },
    // 双击取消量表分类选择
    handleDoubleScalesClick() {
      this.scalesModel = ""
      this.handleGetPsyList(this.scalesModel, "")
    },
    handleDoubleUseStatusClick() {
      this.useStatusModel = ""
      this.handleGetPsyList("", this.useStatusModel)
    },
    // 量表分类：
    changeScalesType(tableType) {
      this.handleGetPsyList(tableType, "")
    },
    // 使用状态：
    changeUseStatus(usageStatus) {
      this.handleGetPsyList("", usageStatus)
    },
    // 获取测评量表
    handleGetPsyList(tableType, usageStatus) {
      this.authPostRequest({
        url: this.$path.evalTable_list,
        params: { name: "", orgCode: "", tableType, usageStatus }
      }).then(res => {
        if (res.success) {
          this.psyList = res.data
          let firstAddPsyItem = {
            name: "创建量表",
          }
          this.psyList.forEach(ite => {
            // 未完成 01 已启用 02  已停用 03
            if (ite.usageStatus == '03') {
              ite.usageStatusNum = 0
              // 已启用
            } else if (ite.usageStatus == '02') {
              ite.usageStatusNum = 1
            } else {

            }

          })

          this.psyList.forEach((item, index) => {
            this.$set(item, 'showButtons', false); // 关键！
          });
          this.psyList.unshift(firstAddPsyItem)
        }
      })
    },
    //更新启动状态
    handleUpdateEableStatu(item) {

      this.postRequest({
        url: this.$path.evalTable_updateEableStatus,
        params: { id: item.id, usageStatus: item.usageStatus == '02' ? '03' : '02' }
      }).then(res => {
        if (res.success) {
          this.handleGetPsyList("", "")
        }
      })
    },
    // 添加量表按钮
    handleAddFromStep() {
      this.baseFromId = ""
      this.baseFrom.name = ""
      this.baseFrom.tableType = ""
      this.baseFrom.description = ""
      this.baseFrom.id = ""
      this.baseFrom.usageStatus = "01"
      this.baseFrom.estimatedDuration = 0
      this.listContainer = false
      this.addFormStep = true

      // 清空题目数据
      this.questionList = [];
      this.allQuestionList = {};

      // 重置题型弹窗数据
      this.handleSetradioTopicForm('radioTopicForm', this.radioTopicForm);
      this.handleSetradioTopicForm('checkboxTopicForm', this.checkboxTopicForm);
      this.handleSetradioTopicForm('shortTopicForm', this.shortTopicForm);
    },
    /**
     * 移动数组元素
     * @param {Array} arr - 目标数组
     * @param {number} index - 当前元素索引
     * @param {string} direction - 移动方向 ('up' 或 'down')
     * @returns {Array} - 新数组（避免直接修改原数组）
     */
    moveArrayItem(arr, index, direction) {
      // 深拷贝避免污染原数组
      const newArr = [...arr];

      if (direction === 'up' && index > 0) {
        // 上移：交换当前元素与前一个元素
        [newArr[index], newArr[index - 1]] = [newArr[index - 1], newArr[index]];

      } else if (direction === 'down' && index < newArr.length - 1) {
        // 下移：交换当前元素与后一个元素
        [newArr[index], newArr[index + 1]] = [newArr[index + 1], newArr[index]];
      }
      return newArr;
    },
    // 数组挪动方法
    moveItem(index, direction, topForm, pureText) {
      let options = this.moveArrayItem(topForm.options, index, direction);
      this.$set(topForm, 'options', options)
    },
    // 第一步的取消按钮
    handleBaseFormCancel(name) {
      this.$refs[name].resetFields();
      this.listContainer = true
      this.addFormStep = false
      // this.baseFrom = {}
      // 清空题目数据
      this.questionList = [];
      this.allQuestionList = {};
    },
    // 基础信息配置（第一步的下一步按钮）
    handleBaseFormNext(name) {
      this.handleBaseForm(name)
    },
    // 基础信息配置（第一步的保存按钮）
    handleBaseFormSubmit(name) {
      this.handleBaseForm(name)
    },
    handleBaseForm(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let url = ""
          let msg = ""
          if (this.baseFromId) {
            url = this.$path.evalTable_update_step1
            msg = "更新成功"
          } else {
            url = this.$path.evalTable_create_step1
            msg = "创建成功"
          }
          this.authPostRequest({ url: url, params: { ...this.baseFrom, id: this.baseFromId } }).then(res => {
            if (res.success) {
              this.firstStep = false
              this.secondStep = true
              if (this.active++ > 2) this.active = 0;
              // 当他是新增的时候，这个时候返回的是TableId
              if (!this.baseFromId) this.firstTableId = res.data
              this.handleGetQuestionList()
              this.$refs[name].resetFields()
              this.baseFromId = res.data
            } else {
              this.$Message.error(res.message)
            }
          })
        } else {
          this.$Message.error('验证未通过');
        }
      })
    },
    // 题目配置 上一步按钮
    handleTopicSetPrevious() {
      this.authGetRequest({ url: this.$path.evalTable_get_step1, params: { id: this.firstTableId } }).then(res => {
        if (res.success) {
          this.baseFrom = res.data
          this.baseFromId = res.data.id
          this.rulesForm.scoreRule = res.data.scoreRule
          this.rulesForm.customScoreRule = res.data.customScoreRule
          this.rulesForm.resultInterpRules = JSON.parse(res.data.resultInterpRule)

          // this.baseFrom = {}
          this.secondStep = false
          this.firstStep = true
          if (this.active-- === 0) this.active = 0;
        }
      })
    },
    isAllFalsy(arr) {
      return arr.every(item => !item);
    },
    // 题目配置 下一步按钮
    handleTopicSetNext() {
      // 下一步继续的话，1首先先验证是否添加其中一种题目了，如果所有题目类型都未添加则不可进行下一步
      // 如果添加其中一种任何一种题型的话，都可进行下一步操作。
      let questionArrTrue = [this.allQuestionList['3'] && this.allQuestionList['3'].length > 0, this.allQuestionList['2'] && this.allQuestionList['2'].length > 0,  this.allQuestionList['1'] && this.allQuestionList['1'].length > 0]
      this.questionIsChioced =  this.isAllFalsy(questionArrTrue) 
           
      if (this.questionIsChioced) {
        this.$Message.error("请添加至少一类题型后进行下一步操作")
        return
      } else {
        if (this.active++ > 2) this.active = 0;
        this.secondStep = false
        this.thirdStep = true
      }
    },
    handleTopicSave() {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '是否保存题目',
        onOk: () => {
          // 下一步继续的话，1首先先验证是否添加其中一种题目了，如果所有题目类型都未添加则不可进行下一步
      // 如果添加其中一种任何一种题型的话，都可进行下一步操作。
          let questionArrTrue = [this.allQuestionList['3'] && this.allQuestionList['3'].length > 0, this.allQuestionList['2'] && this.allQuestionList['2'].length > 0,  this.allQuestionList['1'] && this.allQuestionList['1'].length > 0]
          this.questionIsChioced =  this.isAllFalsy(questionArrTrue) 
              
          if (this.questionIsChioced) {
            this.$Message.error("请添加至少一类题型后进行下一步操作")
            return
          } else {
            if (this.active++ > 2) this.active = 0;
            this.secondStep = false
            this.thirdStep = true
          }
        },
        onCancel: () => {
          this.handleGetQuestionList()
        }
      })
    },
    // 点击单选题按钮
    handleAddRadioTopic() {
      this.radioTopicVisible = true

      this.radioTopicForm.questionTexcontent = ""
      this.radioTopicForm.options = [{
        id: "",
        optionCodeName: "选项A",
        optionCode: "A",
        optionText: "",
        score: 0,
        sortOrder: 0
      }];

      this.clearEditor('radioEditor');
    },
    // 点击多选框的按钮
    handleAddCheckboxTopic() {
      this.checkboxTopicVisible = true
      this.checkboxTopicForm.questionTexcontent = "";

      this.checkboxTopicForm.options = [{
        id: "",
        optionCodeName: "选项A",
        optionCode: "A",
        optionText: "",
        score: 0,
        sortOrder: 0
      }];

      this.clearEditor('checkboxEditor');
    },
    handleAddShortTopic() {
      this.shortTopicVisible = true
      this.shortTopicForm.questionTexcontent = "";
      this.clearEditor('shortEditor');
    },
    clearEditor(refName) {
      this.$nextTick(() => {
        const editor = this.$refs[refName];
        if (editor?.clearContent) {
          editor.clearContent();
        }
      });
    },
    handleGetZD_DTJFGZ() {
      this.authGetRequest({ url: "/bsp-com/static/dic/acp/ZD_DTJFGZ.js" }).then(res => {
        let useRadioType = eval('(' + res + ')')
        this.useRadioTypeList = useRadioType()
        this.useRadioTypeList.forEach((item, index) => {
          if (index == 0) {
            item.name = `${item.name}：每个选项对应预设分数，选中即获得该选项分数`
          } else if (index == 1) {
            item.name = `${item.name}：系统判定答案正确得全分，错误不得分`
          } else {
            item.name = `${item.name}：仅做选择，无分值`
          }
        })
      })
    },
    // 插入新选项并重排后续选项
    insertAndReorderOptions(options, index) {
      // 插入新的空选项
      options.splice(index + 1, 0, {
        id: "",
        optionCodeName: "",
        optionCode: "",
        optionText: "",
        score: 0,
        sortOrder: 0
      });

      // 重新设置所有选项的 optionCode 和 optionCodeName
      for (let i = 0; i < options.length; i++) {
        const code = this.optionCodeList[i];
        options[i].optionCode = code;
        options[i].optionCodeName = `选项${code}`;
      }

      return options;
    },
    // 增加选项的增加按钮
    handleAddRadioIcon(index, radioTopicForm) {
      this.indexNum++;
      this.sortNum++
      this.insertAndReorderOptions(radioTopicForm.options, index);
    },
    reorderOptionsAfterDelete(options, index) {
      // 删除当前索引的选项
      options.splice(index, 1);

      // 从删除的位置开始重新编号
      for (let i = index; i < options.length; i++) {
        const newIndex = i;
        const newCode = this.optionCodeList[newIndex]; // 获取新的 optionCode
        options[i].optionCode = newCode;
        options[i].optionCodeName = `选项${newCode}`;
      }

      return options;
    },

    // 删除item项的按钮
    handleDeleteRadioIcon(index, radioTopicForm) {
      if (radioTopicForm.options.length > 1) {
        // radioTopicForm.options.splice(index, 1)
        this.reorderOptionsAfterDelete(radioTopicForm.options, index);
      }
    },
    handleSetradioTopicForm(name, topicForm) {

      // this.$refs.editorVue.clearContent()
      // this.$nextTick(() => {
      //     this.$refs.editorVue.clearContent()
      // });
      topicForm.questionTexcontent = ""
      topicForm.questionText = ""
      topicForm.scoreRule = ""
      topicForm.srore = 1
      topicForm.answerArr = []

      this.indexNum = 0
      this.sortNum = 1
      topicForm.options = [{
        id: "",
        optionCodeName: "选项A",
        optionCode: "A",
        optionText: "",
        content: "",
        score: 0,
        sortOrder: 0
      }]
      this.$refs[name]?.resetFields();
    },


    // 新增题目的提交按钮
    handleRadioTopicSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let msg = this.uptateId ? '更新成功' : '新增成功';
          let params = {
            ...this.radioTopicForm,
            tableId: this.firstTableId,
            id: this.uptateId || ""
          }
          this.authPostRequest({ url: this.$path.evalTableQuestion_create, params }).then(res => {
            if (res.success) {
              this.radioTopicVisible = false
              this.handleGetQuestionList('1', '01')
              this.handleGetQuestionList()
              this.uptateId = ""
              this.handleSetradioTopicForm(name, this.radioTopicForm)
              this.$Message.success(msg)
            } else {
              this.$Message.error(res.message || '新增失败')
            }
          })
        } else {
          this.$Message.error('验证未通过');
        }
      })
    },
    // 量表的详情按钮
    handleDetailsTable(item) {
      this.authGetRequest({ url: this.$path.evalTable_get_step1, params: { id: item.id } }).then(res => {
        if (res.success) {
          const resolved = this.$router.resolve({
            path: '/discipline/psy/previewPage',
            query: { id: res.data.id, isHaveHisTable: true }
          });
          window.open(resolved.href, '_blank'); // 新标签页打开
        }
      })
    },
    // 多选题的提交按钮
    handleCheckBoxTopicSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let msg = this.uptateId ? '更新成功' : '新增成功';
          let params = {
            ...this.checkboxTopicForm,
            tableId: this.firstTableId,
            id: this.uptateId || "",
          };
          this.checkboxTopicForm.answer = this.checkboxTopicForm.answerArr.join(',')

          this.authPostRequest({ url: this.$path.evalTableQuestion_create, params }).then(res => {
            if (res.success) {
              this.checkboxTopicVisible = false
              this.handleGetQuestionList('2', '02')
              this.handleGetQuestionList()
              this.uptateId = ""
              this.handleSetradioTopicForm(name, this.checkboxTopicForm)
              this.$Message.success(msg)
            } else {
              this.$Message.error(res.message || '新增失败')
            }
          })


        } else {
          this.$Message.error('验证未通过');
        }
      })
    },
    // 简答题的提交按钮
    handleShortTopicSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let msg = this.uptateId ? '更新成功' : '新增成功';
          let params = {
            ...this.shortTopicForm,
            tableId: this.firstTableId,
            id: this.uptateId || ""
          };

          this.authPostRequest({ url: this.$path.evalTableQuestion_create, params }).then(res => {
            if (res.success) {
              this.handleSetradioTopicForm(name, this.shortTopicForm)
              this.shortTopicVisible = false
              this.handleGetQuestionList('3', '')
              this.handleGetQuestionList()
              this.uptateId = ""
              this.$Message.success(msg)
            } else {
              this.$Message.error(res.message || '新增失败')
            }
          })
        } else {
          this.$Message.error('验证未通过');
        }
      })
    },
    // 新增单选题目的取消按钮
    handleRadioTopicReset(name) {
      this.radioTopicVisible = false
      this.handleSetradioTopicForm(name, this.radioTopicForm)
      this.clearEditor('radioEditor');
    },
    // 新增多选题目的取消按钮
    handleCheckBoxTopicReset(name) {
      this.checkboxTopicVisible = false
      this.handleSetradioTopicForm(name, this.checkboxTopicForm)
      this.clearEditor('checkboxEditor');
    },
    // 新增简答题目的取消按钮
    handleShortTopicReset(name) {
      this.shortTopicVisible = false
      this.handleSetradioTopicForm(name, this.shortTopicForm)
      this.clearEditor('shortEditor');
    },
    groupBy(arr, key) {
      return arr.reduce((result, item) => {
        const groupKey = item[key];
        (result[groupKey] = result[groupKey] || []).push(item);
        return result;
      }, {});
    },
    // 获取单选题 多选题 简答题的列表以及数量
    handleGetQuestionList(questionType, scoreRule) {
      if (!questionType) {
        this.authPostRequest({
          url: this.$path.evalTableQuestion_list,
          params: { questionText: "", questionType, scoreRule, tableId: this.firstTableId }
        }).then(res => {
          if (res.success) {
            if (res.data.length > 0) {
              this.$nextTick(() => {
                this.allQuestionList = this.groupBy(res.data, 'questionType')
                this.$set(this, 'allQuestionList', this.groupBy(res.data, 'questionType'))
              })
            }
          }
        })
      } else {
        this.authPostRequest({
          url: this.$path.evalTableQuestion_list,
          params: { questionText: "", questionType, scoreRule, tableId: this.firstTableId }
        }).then(res => {
          if (res.success) {
            this.topicNumber = res.data.length
            this.questionList = res.data
            this.$set(this, 'questionList', res.data)
          }
        })
      }

    },
    validateQuestionText(rule, value, callback) {
      if (!value) {
        callback(new Error('请输入题目内容'));
      } else {
        // 验证通过时必须调用callback()
        callback(); // 这是解决图标转圈问题的关键
      }
    },
    // 添加单选题的题目内容输入框事件
    hChangeData(editDataHtml, editData) {
      this.radioTopicForm.questionTextHtml = editDataHtml
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = editDataHtml; // 将 HTML 内容赋值给临时元素
      this.radioTopicForm.questionText = tempDiv.innerText || tempDiv.textContent;
    },
    handleEditorChange(editDataHtml, index) {
      this.radioTopicForm.options[index].optionTextHtml = editDataHtml
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = editDataHtml; // 将 HTML 内容赋值给临时元素
      this.$set(this.radioTopicForm.options[index], 'optionText', tempDiv.innerText || tempDiv.textContent);
    },
    // 添加多选题的题目内容输入框事件
    hChangeDataCkeckbox(editDataHtml, editData) {
      this.checkboxTopicForm.questionTextHtml = editDataHtml
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = editDataHtml; // 将 HTML 内容赋值给临时元素
      this.checkboxTopicForm.questionText = tempDiv.innerText || tempDiv.textContent;
    },
    handleEditorChangeCheckBox(editDataHtml, index) {
      this.checkboxTopicForm.options[index].optionTextHtml = editDataHtml
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = editDataHtml; // 将 HTML 内容赋值给临时元素
      this.$set(this.checkboxTopicForm.options[index], 'optionText', tempDiv.innerText || tempDiv.textContent);
    },
    hChangeDataShort(editDataHtml, editData) {
      this.shortTopicForm.questionTextHtml = editDataHtml
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = editDataHtml; // 将 HTML 内容赋值给临时元素
      this.shortTopicForm.questionText = tempDiv.innerText || tempDiv.textContent;
    },
    // 切换提醒类型的Tab切换
    handleChangeQuestionType(name) {
      if (name == 'oneQuestion') {
        this.handleGetQuestionList('1', '01')
      } else if (name == 'moreQuestion') {
        this.handleGetQuestionList('2', '02')
      } else if (name == 'simpleQuestion') {
        this.handleGetQuestionList('3', '')
      } else {
        this.handleGetQuestionList()
      }
      //
    },
    // 点击那三个小点点中的事件
    handOpenBtnType(id) {
      this.psyList.forEach((item, i) => {
        const show = (item.id === id) ? !item.showButtons : false;
        this.$set(this.psyList[i], 'showButtons', show);
      });

    },
    // 点击那三个小点点中的编辑事件
    handleEditTable(item) {
      this.listContainer = false
      this.addFormStep = true

      Promise.all([
        this.authGetRequest({ url: this.$path.evalTable_get_step1, params: { id: item.id } }),
        this.authGetRequest({ url: this.$path.evalTableQuestion_get, params: { id: item.id } })
      ]).then(res => {
        if (res[0].success) {
          this.baseFrom = res[0].data
          this.baseFromId = res[0].data.id
          this.firstTableId = res[0].data.id
          this.rulesForm.scoreRule = res[0].data.scoreRule
          this.rulesForm.resultInterpRules = JSON.parse(res[0].data.resultInterpRule)
          this.rulesForm.customScoreRule = res[0].data.customScoreRule
          // 进入题目配置时重新获取题目数据
          this.handleGetQuestionList();
        }
      })
    },
    handleDeleteQustionCommon(item, index, questionType, rules) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: () => {
          this.authGetRequest({ url: this.$path.evalTableQuestion_delete, params: { ids: item.id } }).then(res => {
            if (res.success) {
              this.$Message.success('删除成功')
              this.handleGetQuestionList(questionType, rules)
              this.handleGetQuestionList()
            }
          })
        }
      })
    },
    // 删除单选题目类型
    handleDeleteRadioQuestion(item, index) {
      this.handleDeleteQustionCommon(item, index, '1', '01')
    },
    // 删除多选题目类型
    handleDeleteCheckBoxQuestion(item, index) {
      this.handleDeleteQustionCommon(item, index, '2', '02')
    },
    // 删除简答题目类型
    handleDeleteSimpleQuestion(item, index) {
      this.handleDeleteQustionCommon(item, index, '3', '')
    },
    handleUpdateQuestion(item, index, topicVisible, topicForm) {


      this.authGetRequest({ url: this.$path.evalTableQuestion_get, params: { id: item.id } }).then(res => {
        if (res.success) {
          this.uptateId = res.data.id
          if (topicVisible == 'radioTopicVisible') {
            this.$set(this, 'radioTopicVisible', true)
            this.radioTopicForm.scoreRule = res.data.scoreRule
          }
          if (topicVisible == 'checkBoxTopicVisible') {
            this.$set(this, 'checkboxTopicVisible', true)
            this.checkboxTopicForm.scoreRule = res.data.scoreRule
          }
          if (topicVisible == 'shortTopicVisible') {
            this.$set(this, 'shortTopicVisible', true)
          }

          topicForm.options = res.data.options
          topicForm.options.forEach((item, index) => {
            item.optionCodeName = `选项${this.optionCodeList[index]}`
          })
          topicForm.options.forEach(item => {
            item.content = item.optionText
          })
          topicForm.questionTexcontent = res.data.questionText
        }
      })
    },
    // 更新单选题目类型
    handleUpateRadioQuestion(item, index) {
      this.handleUpdateQuestion(item, index, 'radioTopicVisible', this.radioTopicForm)
    },
    // 更新多选题目类型
    handleUpateCheckBoxQuestion(item, index) {
      this.handleUpdateQuestion(item, index, 'checkBoxTopicVisible', this.checkboxTopicForm)
    },
    handleUpateSimpleQuestion(item, index) {
      this.handleUpdateQuestion(item, index, 'shortTopicVisible', this.shortTopicForm)

    },
    // 删除题目
    handleDeleteTable(item) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: () => {
          this.postRequest({ url: this.$path.evalTable_delete_step1, params: { ids: item.id } }).then(res => {
            if (res.success) {
              this.$Message.success('删除成功')
              this.handleGetPsyList()
            }
          })
        }
      })
    },
    //题目预览 // 计分规则配置预览按钮
    handlePreview(item) {
      const resolved = this.$router.resolve({
        path: '/discipline/psy/previewPage',
        query: { id: this.firstTableId }
      });
      window.open(resolved.href, '_blank'); // 新标签页打开
    },
    // 计分规则配置提交按钮
    handleRulesFormSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.authPostRequest({
            url: this.$path.evalTable_submit,
            params: { ...this.rulesForm, id: this.firstTableId }
          }).then(res => {
            if (res.success) {
              this.addFormStep = false
              this.listContainer = true
              this.handleGetPsyList()
              // this.firstStep = true
              this.thirdStep = false
              this.$Message.success('新增成功')
            } else {
              this.$Message.error(res.message || '新增失败')
            }
          })
        } else {
          this.$Message.error('验证未通过');
        }
      })
    },
    // 计分规则配置上一步按钮
    handleRulesFormCancel(name) {
      this.thirdStep = false
      this.secondStep = true
      if (this.active-- === 0) this.active = 0;
    },
    // 计分规则配置取消按钮
    handleRulesFormBack(name) {
      this.addFormStep = false
      this.listContainer = true
      this.active = 1
      this.firstStep = true
      this.secondStep = false
      this.thirdStep = false
      this.$refs[name]?.resetFields();
    },
    handleAddRulesItem() {
      this.index++;
      this.rulesForm.resultInterpRules.push({
        min: 0,
        max: 0,
        result: ""
      });
    },
    handleDeleteRulesItem(index) {
      if (this.rulesForm.resultInterpRules.length > 1) {
        this.rulesForm.resultInterpRules.splice(index, 1)
      }
    },
    handleDeleteQuestion(item) {

    },
  },
  mounted() {
    this.getTableTypeByUrl()

  },
  watch: {
    '$route'(to, from) {
      this.getTableTypeByUrl()
    }
  },


}
</script>

<style lang="less" scoped>
.max-container {
  width: 100%;
  height: 100%;
}


.list-container {
  width: 100%;
  height: 100%;

  .tableDetail {
    width: 100%;
    height: calc(~'100% - 48px');

    .header-top {
      padding-top: 10px;
      display: flex;
      align-items: center;

      .scales-list,
      .useStatus-list {
        display: flex;
        align-items: center;
        margin-left: 20px;
      }
    }

    .add-psy-list {
      // padding-top: 20px;
      // height: calc(~'100% - 20px');
      // margin-bottom: 100px;
      width: 100%;
      display: flex;
      // justify-content: space-around;
      flex-wrap: wrap;

      .add-psy-list-container {
        margin-left: 15px;
        margin-top: 10px;
        margin-bottom: 10px;

        .add-psy-img {
          border: dashed 1px #3491FA;
          width: 26vw;
          height: 300px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;

          h4 {
            cursor: pointer;
            color: #3491FA;
            margin: 0 auto;
          }
        }

        .card-info {
          width: 26vw;
          height: 300px;
          position: relative;

          .title-name {
            display: flex;

            .name-id {
              margin-top: 5px;
              margin-left: 8px;

              h4 {
                max-width: 220px;
                white-space: nowrap;
                /* 强制文本不换行 */
                overflow: hidden;
                /* 隐藏溢出内容 */
                text-overflow: ellipsis;
                /* 溢出部分显示省略号 */
              }

              p {
                font-weight: normal;
                font-size: 14px;
                color: #8D99A5;
                margin-top: 10px;
              }
            }
          }

          .unfinished-img {
            display: inline-block;
            width: 60px;
            height: 60px;
            background: url('../../../assets/images/unFinished.png') center / cover no-repeat;
          }

          .rate-demo {
            padding: 20px;

            .rate-demo-type {
              margin-bottom: 8px;

              span:first-child {
                display: inline-block;
                width: 70px;
              }
            }

            .description {
              display: flex;

              // span:first-child {
              //     display: inline-block;
              //     width: 70px;
              // }

              span:last-child {
                display: -webkit-box;
                /* 启用弹性伸缩盒模型 */
                -webkit-box-orient: vertical;
                /* 垂直排列子元素 */
                -webkit-line-clamp: 2;
                /* 限制显示2行 */
                overflow: hidden;
                /* 隐藏溢出内容 */
                text-overflow: ellipsis;
                /* 超出部分显示省略号 */
                width: 17vw;
              }
            }
          }

          .last-time {
            width: 100%;
            height: 53px;
            line-height: 53px;
            border-top: solid 1px #f5f5f5;
            position: absolute;
            bottom: 0;

            .icon-container {
              display: flex;
              justify-content: space-around;

              .look-icon {
                display: flex;
                align-items: center;
                cursor: pointer;
              }

              // .look-icon:nth-child(2) {}

            }
          }

          .btn-type {
            display: block;
            position: absolute;
            border-width: 1px;
            border-style: solid;
            border-image: initial;
            border-color: rgb(232, 234, 236);
            background: #fff;
            right: 0;
            border-radius: 3px;
            z-index: 100;
          }

          .btn-type:hover {
            box-shadow: rgba(0, 0, 0, .2) 0px 1px 3px;
            border-color: rgb(238, 238, 238);
          }


          .btn-type-container-wwc {
            bottom: -45px;
          }

          .btn-type-container {
            bottom: -75px;
          }
        }
      }
    }
  }
}

.add-form-step {
  width: 100%;
  height: 100%;
  background-color: #F1F5F6;
  display: flex;
  justify-content: space-between;


  .left-step {
    width: 220px;
    // margin: 10px;
    margin: 10px 0 10px 10px;
    border-radius: 5px 0 0 5px;
    background-color: #fff;
    background: url('../../../assets/images/tableLeftStep.png');

    .step-container {
      height: 40%;
      padding: 30px 0 50px 20px;
    }

    h4 {
      display: inline-block;
      padding: 20px;
    }
  }

  .right-tab-form {
    flex: 1;
    border-radius: 0 5px 5px 0;
    margin: 10px 10px 10px 0px;
    background-color: #fff;
    overflow-y: auto;
  }

  .right-tab-form-first {
    h4 {
      display: inline-block;
      padding: 20px;
    }
  }

  .right-tab-form-second {

    h4 {
      display: inline-block;
      padding: 20px;
    }

    .tab-container {
      padding: 0 0 0 20px;
    }
  }
}

/deep/ .ivu-card-body {
  padding: 0;
}

/deep/ .w-e-text-container {
  height: 190px;
}

/deep/ .fm-content-wrap .ivu-form-item .ivu-form-item-content {
  width: 75%;
}

/deep/ .radio-Topic-Visible {
  // max-height: 400px;
  overflow-y: auto;
}

.form-list {
  position: relative;

  .icon-list {
    position: absolute;
    right: -20px;
    top: 20px;
    width: 120px;

    /deep/ .ivu-icon {
      font-size: 16px;
      margin-left: 10px;
    }

    .el-icon-delete {
      margin-left: 10px;
    }
  }
}

.priview-Normal {
  overflow-y: auto;

  .header-top {
    width: 100%;
    height: 84px;
    background-color: #2d8cf0;
  }

  .content-container {
    display: flex;
    justify-content: space-between;

  }
}

/deep/ .content-wrapper {
  padding: 0;
  // margin: 20px;
  // border-radius: 6px;
  // height: calc(100% - 80px);
  // overflow: auto;
  // position: relative;
  // background: #FFFFFF;
  // -webkit-box-shadow: 0px 4px 16px 1px rgba(0, 0, 0, 0.16);
  // box-shadow: 0px 4px 16px 1px rgba(0, 0, 0, 0.16);
}

/* 蓝色对号图标 */
// /deep/.el-step__head.is-success {
//     color: #409EFF !important;
//     /* 图标蓝色 */
//     font-weight: bold;
//     border-color: #409EFF !important;
// }
// /deep/.el-step__title.is-success {
//     border-color: #409EFF !important;
// }
/* 蓝色标题文字 */
// /deep/.el-step__title {
//     color: #409EFF !important;
//     /* 文字蓝色 */
//     font-weight: bold;
// }

.fm-content-wrap-title {
  border-bottom: 1px solid #cee0f0;
  background: #eff6ff;
  line-height: 40px;
  padding-left: 10px;
  font-family: Source Han Sans CN;
  font-weight: 700;
  font-size: 16px;
  color: #00244a;
  margin-bottom: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.form-list-rules {
  padding: 10px 10px 10px 0;
  margin-left: 33px;
  border: solid 1px #f5f5f5;
  border-radius: 4px;
  margin-bottom: 10px;
  position: relative;

  .index-num {
    position: absolute;
    top: 20px;
    width: 30px;
    border-radius: 0 15px 15px 0;
    color: #fff;
    background-color: #2390ff;
  }

  .form-content {

    margin-right: 20px;
    margin-left: -37px;

    .header-form {
      display: flex;
      justify-content: space-between;

      .time-chioce {
        display: flex;
        align-items: center;
      }
    }
  }

  .del-icon {
    position: absolute;
    right: 10px;
    top: 5px;
  }
}
</style>
