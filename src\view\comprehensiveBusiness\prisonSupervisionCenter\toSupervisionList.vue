<template>
  <div>
    <ui-condition-box v-model="param"  :instance="$refs['uiCardTable']">
      <ui-condition-item label="违规对象" prop="outlineObject">
        <ui-select
          class="com-condition-width-1"
          v-model="param.outlineObject"
          :select-data="objectOptions"
          label-key="dataName"
          value-key="dataCode"
        ></ui-select>
      </ui-condition-item>
      <ui-condition-item label="登记时间" :prop="['operateTimeStart','operateTimeEnd']">
        <ui-date-picker
          class="com-condition-width-2"
          type="datetimerange"
          v-model="param.operateTimeStart"
          :end-time="param.operateTimeEnd"
          @end-change="param.operateTimeEnd = $event"
        ></ui-date-picker>
      </ui-condition-item>
      <ui-condition-item label="违规时间" :prop="['outlineTimeStart','outlineTimeEnd']">
        <ui-date-picker
          class="com-condition-width-2"
          type="datetimerange"
          v-model="param.outlineTimeStart"
          :end-time="param.outlineTimeEnd"
          @end-change="param.outlineTimeEnd = $event"
        ></ui-date-picker>
      </ui-condition-item>
    </ui-condition-box>
    <com-hr/>
    <div class="com-table-wrapper">
      <ui-card-table
        :height="$getTableHeight()"
        :get-data-method="operate"
        :is-init-load="false"
        :get-data-param="param"
        :data.sync="tableData"
        ref="uiCardTable"
        @on-change="changeSelect"
      >
        <div slot="operateBox">
          <ui-button :disabled="selectData.length === 0"  @click="handleSend(selectData)">提交</ui-button>
        </div>
        <ui-table slot="table" full  :columns="columns" :data="tableData" @on-selection-change="tableSelect">
          <template slot="operate"   slot-scope="{row}" >
            <span class="com-table-btn"  @click="toPage(row)">信息补充</span>
            <span class="com-table-btn"  v-if="`${row.unSubmit}` === '1'" @click="toComplainForm(row)">申诉</span>
          </template>
        </ui-table>
        <div slot="card" class="table-card-container">
          <div class="table-card-item" v-for="item in tableData" :key="item.id">
            <div class="table-card-head-box">
              <ui-checkbox  class="head-check" v-model="checkData[item.id]"  @on-change="cardSelect"></ui-checkbox>
              <div class="head-item">
                <label>违规单位：</label>
                <span>{{item.unitName}}</span>
              </div>
              <div class="head-item">
                <label>违规时间：</label>
                <span>{{item.outlineTime}}</span>
              </div>
            </div>
            <div class="table-card-content-box">
              <div class="content-row">
                <div class="content-item">
                  <label>违规类型：</label>
                  <span>{{item.questions}}</span>
                </div>
                <div class="content-item">
                  <label>巡查来源：</label>
                  <span>{{item.source}}</span>
                </div>
                <div class="content-item">
                  <label>整改期限：</label>
                  <span>{{item.correctPeriod}}</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-item">
                  <label>发送时间：</label>
                  <span>{{item.forwardTime}}</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-item">
                  <label>违规详情：</label>
                  <span>{{item.detail}}</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-item w100">
                  <label>附件：</label>
                  <ui-multi-uploader :value="trainsFromData(item.files)" readonly/>
                </div>
              </div>
              <div class="content-operate">
                <span  v-if="`${item.isRepresent}` === '1'"  class="red-text">申诉中</span>
                <div class="operate">
                  <ui-button @click="toPage(item)" class="mr-10">信息补充</ui-button>
                  <ui-button  @click="toComplainForm(item)" v-if="`${item.unSubmit}` === '1'" >申诉</ui-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ui-card-table>
    </div>
  </div>
</template>

<script>
import {fileTranslate} from "@/util";
import { facingPage, finishSuperviseForm } from "@/axios/zhjgBranchWork";
import {findByTypeCode } from "@/axios/zhjgBasicBusiness";
import {selectData} from "@/mixins";
export default {
  data() {
    return {
      userInfo: this.$store.state.userInfo,
      objectOptions: [],
      columns: [
        {
          type: "selection",
          width: 60,
        },
        {
          title: "序号",
          key: "index",
          width: 80,
        },
        {
          title: "违规单位",
          key: "unitName",
        },
        {
          title: "违规对象",
          key: "outlineObject",
        },
        {
          title: "违规类型",
          key: "questions",
        },
        {
          title: "违规时间",
          key: "outlineTime",
          width: 220,
        },
        {
          title: "巡查来源",
          key: "source",
        },
        {
          title: "登记人",
          key: "operateUserName",
        },
        {
          title: "登记时间",
          key: "operateTime",
          width: 220,
        },
        {
          title: "操作",
          slot: "operate",
          width: 180,
        },
      ],
      tableData: [],
      param: {},
    };
  },
  mixins: [selectData],
  created() {
    this.getDicOption("C_WGDX", "objectOptions");
  },
  activated() {
    this.$refs.uiCardTable.changePageNumber();
  },
  methods: {
    getDicOption(code, list) {
      findByTypeCode(code).then(res => {
        this[list] = res.data || [];
      });
    },
    toComplainForm(item) {
      this.$toPage({
        name: "prisonAddComplain",
        params: {tableData: [item]}
      });
    },
    toPage(item) {
      this.$toPage({
        name: "prisonSupervisionForm",
        params: {id: item.id, row: item}
      });
    },
    handleSend(arr) {
      let message = "是否确认完成督导单?";
      for (let i = 0; i < arr.length; i++) {
        if (`${arr[i].isRepresent}` === "1") {
          return this.$messageInfo("存在申诉中的督导,请重新选择");
        }
        if (`${arr[i].isApprove}` === "0" || `${arr[i].isApprove}` === "2") {
          message = "存在未经领导审批的督导单,是否确认完成督导单?";
        }
      }
      this.$showConfirmModal({
        confirmHandler: () => {
          let param = arr.map(item => item.id);
          finishSuperviseForm(param).then(() => {
            this.$operateSuccessMessage();
            this.$emit("refreshNum", true);
            this.$refs.uiCardTable.changePageNumber(1);
          }).catch(err => this.$messageError(err));
        },
        contentText: message,
      });
    },
    operate(param) {
      param.unitId = this.userInfo.prisonId;
      param.unitName = this.userInfo.prisonName;
      param.userId = "";
      param.status = "1";
      this.resetSelect();
      return facingPage(param);
    },
    trainsFromData(data) {
      return fileTranslate(data);
    }
  }
};
</script>
<style lang="less">
  @import "../../assets/styles/component/tableCard.less";
</style>
