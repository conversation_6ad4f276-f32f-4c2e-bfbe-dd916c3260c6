<template>
  <div>
    <div class="bsp-base-tit" style="font-size: 18px;font-weight: bold;">
      分级处遇
    </div>
    <div class="prison-select-center">
      <div class="prison-select-center-bottom-left">
        <div>
          <div class="risk-fjtj">
            <div class="risk-statitis left" style="border: 0">分级处于统计</div>
            <div>
              <el-select v-model="curRoomId" @change="changeRoom" placeholder="">
                <el-option v-for="item in curRoomIds" :value="item.roomCode" :label="item.name"/>
              </el-select>
            </div>
          </div>
          <div class="risk-statitis">
            <div class="risk-tp">
              <RistGaugeChart :riskIds="riskIds" :curRoomId="curRoomId" :riskScore="riskScore"/>
            </div>
            <div class="cards">
              <div class="card">
                <div class="gyl"></div>
                <div class="info">
                  <div class="title">所外就医</div>
                  <div class="value">{{ orgScore.treatment }}</div>
                </div>
              </div>
              <div class="card">
                <div class="cs">
                </div>
                <div class="info">
                  <div class="title">投转出所</div>
                  <div class="value">{{ orgScore.out }}</div>
                </div>
              </div>
              <div class="card">
                <div class="dqgks">
                </div>
                <div class="info">
                  <div class="title">重点关注人员</div>
                  <div class="value">{{ orgScore.attention }}</div>
                </div>
              </div>
              <div class="card">
                <div class="dqajs">
                </div>
                <div class="info">
                  <div class="title">风险人员</div>
                  <div class="value">{{ orgScore.riskPrison }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="prison-select-center-top-bottom">
          <Tabs :value="activeName" @on-click="changeTabs" style="min-height: 528px;">
            <TabPane label="监室风险列表" name="jsfxlb">
              <!--操作区-->
              <div class="room-cz-button-search">
                <el-select clearable v-model="areaId" @change="changeArea" placeholder="请选择监区"
                           class="button-select">
                  <el-option v-for="item in orgCodeList" :value="item.areaCode" :key="item.id"
                             :label="item.areaName"/>
                </el-select>
                <el-select clearable v-model="roomId" @change="changeRoom" placeholder="请选择监室号"
                           class="button-select">
                  <el-option v-for="item in roomIds" :value="item.roomCode" :key="item.roomCode"
                             :label="item.roomName"/>
                </el-select>
                <el-select clearable v-model="riskId" placeholder="请选择风险等级"
                           class="button-select">
                  <el-option v-for="item in riskIds" :value="item.orderId" :key="item.orderId"
                             :label="item.name"/>
                </el-select>
                <el-button type="primary" class="button-select"
                           style="width: 62px;height: 34px;"
                           @click.native="searchData">筛选
                </el-button>
              </div>
              <!--card列表-->
              <div v-if="dataList.length > 0" style="flex-wrap: wrap;display: flex; font-size: 12px;">
                <div v-for="roomData in dataList" :key="roomData.id" class="fm-content-card-item">
                  <RoomCard :roomData="roomData" :activeName="activeName"/>
                </div>
              </div>
              <noData v-else />
            </TabPane>
            <TabPane label="人员风险列表" name="ryfxlb">
              <!--操作区-->
              <div class="room-cz-button-search">
                <el-select clearable v-model="areaId" @change="changeArea" placeholder="请选择监区"
                           class="button-select">
                  <el-option v-for="item in orgCodeList" :value="item.areaCode" :key="item.id"
                             :label="item.areaName"/>
                </el-select>
                <el-select clearable v-model="roomId" @change="changeRoom" placeholder="请选择监室号"
                           class="button-select">
                  <el-option v-for="item in roomIds" :value="item.roomCode" :key="item.roomCode"
                             :label="item.roomName"/>
                </el-select>
                <el-select clearable v-model="riskId" placeholder="请选择风险等级"
                           class="button-select">
                  <el-option v-for="item in riskIds" :value="item.orderId" :key="item.orderId"
                             :label="item.name"/>
                </el-select>
                <el-button type="primary" class="button-select"
                           style="width: 62px;height: 34px;"
                           @click.native="searchData">筛选
                </el-button>
              </div>
              <!--card列表-->
              <div v-if="dataList.length > 0" style="flex-wrap: wrap;display: flex; font-size: 12px;">
                <div v-for="roomData in dataList" :key="roomData.id" class="fm-content-card-item">
                  <RoomCard :roomData="roomData" :activeName="activeName"/>
                </div>
              </div>
              <noData v-else />
            </TabPane>
          </Tabs>
          <!--分页-->
          <div style="text-align: center;margin-bottom: 10px">
            <Page :total="total" class="pageWrap" show-total :page-size="page.pageSize" @on-prev="getNo"
                  @on-next="getNo" :current="page.pageNo" @on-change="getNo" @on-page-size-change="getSize"/>
          </div>
        </div>
      </div>
      <div class="prison-select-center-bottom-right">
        <div class="right-top">
          <Tabs :value="activePictureName" @on-click="changePicTabs">
            <TabPane label="风险监室占比" name="fxjszb">
              <RistPerChart :itemData="itemData" :activePictureName="activePictureName"/>
            </TabPane>
            <TabPane label="风险人员占比" name="fxryzb">
              <RistPerChart :itemData="itemData" :activePictureName="activePictureName"/>
            </TabPane>
          </Tabs>
        </div>
        <div class="right-bottom">
          <div class="risk-statitis left" style="border: 0;line-height: 19px">按事件动态</div>
          <Table stripe :columns="columns" :data="data" style="font-size: 14px;border-radius: 6px;"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RistGaugeChart from './RistGaugeChart.vue'
import RistPerChart from './RistPerChart.vue'
import RoomCard from './RoomCard.vue'
import noData from "@/components/bsp-empty/index.vue";

export default {
  components: {
    noData,
    RoomCard,
    RistGaugeChart,
    RistPerChart
  },
  props: {},
  data() {
    return {
      itemData: [],
      itemPrisonerData: [],
      curRoomId: '',
      roomId: '',
      roomIds: [],
      areaId: '',
      orgCodeList: [],
      riskId: '',
      riskIds: [],
      curRoomIds: [],
      orgScore: {},
      curOrg: '',
      page: {
        pageNo: 1,
        pageSize: 10,
      },
      total: 0,
      dataList: [],
      activeName: 'jsfxlb',
      activePictureName: 'fxjszb',
      riskScore: 0,
      columns: [
        {
          title: '序号',
          key: 'name'
        },
        {
          title: '发生事件',
          key: 'age'
        },
        {
          title: '所情地点',
          key: 'address'
        },
        {
          title: '所情等级',
          key: 'address1'
        },
        {
          title: '发送时间',
          key: 'address2'
        }
      ],
      data: []
    }
  },
  methods: {
    getItemData() {
      let url = this.activePictureName === 'fxjszb' ? this.$path.fjcy_integral_riskModel_getSickRoom : this.$path.fjcy_integral_riskModel_getSickPrisoner
      this.$store.dispatch('getRequest', {
        url: url,
        params: {
          orgCode: this.$store.state.common.orgCode
        }
      }).then(res => {
        if (res.success) {
          this.itemData = res.data
        }
      })
    },
    changePicTabs(name) {
      this.activePictureName = name
      this.getItemData()
    },
    changeTabs(name) {
      this.activeName = name
      this.page.pageNo = 1
      this.searchData()
    },
    handleSearch(pageNo) {
      this.page.pageNo = pageNo
      let condis = []
      // 监区
      if (this.areaId && this.activeName === 'jsfxlb') {
        this.orgCodeList.forEach(item => {
          if (item.areaCode === this.areaId) {
            condis.push('{"name":"area_name","op":"like","value":"' + item.areaName + '","valueType":"string"}')
          }
        })
      }
      // 监室
      if (this.roomId) {
        this.roomIds.forEach(item => {
          if (item.roomCode === this.roomId) {
            condis.push('{"name":"room_name","op":"like","value":"' + item.roomName + '","valueType":"string"}')
          }
        })
      }
      // 等级level_name
      if (this.riskId) {
        this.riskIds.forEach(item => {
          if (item.orderId === this.riskId) {
            condis.push('{"name":"level_name","op":"like","value":"' + item.name + '","valueType":"string"}')
          }
        })
      }
      let condisLast = ''
      if (condis.length > 0) {
        condisLast = '[' + condis.join(',') + ']'
      }
      let modelId = ''
      if (this.activeName === 'jsfxlb') {
        modelId = serverConfig.APP_CODE + ':fjcyjsfx:list'
      } else {
        modelId = serverConfig.APP_CODE + ':fjcyryfx:list'
      }
      let params = {
        condis: condisLast,
        modelId: modelId,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize
      }
      this.$store.dispatch('postRequest', {url: this.$path.get_query_grid, params: params})
        .then(res => {
          if (res.success) {
            this.dataList = res.rows
            this.total = res.total
          }
        })
    },
    getNo(pageNo) {
      this.$set(this.page, 'pageNo', pageNo)
      this.handleSearch(pageNo)
    },
    getSize(pageSize) {
      this.$set(this.page, 'pageSize', pageSize)
    },
    searchData() {
      this.handleSearch(1)
    },
    changeArea(data) {
      this.getAreaList()
    },
    changeRoom(roomId) {
      this.getOrgScore(roomId)
    },
    getRoomData() {
      this.$store.dispatch("authGetRequest", {
        url: this.$path.fjcy_integral_riskModel_get_orgCode
      }).then(res => {
        if (res.success) {
          this.curRoomIds = res.data
          this.curRoomId = res.data[0].roomCode
        } else {
          this.$Message.error("获取数据失败")
        }
      })
    },
    getOrgScore(roomId) {
      this.$store.dispatch("authGetRequest", {
        url: this.$path.fjcy_integral_riskModel_getOrgScore,
        params: {
          orgCode: this.$store.state.common.orgCode,
          roomCode: roomId
        }
      }).then(res => {
        if (res.success) {
          this.orgScore = res.data
          this.riskIds = res.data.level
          this.riskScore = res.data.score
        } else {
          this.$Message.error("获取数据失败")
        }
      })
    },
    getAreaByOrgCode() {
      this.$store.dispatch('getRequest', {
        url: '/acp-com/base/area/getAreaByOrgCode',
        params: {
          orgCode: this.$store.state.common.orgCode
        }
      }).then(res => {
        if (res.success) {
          this.orgCodeList = res.data
        }
      })
    },
    getAreaList() {
      this.$store.dispatch('authPostRequest', {
        url: '/acp-com/base/pm/areaPrisonRoom/list',
        params: {
          orgCode: this.$store.state.common.orgCode,
          areaId: this.areaId
        }
      }).then(res => {
        if (res.success) {
          this.roomIds = res.data
        }
      })
    },
  },
  mounted() {
    this.getItemData()
    this.getRoomData()
    this.getOrgScore()
    this.getAreaByOrgCode()
    this.handleSearch(this.page.pageNo)
  }
}
</script>

<style scoped>

.fm-content-card-item {
  max-height: 88px;
  width: 165px;
  max-width: 175px;
  background: #fff;
  border-radius: 6px;
  margin: 5px;
  border: 1px solid transparent;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), /* 顶部阴影 - 轻微 */ 2px 0 4px rgba(0, 0, 0, 0.1), /* 左侧阴影 - 轻微 */ -2px 0 4px rgba(0, 0, 0, 0.1), /* 右侧阴影 - 轻微 */ 0 6px 12px rgba(0, 0, 0, 0.2); /* 底部阴影 - 更明显 */
}

.fm-content-card-item:hover {
  border: 1px solid #91baf8;
  cursor: pointer;
}

.fm-content-card-item:nth-child(5n) {
}

.button-select {
  width: 25%;
  margin-right: 10px;
  height: 40px;
  font-size: 12px;
}

.room-cz-button-search {
  padding: 10px;
  text-align: center;
}

.card {
  padding: 6px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #eff6ff;
  --tw-gradient-to: rgb(239 246 255 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.gyl {
  display: inline-block;
  width: 0.50rem;
  height: 0.50rem;
  background: url('../../../../assets/images/fjcy/gyl.png') no-repeat;
  background-size: 100% 100%;
  vertical-align: text-bottom;
  margin-right: 0.05rem;
}

.cs {
  display: inline-block;
  width: 0.50rem;
  height: 0.50rem;
  background: url('../../../../assets/images/fjcy/cs.png') no-repeat;
  background-size: 100% 100%;
  vertical-align: text-bottom;
  margin-right: 0.05rem;
}

.dqajs {
  display: inline-block;
  width: 0.50rem;
  height: 0.50rem;
  background: url('../../../../assets/images/fjcy/dqajs.png') no-repeat;
  background-size: 100% 100%;
  vertical-align: text-bottom;
  margin-right: 0.05rem;
}

.dqgks {
  display: inline-block;
  width: 0.50rem;
  height: 0.50rem;
  background: url('../../../../assets/images/fjcy/dqgks.png') no-repeat;
  background-size: 100% 100%;
  vertical-align: text-bottom;
  margin-right: 0.05rem;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.cards {
  width: 55%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  flex: 1;
}

.risk-tp {
  width: 45%;
}

.risk-fjtj {
  display: flex;
  line-height: 19px;
}

.risk-statitis {
  display: flex;
  padding: 10px;
  border-style: solid;
  border-color: rgb(243 244 246);
  border-top-width: 2px;
  border-right-width: 0px;
  border-left-width: 0px;
  border-bottom-width: 2px;
  flex: 1;

  .left {
    text-align: left;
  }
}

.prison-select-center {
  background-color: #e9eef5;
  width: 100%;
  display: flex;
  height: 100%;
}

.prison-select-center-top-bottom {
  margin-top: 5px;
  border-radius: 6px;
}

.prison-select-center-bottom-left {
  width: 45%;
  height: 100%;
  background: #fff;
  margin: 2px;
  border-radius: 6px;
}

.prison-select-center-bottom-right {
  width: 55%;
  margin: 2px;
  border-radius: 6px;
}

.right-top {
  min-height: 290px;
  border-radius: 6px;
  background-color: #fff;
}

.right-bottom {
  margin-top: 2px;
  background-color: #fff;
  height: 64.75%;
  margin-bottom: 1px;
}
</style>
