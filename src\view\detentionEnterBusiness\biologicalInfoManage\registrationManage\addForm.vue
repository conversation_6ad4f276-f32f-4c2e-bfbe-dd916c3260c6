<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <div class="fm-content-info bsp-base-content" style="top:30px !important;padding:unset !important;">
      <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="200" label-colon
        style="padding: 0 .625rem;">
        <div class="fm-content-box">
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />基本信息
          </p>
          <Row>
            <Col span="3" class="col-title"><span>监室号</span></Col>
            <Col span="5"><span>{{ combineInfoData.roomName }}</span></Col>
            <Col span="3" class="col-title"><span>姓名</span></Col>
            <Col span="5"><span>{{ combineInfoData.xm }}</span></Col>
            <Col span="3" class="col-title"><span>曾用名/别名/绰号</span></Col>
            <Col span="5"><span>{{ combineInfoData.bm }}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>性别</span></Col>
            <Col span="5"><span>{{ combineInfoData.xbName }}</span></Col>
            <Col span="3" class="col-title"><span>出生日期</span></Col>
            <Col span="5"><span>{{ combineInfoData.csrq }}</span></Col>
            <Col span="3" class="col-title"><span>涉嫌罪名</span></Col>
            <Col span="5"><span>{{ combineInfoData.sxzm }}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>收押登记时间</span></Col>
            <Col span="5"><span>{{ rowData.add_time }}</span></Col>
            <Col span="0" class="col-title"><span></span></Col>
            <Col span="8">
            <Button style="margin: 0 13px 0 16px" type="primary" @click.native="showBaseInfoDialog()">查看入所登记详情</Button>
            </Col>
          </Row>
        </div>
        <div class="bsp-base-subtit" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />信息采集详情
          </p>
          <div class="form">
            <Row></Row>
            <Button type="primary" @click.native="getGatherStatus()" style="margin-bottom: 10px;">获取采集状态</Button>
            <Row>
              <Col span="24">
              <Table :columns="columns" :data="dataTable" border>
                <template slot-scope="{ row, index }" slot="xxxx">
                   <div class="img-box-lsit" v-if="row.xxxx">
                    <img src="" v-for="i in 20" :key="i">
                   </div>
                </template>
                <template slot-scope="{ row, index }" slot="bz">
                  <Input type="text" v-model="row.bz" @on-blur="bzChange(row, index)" />
                </template>

                <template slot-scope="{ row, index }" slot="action">
                  <div class="action-gather">
                    <Button type="primary" style="margin-right: 5px" @click="startGather(row.itemName)" v-if="row.cjxmlx != '04'">开始采集</Button>
                    <!--                    <Button type="primary"  @click="upload(index)">手动上传</Button>-->
                    <file-upload :defaultList="row.swtzUrl" :serviceMark="serviceMark" :bucketName="bucketName"
                      :beforeUpload="beforeUpload" v-if="showFile" @fileSuccess="fileSuccessFile"
                      @fileRemove="fileRemoveFile" @fileComplete="(data) => fileCompleteFile(data, index)" />
                  </div>

                </template>
              </Table>
              </Col>
            </Row>
          </div>
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />手环设备绑定
          </p>
          <div class="form">
            <Row>
              <Col span="8">
              <FormItem label="手环ID" prop="shid">
                <Input type="text" v-model="formData.shid" placeholder="请填写" />
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="绑定状态" prop="shbdzt">
                <s-dicgrid v-model="formData.shbdzt" @change="$refs.formData.validateField('shbdzt')" :isSearch="true"
                  dicName="ZD_SYRSSHBDZT" />
              </FormItem>
              <!--                <FormItem label="绑定状态" prop="shbdzt" >-->
              <!--                  <Input type="text" v-model="formData.shbdzt" placeholder="请填写"/>-->
              <!--                </FormItem>-->
              </Col>
              <Col span="8">
              <Button type="primary" style="margin: 0 13px 0 16px" @click="wristbandBinding()">开始绑定</Button>
              </Col>
            </Row>
            <Row>
              <Col span="8">
              <FormItem label="绑定时间" prop="sdbdsj">
                <div class="ivu-form-item-label">{{ formData.sdbdsj }}</div>
              </FormItem>
              </Col>

            </Row>

          </div>
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />经办信息
          </p>
          <div class="form">
            <Row>
              <Col span="8">
              <FormItem label="经办人" prop="jbr" :rules="[{ trigger: 'blur', message: '经办人为必填', required: true }]">
                <Input type="text" v-model="formData.jbr" placeholder="系统自动获取登录人信息" />
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="经办时间" prop="jbsj">
                <div class="ivu-form-item-label">{{ formData.jbsj }}</div>
              </FormItem>
              </Col>

            </Row>
          </div>
        </div>
      </Form>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
      <Button style="margin: 0 20px" :loading="loadingSave" @click="handleSubmit(false)">暂 存</Button>
      <Button style="margin: 0 20px" type="primary" :loading="loading" @click="handleSubmit(true)">提交</Button>
    </div>

    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-sy-modal" width="80%"
      title="入所登记详情">
      <div style="height: 75vh;overflow: auto;">
        <div class="fm-content-info bsp-base-content" style="padding:0px !important;">
          <baseInfoDetail :rowData="rowData" :inDialog="true"></baseInfoDetail>
        </div>
      </div>
    </Modal>

    <start-approval ref="approval" :assigneeUserId="approvalData.assigneeUserId"
      :assigneeUserName="approvalData.assigneeUserName" :assigneeOrgId="approvalData.assigneeOrgId"
      :assigneeOrgName="approvalData.assigneeOrgName" :assigneeAreaId="approvalData.assigneeAreaId"
      :assigneeAreaName="approvalData.assigneeAreaName" :definition="approvalData.definition" :bindEvent="false"
      :showcc="false" :error="startError" :businessId="approvalData.businessId" :variables="approvalData.variables"
      :startUpSuccess="startUpSuccess" :beforeOpen="beforeOpen" :msgUrl="msgUrl" :msgTit="msgTit"
      :module="module"></start-approval>
  </div>
</template>

<script>
import HisignPICS, { getCurrentTime } from '@/assets/js/device/inforCollection/pics.js';
import dayjs from 'dayjs';
import { startApproval } from 'gs-start-approval'
import { sImageUploadLocal } from '@/components/upload/image'
import { fileUpload } from 'sd-minio-upfile'
import { imgUpload } from 'sd-minio-upimg'
import { mapActions } from "vuex";
import { getUserCache } from '@/libs/util'
import baseInfoDetail from '../../detentionEnterManage/recordManage/detail'
import { Row } from 'view-design';
import { url } from 'video.js';
export default {
  components: {
    baseInfoDetail, sImageUploadLocal, fileUpload, imgUpload, startApproval
  },
  props: {
    rowData: {
      type: [Array, Object],
      default: {}
    },
    entireProcess: {
      default: false,
    }
  },
  data() {
    return {
      combineInfoData: {

      },
      formData: {
        shbdzt: "01"
      },
      loading: false,
      loadingSave: false,
      ruleValidate: {},
      openModal: false,
      showFile: false,
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      columns: [
        {
          type: 'index',
          width: 70,
          align: 'center',
          title: '序号'
        },
        {
          title: '采集项目',
          key: 'cjxm',
          align: 'center',
          width: 200,
        },
        {
          title: '详细信息',
          slot: 'xxxx',
          align: 'center',
        },
        {
          title: '备注',
          slot: 'bz',
          align: 'center',
          width: 200,
        },
        {
          title: '操作',
          slot: 'action',
          width: 300,
          align: 'center'
        }
      ],
      dataTable: [],
      msgUrl: '/#/detentionBusiness/detentionEnterRegister',
      msgTit: '【审批】收押入所',
      businessId: this.rowData.rybh,
      module: serverConfig.APP_MARK,
      approvalData: {
        definition: [
          {
            name: '入所审批流程',
            defKey: 'shouyarusuoshenpiliuchengkanshousuo'
          }
        ],
        assigneeOrgId: this.$store.state.common.orgCode,
        assigneeOrgName: this.$store.state.common.orgName,
        assigneeUserId: this.$store.state.common.idCard,
        assigneeUserName: this.$store.state.common.userName,
        businessId: this.rowData.rybh,
        fApp: serverConfig.APP_MARK,
        fXxpt: 'pc',
        variables: {
          eventCode: this.rowData.rybh,
          busType: '105_001'
        }
      },
      isTest: true,  //演示用
      mockData: {},
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    getCurrentTimeFormatted() {
      const now = new Date();

      const year = now.getFullYear();             // 获取年份
      const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，要加1，并补零
      const day = String(now.getDate()).padStart(2, '0');        // 日期补零

      const hours = String(now.getHours()).padStart(2, '0');     // 小时补零
      const minutes = String(now.getMinutes()).padStart(2, '0'); // 分钟补零
      const seconds = String(now.getSeconds()).padStart(2, '0'); // 秒数补零

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleClose() {
      this.$emit('close', false)
    },
    handleNext() {
      this.$emit('nextStep', false)
    },
    showBaseInfoDialog() {
      this.openModal = true;
    },
    getCombineInfoDetail(rybh) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_getCombineInfo,
        params: {
          rybh: rybh
        }
      }).then(resp => {
        if (resp.code == 0) {
          if (resp.data) {
            this.combineInfoData = resp.data
          }
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    bzChange(row, index) {
      if (row.bz != this.dataTable[index].bz) {
        this.dataTable[index].bz = row.bz
        this.submitBiometricInfo(this.dataTable[index])
      }
    },
    beforeUpload() { },
    fileSuccessFile() { },
    fileRemoveFile() { },
    fileCompleteFile(data, index) {
      if (data && data.length > 0) {
        this.dataTable[index].swtz = JSON.stringify(data)
        this.submitBiometricInfo(this.dataTable[index])
      }
    },
    submitBiometricInfo(data) {
      data.status = "02"
      data.businessType = "kss"
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_biometricInfoCreate,
        params: data
      }).then(resp => {
        this.loading = false
        this.loadingSave = false
        if (resp.code == 0) {
          this.$Message.success('提交成功!');

        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    getBiometricInfo(rybh) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_getBiometricInfoByRybh,
        params: {
          rybh: rybh
        }
      }).then(resp => {
        if (resp.code == 0) {
          console.log("生物信息采集信息---")
          if (resp.data && resp.data.length > 0) {
            for (let i = 0; i < this.dataTable.length; i++) {
              let data = resp.data.find(t => t.cjxmlx == this.dataTable[i].cjxmlx)
              if (data) {
                this.dataTable[i].id = data.id;
                this.dataTable[i].bz = data.bz;
                this.dataTable[i].swtz = data.swtz;
                this.dataTable[i].swtzUrl = []
                if (data.swtz) {
                  this.dataTable[i].swtzUrl = JSON.parse(data.swtz)
                }
              }
            }
            this.showFile = true
            console.log(this.dataTable, "this.dataTable------")
          }
        } else {
          this.showFile = true
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    getPrisonerInfo(rybh) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_getPrisonerInfo,
        params: {
          rybh: rybh
        }
      }).then(resp => {
        if (resp.code == 0) {
          this.formData = resp.data
          this.formData.jbsj = resp.data.jbsj ? resp.data.jbsj : this.getCurrentTimeFormatted();
          this.formData.sdbdsj = resp.data.sdbdsj ? resp.data.sdbdsj : this.getCurrentTimeFormatted();
          this.formData.shbdzt = resp.data.shbdzt ? resp.data.shbdzt : "01";
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    startGather(itemName) {
      console.log("开始采集")
      this.mockData.CJSJ = getCurrentTime(this.mockData.CJSJ)
      let resp = HisignPICS.StartItemCollect(this.rowData.rybh, this.mockData, itemName, this.callBack);
      // var resp = HisignPICS.CheckCollectSvr();
      console.log(JSON.stringify(resp))

    },
    getGatherStatus() {
      this.authGetRequest({ url: this.$path.db_getInRecordStatusKss, params: { rybh: this.rowData.rybh } }).then(res => {
        if (res.success) {
          if(res.data.xxcj == '01') {
            this.$Message.error('未采集数据');
          }

        }
      })
    },
    callBack(res) {
      console.error(res)
      if (res.code) {
        console.log('-----------------------采集-----------------------------');
          console.log(res);
          
      }
    },
    getCollectedPersonDetail(rybh) {
      let params = {
        jgrybm: rybh,
        businessType: 'kss',
        orgCode: getUserCache.getOrgCode()

      }
      this.$store.dispatch('authGetRequest', {
        url: this.$path.db_getCollectedPersonDetail,
        params: params
      }).then(resp => {
        if (resp.success) {
          this.mockData = resp.data
          console.log("------获取采集人员信息---", resp)
        } else {
          this.$Modal.error({
          })
        }
      })
    },
    upload() {

    },
    wristbandBinding() {

    },
    handleSubmit(tag) {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.saveData(tag)
        } else {
          this.loading = false
          this.$Message.error('请填写完整!!');
        }
      })
    },
    saveData(tag) {
      this.formData.dataSources ? '' : this.$set(this.formData, 'dataSources', 0)
      // let params=this.formData
      let params = {}
      params.rybh = this.rowData.rybh
      params.sdbdsj = this.formData.sdbdsj
      params.shbdzt = this.formData.shbdzt
      params.shid = this.formData.shid
      params.sdbdsj = this.formData.sdbdsj
      params.businessType = "kss"
      if (tag) {
        this.loading = true
        params.status = "03"
      } else {
        this.loadingSave = true
        params.status = "02"
      }
      params.rslx = this.rowData.rslx
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_biometricInfoAddInformation,
        params: params
      }).then(resp => {
        this.loading = false
        this.loadingSave = false
        if (resp.code == 0) {
          this.$Message.success('提交成功!');
          if (this.entireProcess && tag) {
            if (resp.data.isApproval) {
              if (this.rowData.rslx == "01") {
                this.businessId = this.rowData.rybh
                this.msgUrl = '/#/detentionBusiness/detentionEnterRegister?eventCode=' + this.rowData.rybh
                this.$refs['approval'].openStartApproval()
              }
              else {
                this.handleClose();
              }
            } else {
              this.handleNext();
            }
          } else {
            this.handleClose();
          }

        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    startError(data) {
      this.errorModal({ content: '流程启动失败。原因:' + data.msg }).then(() => {
        location.reload()
      })
    },
    startUpSuccess(data) {
      return new Promise((resolve, reject) => {
        let that = this
        setTimeout(() => {
          this.updateRegStatus(data)
        }, 500)
      })
    },
    updateRegStatus(data) {
      let params = {
        rybh: this.rowData.rybh,
        taskId: "",
        actInstId: data.actInstId
      }
      //调用接口
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_detainRegKssUpdateWorkflowInfo,
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.$Message.success('提交成功!');
          if (this.isTest) {
            let info = {
              rybh: this.rowData.rybh,
              rslx: this.rowData.rslx,
              act_inst_id: params.actInstId
            }
            this.handleNext(info);
          } else {
            this.handleClose();
          }
        } else {
          this.$Message.error(resp.msg);
        }
      })
    },
    beforeOpen() {
      return new Promise((resolve, reject) => {
        this.$set(this.approvalData, 'businessId', this.rowData.rybh)
        this.msgUrl = '/#/detentionBusiness/detentionEnterRegister?eventCode=' + this.rowData.rybh
        this.msgTit = `【收押入所审批】民警：${this.formData.jbr}于${this.formData.jbsj}提交了对${this.combineInfoData.xm}收押入所的申请，请尽快审批！`
        resolve(true)
      })
    },
  },
  mounted() {
    this.formData.jbsj = this.getCurrentTimeFormatted();
    this.formData.sdbdsj = this.getCurrentTimeFormatted();
    this.$set(this.formData, 'jbr', getUserCache.getUserName())
    this.dataTable = [{
      cjxmlx: '01',
      cjxm: '指掌纹信息',
      xxxx: '',
      bz: '',
      rybh: this.rowData.rybh,
      swtzUrl: [],
      itemName: 'finger'
    }, {
      cjxmlx: '02',
      cjxm: '虹膜信息',
      xxxx: '',
      bz: '',
      rybh: this.rowData.rybh,
      swtzUrl: [],
      itemName: 'iris'
    }, {
      cjxmlx: '03',
      cjxm: '人像信息',
      xxxx: '',
      bz: '',
      rybh: this.rowData.rybh,
      swtzUrl: [],
      itemName: 'photo'
    }, {
      cjxmlx: '04',
      cjxm: 'DNA信息',
      xxxx: '',
      bz: '',
      rybh: this.rowData.rybh,
      swtzUrl: [],
      itemName: 'dna'
    }]
    this.getCombineInfoDetail(this.rowData.rybh)
    this.getCollectedPersonDetail(this.rowData.rybh)
    if (this.entireProcess) {
      if (this.rowData.rybh) {
        this.getPrisonerInfo(this.rowData.rybh)
        this.showFile = false
        this.getBiometricInfo(this.rowData.rybh)
      } else {
        this.showFile = true
      }
    } else {
      if (this.rowData.status == "02") {
        this.getPrisonerInfo(this.rowData.rybh)
        this.showFile = false
        this.getBiometricInfo(this.rowData.rybh)
      } else {
        this.showFile = true
      }
    }



  }
}
</script>

<style scoped >
@import "~@/assets/style/formInfo.css";

.fm-content-info {
  padding: 26px 0 26px 0 !important;
}

.action-gather {
  display: flex;
}

.img-box-lsit {
  display: flex;
  gap: 10px; 
  overflow: auto;
  padding: 3px 0;


}
.img-box-lsit::-webkit-scrollbar {
     height: 4px; /*  设置横轴（x轴）轴滚动条 */
 }


.img-box-lsit img {
  width: 50px;
  height: 50px;
  
}
</style>
