<template>
  <div style="width: 100%;height: 100%;">
    <div class="table-container" v-if="tabContainer">
      <Tabs :value="tabName">
        <TabPane label="文明个人" name="prisonCell">
          <div class="header-prison-date">
            <h1>{{ yearNum }}年文明个人月度评比</h1>
            <DatePicker type="year" v-model="currerentYear" placement="top" format="yyyy"
              style="width: 200px;margin-left: 10px;z-index: 100;" />
          </div>
          <div v-for="item in civilizedRoomList" :key="item.id" class="civilized-room-list">
            <Alert type="warning" v-if="item.list == null">
              <div style="display: flex;align-items: center;justify-content: space-between;">
                <div>{{ item.evalMonth }}未评比，请及时登记评比</div>
              </div>
            </Alert>
            <header>
              <h2>
                {{ item.evalMonthName }}
              </h2>
              <div v-if="item.list == null">
                <Button type="text" ghost @click="handleAddDjpb(item)">登记评比</Button>
              </div>
            </header>
            <div v-if="item.list == null" class="no-data-png">
              <img src="@/assets/images/noData.png" alt="">
            </div>
            <div class="max-room-info">
              <!-- <Button type="text" ghost @click="handleAddDjpb(item)">登记评比</Button> -->
              <div v-for="itemChild in item['list']" :key="itemChild.id" class="room-info-list">
                <div class="sex-room-img">
                  <img v-if="itemChild.prisonerVwRespVO" :src="itemChild.prisonerVwRespVO.frontPhoto" alt="">
                  <img v-else src="../../../assets/images/main.png" alt="" srcset="">
                </div>
                <div class="right-room-info">
                  <h2>{{ itemChild.jgryxm }}</h2>
                  <p>
                    {{ itemChild.roomName }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </TabPane>
        <TabPane label="月度评比台账" name="tzContainer">
          <s-DataGrid ref="grid" funcMark="wmgrydpbtz" :customFunc="true" :params="params">
            <template slot="customRowFunc" slot-scope="{ func, row, index }">
              <Button type="primary"
                v-if="func.includes(globalAppCode + ':wmgrydpbtz:djpb') && row.status == '01' || row.status == '03'"
                @click.native="handlePb(index, row)">登记评比</Button>
              <Button type="primary" v-if="func.includes(globalAppCode + ':wmgrydpbtz:sp') && row.status == '02'"
                @click.native="handleSp(index, row)">审批</Button>
              <Button type="primary" v-if="func.includes(globalAppCode + ':wmgrydpbtz:xq') && row.status == '04'"
                @click.native="handleXq(index, row)">详情</Button>
            </template>
          </s-DataGrid>
        </TabPane>
      </Tabs>
    </div>
    <div v-if="djpbContainer" class="max-djpb-Container">
      <div class="prison-Cell-List">
        <IndividualsList @getSelctedPrison="getSelctedPrison"></IndividualsList>
      </div>
      <div class="table-wmjsdj-container">
        <h3>文明个人登记</h3>
        <Table border height="300" :columns="columns" :data="selecrPriObj"></Table>
      </div>
      <div class="bsp-base-fotter">
        <Button @click="handleReset" style="margin-right: 10px;">取消</Button>
        <Button type="primary" @click="handleSubmit">确认</Button>
      </div>
    </div>
    <div v-if="spContainer">
      <h5>{{ monthName }}月度文明个人评比审批</h5>
      <p class="fm-content-wrap-title">
        <Icon type="md-list-box" size="24" color="#2b5fda" />文明个人评选信息
      </p>
      <Table border height="300" :columns="columnsPri" :data="selecrPriList"></Table>
      <Form ref="formData" inline style="margin-top: 10px;">
        <div class="fm-content-box">
          <Row>
            <Col span="3" class="col-title"><span>申请人</span></Col>
            <Col span="9"><span>
              {{ spInfo.applyUser }}
            </span>
            </Col>
            <Col span="3" class="col-title"><span>申请时间</span></Col>
            <Col span="9"><span>
              {{ spInfo.applyTime }}
            </span></Col>
          </Row>
        </div>
      </Form>
      <div class="sp-form-container">
        <header>
          <h2>审批</h2>
        </header>
        <div class="border-sp-container">
          <Form ref="formValidate" :model="formValidate" :label-width="120">
            <FormItem label="审批结果" prop="approvelResult"
              :rules="{ required: true, message: '请选择审批结果', trigger: 'change' }">
              <RadioGroup v-model="formValidate.approvelResult">
                <Radio label="04">通过</Radio>
                <Radio label="03">不通过</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem label="审批意见" prop="approvelComment"
              :rules="{ required: true, message: '请输入审批意见', trigger: 'blur' }">
              <Input v-model="formValidate.approvelComment" type="textarea"
                :autosize="{ minRows: 2, maxRows: 5 }"></Input>
            </FormItem>
          </Form>
        </div>
      </div>
      <div class="bsp-base-fotter">
        <Button @click="handleSpReset('formValidate')" style="margin-right: 10px;">取消</Button>
        <Button type="primary" @click="handleSpSubmit('formValidate')">确认</Button>
      </div>
    </div>
    <div v-if="xqContainer">
      <h5>{{ monthName }}月度文明监室评比详情</h5>
      <p class="fm-content-wrap-title">
        <Icon type="md-list-box" size="24" color="#2b5fda" />文明监室评选信息
      </p>
      <Table border height="300" :columns="columnsPri" :data="selecrPriList"></Table>
      <p class="fm-content-wrap-title">
        <Icon type="md-list-box" size="24" color="#2b5fda" />业务流程轨迹
      </p>
      <s-general-history v-if="actInstId" :key="timer" :showModifyBtn="false" :showRevokeBtn="false"
        :actInstId="actInstId">
      </s-general-history>
      <div class="bsp-base-fotter">
        <Button @click="handleBack" style="margin-right: 10px;">返回</Button>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { mapActions } from 'vuex'
import IndividualsList from './components/IndividualsList.vue';
import { fileUpload } from 'sd-minio-upfile'
import { sDataGrid } from 'sd-data-grid'
import { sGeneralHistory } from 'sd-general-history'
export default {
  name: "civilizedCell",
  data() {
    return {
      tabContainer: true,
      djpbContainer: false,
      xqContainer: false,
      spContainer: false,
      tabName: "prisonCell",
      monthName: "",
      actInstId: "",
      timer: '',
      currerentYear: dayjs().format('YYYY'),
      civilizedRoomList: [],
      selecedPri: [],
      selecrPriObj: [],
      selecrPriList: [],
      againRegister: false,
      formValidate: {
        approvelResult: "04",
        approvelComment: "同意",
      },
      columnsPri: [
        {
          title: '姓名',
          key: 'jgryxm',
        },
        {
          title: '监室',
          key: 'roomName',
        },
        {
          title: '本月奖励数',
          key: 'numberOfReward',
        },
        {
          title: '本月违规数',
          key: 'numberOfViolations',
        },
        {
          title: '本月惩罚数',
          key: 'numberOfPunishment',
        },
        {
          title: '评选理由',
          key: 'selectionReason',
        },
        {
          title: '附件',
          key: 'attUrl',
          render: (h, params) => {
            return h(fileUpload, {
              props: {
                serviceMark: serverConfig.OSS_SERVICE_MARK, // 绑定当前行数据
                bucketName: serverConfig.bucketName,
                defaultList: params.row.attUrl,
                isDetail: true
              },
              // on: {
              //     'fileCompleteFile': (data) => {
              //         // this.fileSuccess(data)
              //         this.selecrPriObj[params.index].attUrl = JSON.stringify(data);
              //     }
              // }
            });
          }
        },
      ],
      columns: [
        {
          title: '姓名',
          key: 'jgryxm',
        },
        {
          title: '监室',
          key: 'roomName',
        },
        {
          title: '本月奖励数',
          key: 'numberOfReward',
        },
        {
          title: '本月违规数',
          key: 'numberOfViolations',
        },
        {
          title: '本月惩罚数',
          key: 'numberOfPunishment',
        },
        {
          title: '评选理由',
          key: 'selectionReason',
          render: (h, params) => {
            return h('Input', {
              props: {
                value: params.row.selectionReason, // 绑定当前行数据
                size: 'small'
              },
              on: {
                // 监听输入事件，更新数据
                input: (val) => {
                  this.selecrPriObj[params.index].selectionReason = val;
                },
                // 监听失焦事件（可选）
                'on-blur': () => {
                  console.log('失焦触发');
                }
              }
            });
          }
        },
        {
          title: '附件',
          key: 'attUrl',
          render: (h, params) => {
            return h(fileUpload, {
              props: {
                serviceMark: serverConfig.OSS_SERVICE_MARK, // 绑定当前行数据
                bucketName: serverConfig.bucketName,
              },
              on: {
                'fileSuccess': (data) => {
                  this.selecrPriObj[params.index].attUrl = JSON.stringify(data);
                }
              }
            });
          }
        },
        {
          title: '操作',
          key: 'action',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text' },
                on: { click: () => this.deletePrison(params.row, params.index) }
              }, '删除')
            ])
          }
        }
      ],
      monthWpbId: "",
      params: {},
      spInfo: {},
      approvalId: ""
    }
  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleGetCivilizedRoomList() {
      let params = {
        applyTime: [],
        applyUser: "",
        applyUserSfzh: "",
        evalMonth: "",
        orgCode: "",
        civilizedPersonne: "",
        status: "",
        year: this.currerentYear
      }
      this.authPostRequest({ url: this.$path.get_IndividualsList, params }).then(res => {
        if (res.success) {
          this.civilizedRoomList = res.data
          this.civilizedRoomList.forEach(item => {
            let names = item.evalMonth.split('-')
            item.evalMonthName = `${names[0]}年${names[1]}月`
            if (item.list) {
              item.list.forEach(itemChild => {
                if (itemChild.prisonerVwRespVO) {
                  // itemChild.prisonerVwRespVO.frontPhoto = itemChild.prisonerVwRespVO.frontPhoto.replace("192.168.3.251", "172.26.0.22");
                }
              })
            }
          })
        }
      })
    },
    handleAddDjpb(item) {
      this.monthWpbId = item.id
      this.tabContainer = false
      this.djpbContainer = true
    },
    handleReset() {
      this.tabContainer = true
      this.djpbContainer = false
      this.selecrPriObj = []
      this.selecedPri = []
    },
    handleSubmit() {
      if (this.selecrPriObj.length > 0) {
        this.authPostRequest({ url: this.againRegister ? this.$path.civilizedPersonne_againEvaluation : this.$path.civilizedPersonne_registEvaluation, params: { id: this.monthWpbId, list: this.selecrPriObj } }).then(res => {
          if (res.success) {
            this.$Message.success("登记成功")
            this.handleGetCivilizedRoomList()
            this.tabContainer = true
            this.djpbContainer = false
            this.selecrPriObj = []
            this.selecedPri = []
          } else {
            this.$Message.success(res.message)
          }
        })
      } else {
        this.$Message.error("请选中监室后在进行登记")
        return
      }
    },
    fileSuccess(data) {
      console.error(data);

    },
    getSelctedPrison(selecedPri, selecrPriObj) {
      this.selecedPri = selecedPri
      this.selecrPriObj = selecrPriObj.map(item => {
        let itemObj = {
          "jgrybm": item.jgrybm,
          "jgryxm": item.xm,
          "numberOfPunishment": item.bycfs,
          "numberOfReward": item.byjls,
          "numberOfViolations": item.bywgs,
          attUrl: item.attUrl,
          selectionReason: item.selectionReason,
          roomId: item.jsh,
          roomName: item.roomName
        }
        return itemObj
      })
    },
    deletePrison(row, index) {
      this.selecrPriObj.splice(index, 1)
      this.selecedPri.splice(index, 1)
    },
    handleXq(idx, row) {
      this.monthName = row.cn_eval_month
      this.actInstId = row.act_inst_id
      this.authGetRequest({ url: this.$path.get_civilizedPersonne_detail, params: { id: row.id } }).then(res => {
        if (res.success) {
          this.selecrPriList = res.data.list
          this.selecrPriList.forEach(item => {
            item.attUrl = [JSON.parse(item.attUrl)]
          })
          this.tabContainer = false
          this.xqContainer = true
        }
      })
    },
    handleBack() {
      this.tabName = 'tzContainer'
      this.tabContainer = true
      this.xqContainer = false
    },
    handleSp(idx, row) {
      this.monthName = row.cn_eval_month
      this.actInstId = row.act_inst_id
      this.approvalId = row.id
      this.authGetRequest({ url: this.$path.get_civilizedPersonne_detail, params: { id: row.id } }).then(res => {
        if (res.success) {
          this.selecrPriList = res.data.list
          this.selecrPriList.forEach(item => {
            item.attUrl = [JSON.parse(item.attUrl)]
          })
          this.spInfo = res.data
          this.tabName = 'tzContainer'
          this.tabContainer = false
          this.spContainer = true
        }
      })
    },
    handleCommonReset(name) {
      this.$refs[name].resetFields();
      this.formValidate.approvelResult = "04"
      this.formValidate.approvelComment = "同意"
      this.tabName = "tzContainer"
      this.tabContainer = true
      this.spContainer = false
    },
    handleSpReset(name) {
      this.handleCommonReset(name)
    },
    handleSpSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.authPostRequest({ url: this.$path.handle_civilizedPersonne_approval, params: { ...this.formValidate, id: this.approvalId } }).then(res => {
            if (res.success) {
              this.handleCommonReset(name)
              this.$Message.success('审批成功')
            } else {
              this.$Message.error(res.message)
            }
          })
        } else {
          this.$Message.error('验证未通过');
        }
      })
    },
    handlePb(idx, row) {
      this.monthWpbId = row.id
      this.tabContainer = false
      this.djpbContainer = true
      this.againRegister = true
    }
  },

  components: {
    IndividualsList,
    fileUpload,
    sDataGrid,
    sGeneralHistory
  },

  created() {
    this.handleGetCivilizedRoomList()
  },

  computed: {
    yearNum() {
      return dayjs(this.currerentYear).year()
    },
  },
  watch: {
    actInstId() {
      if (this.actInstId) {
        this.timer = new Date().getTime()
      }
    },
    'formValidate.approvelResult': {
      handler(newVal) {
        if (newVal == '04') {
          this.formValidate.approvelComment = "同意"
        } else {
          this.formValidate.approvelComment = "不同意"
        }
      }
    }
  }
}

</script>

<style scoped lang="less">
/deep/.fm-content-wrap-title {
  border-bottom: 1px solid #cee0f0;
  background: #eff6ff;
  line-height: 40px;
  padding-left: 10px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: bold;
  font-size: 16px;
  color: #00244A;
  /* margin-bottom: 16px; */
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  // border-bottom: 1px solid #CEE0F0;
  margin: 10px 0;
}


/deep/.ivu-btn-text {
  color: #2d8cf0;
}

.table-container {
  width: 100%;
  height: 100%;

  .header-prison-date {
    margin: 0 10px;
    padding: 10px 0;
    // height: 120px;
    border: solid 1px #dcdee2;
    border-radius: 4px;

    display: flex;
    justify-content: center;
    align-items: center;
  }

  .add-dj-container {
    margin: 10px 10px 0 10px;

  }

  .civilized-room-list {
    border: solid 1px #dcdee2;
    margin: 0 10px;
    border-radius: 4px;
    padding: 10px 0;
    margin-top: 10px;


    header {
      width: 100%;
      height: 30px;
      line-height: 30px;
      border-bottom: solid 1px #e8eaec;
      padding-left: 10px;
      display: flex;
      justify-content: space-between;
    }

    .no-data-png {
      display: flex;
      justify-content: center;

      img {
        width: 183px;
        height: 100px;
      }
    }

    .max-room-info {
      display: flex;
      flex-flow: wrap;

      .room-info-list {
        border: solid 1px #f5f5f5;
        margin-top: 10px;
        padding: 10px 10px;
        margin-left: 10px;
        display: flex;
        align-items: center;

        .sex-room-img {
          img {
            width: 100px;
            height: 111px;
          }
        }

        .right-room-info {
          margin-left: 6px;
        }
      }
    }
  }
}

.max-djpb-Container {
  position: relative;

  .prison-Cell-List {
    width: 100%;
    height: 620px;
    position: relative;
  }

  .table-wmjsdj-container {
    padding-bottom: 30px;

    h3 {
      padding: 10px 0;
    }
  }
}

.sp-form-container {
  margin-top: 10px;

  header {
    height: 50px;
    line-height: 50px;
    text-align: center;
    background: #f8f8f9;
    border: solid 1px #dcdee2;
    border-radius: 4px;
  }

  .border-sp-container {
    //  height: 50px;
    border: solid 1px #dcdee2;
    border-top: none;
    height: 150px;
    padding-bottom: 30px;
  }
}
</style>
