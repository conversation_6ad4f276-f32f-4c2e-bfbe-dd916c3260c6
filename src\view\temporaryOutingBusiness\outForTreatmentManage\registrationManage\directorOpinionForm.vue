<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <div class="fm-content-info bsp-base-content" style="top:30px !important;padding:unset !important;">
      <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="200" label-colon style="padding: 0 .625rem;">
        <div class="fm-content-box">
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />人员信息</p>
          <Row>
            <Col span="3" class="col-title"><span>姓名</span></Col>
            <Col span="5"><span>{{combineInfoData.xm}}</span></Col>
            <Col span="3" class="col-title"><span>性别</span></Col>
            <Col span="5"><span>{{combineInfoData.xbName}}</span></Col>
            <Col span="3" class="col-title"><span>国籍</span></Col>
            <Col span="5"><span>{{combineInfoData.gj}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>出生日期</span></Col>
            <Col span="5"><span>{{combineInfoData.csrq}}</span></Col>
            <Col span="3" class="col-title"><span>监室号</span></Col>
            <Col span="5"><span>{{combineInfoData.roomName}}</span></Col>
          </Row>
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />就医信息</p>
          <Row>
            <Col span="3" class="col-title"><span>预约时间</span></Col>
            <Col span="5"><span>{{formData.appointmentTime}}</span></Col>
            <Col span="3" class="col-title"><span>就诊医院</span></Col>
            <Col span="5"><span>{{formData.hospitalName}}</span></Col>
            <Col span="3" class="col-title"><span>出所就医原因</span></Col>
            <Col span="5"><span>{{formData.csjyyyName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>病情等级</span></Col>
            <Col span="5"><span>{{formData.symptomLevelName}}</span></Col>
            <Col span="3" class="col-title"><span>精神情况</span></Col>
            <Col span="5"><span>{{formData.jszk}}</span></Col>
            <Col span="3" class="col-title"><span>是否存在拒绝服药或抗拒治疗情况</span></Col>
            <Col span="5"><span>{{formData.sfczjjfyhkjzlqkName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>是否存在吞食异物情况</span></Col>
            <Col span="5"><span>{{formData.sfcztsywqkName}}</span></Col>
            <Col span="3" class="col-title"><span>是否存在不服从管理或自残行为</span></Col>
            <Col span="5"><span>{{formData.sfczbfcglhzcxwName}}</span></Col>
            <Col span="3" class="col-title"><span>是否存在其他危险行为或异常情况</span></Col>
            <Col span="5"><span>{{formData.sfczqtwxxwhycqkName}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>病情描述</span></Col>
            <Col span="13"><span>{{formData.symptomDesc}}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>备注</span></Col>
            <Col span="13"><span>{{formData.remark}}</span></Col>
          </Row>
        </div>
        <div class="bsp-base-subtit" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />医务主任意见</p>
          <div class="form">
            <approvalInfo :actInstId="this.rowData.act_inst_id"></approvalInfo>
          </div>
        </div>
      </Form>
    </div>
    <div class="bsp-base-fotter" >
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
<!--      <Button style="margin: 0 20px" :loading="loadingSave" @click="handleSubmit(false)">暂 存</Button>-->
      <Button style="margin: 0 20px"  type="primary" :loading="loading" @click="handleApproval()">审批</Button>
    </div>

    <s-general-audit
      v-if="this.rowData.act_inst_id && showAudit"
      :key="timer"
      ref="approvalAudit"
      @audit-close="audit_close"
      :showFileUpload="false"
      :beforeOpen="beforeOpen"
      :actInstId="this.rowData.act_inst_id"
      :showcc="false"
      :businessId="getBussinId"
      :module="module"
      :extraOrgId="extraOrgId"
      :extraRegId="extraRegId"
      extraCityId=""
      :selectUsers="selectUsers"
      :modalWidth="modalWidth"
      :msgUrl="msgUrl"
      :msgTit="msgTit"
      :auditComplete="approvalSuccess"
      approvalContent=""
    >
    </s-general-audit>
  </div>
</template>

<script>
  import approvalInfo from "./approvalInfo";
  import {mapActions} from "vuex";
  import { getUserCache } from '@/libs/util'
  import { sGeneralAudit } from 'gxx-general-audit'
  import {startApproval} from 'gs-start-approval'

  export default {
    components:{
      startApproval,approvalInfo,sGeneralAudit
    },
    props:{
      rowData:{
        type: [Array,Object],
        default: {}
      },
      entireProcess: {
        default: false,
      }
    },
    data(){
      return{
        formData: {
        },
        combineInfoData:{},
        loading:false,
        loadingSave:false,
        ruleValidate: {},
        msgUrl: '/#/detentionBusiness/outForTreatmentRegister',
        msgTit:'【审批】出所就医',
        timer: '',
        actInstId:"",
        getBussinId: '',
        showAudit:false,
        module: serverConfig.APP_MARK,
        selectUsers: '',
        extraOrgId: this.$store.state.common.orgCode,
        extraRegId: this.$store.state.common.regCode,
        modalWidth: '600',
        isTest:true,  //演示用
      }
    },
    methods:{
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      getCurrentTimeFormatted() {
        const now = new Date();

        const year = now.getFullYear();             // 获取年份
        const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，要加1，并补零
        const day = String(now.getDate()).padStart(2, '0');        // 日期补零

        const hours = String(now.getHours()).padStart(2, '0');     // 小时补零
        const minutes = String(now.getMinutes()).padStart(2, '0'); // 分钟补零
        const seconds = String(now.getSeconds()).padStart(2, '0'); // 秒数补零

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      handleClose(){
        this.$router.replace({
          query: {
            ...this.$route.query,
            eventCode: undefined,  // 设置为 undefined 即可删除该参数
            step:undefined,
          }
        });
        this.$emit('close',false)
      },
      handleNext(){
        this.$emit('nextStep',false)
      },

      // handleSubmit(tag){
      //   this.$refs['formData'].validate((valid) => {
      //     if (valid) {
      //       this.saveData(tag)
      //     } else {
      //       this.loading=false
      //       this.$Message.error('请填写完整!!');
      //     }
      //   })
      // },

      handleApproval(){
        this.openAuidt()
      },
      openAuidt(){
        this.timer = new Date().getTime()
        this.showAudit=true
        this.getBussinId = this.rowData.jgrybm
        console.log(this.rowData,'sssssssssss')
        setTimeout(() => {
          this.$refs['approvalAudit'].openAudit()
        }, 500)
      },
      audit_close() {
        this.$refs.approvalAudit.isOpen = false
        this.getBussinId =''
        this.actInstId = ''
        this.showAudit=false
        // 重新加载子组件
        this.timer = new Date().getTime()
      },
      beforeOpen() {
        return new Promise((resolve, reject) => {
          let jbsj = this.getCurrentTimeFormatted();
          let jbr = getUserCache.getUserName()
          this.msgUrl= '/#/detentionBusiness/outForTreatmentRegister?eventCode='+this.rowData.jgrybm+"&step=04"
          this.msgTit=`【出所就医审批】医务主任：${jbr}于${jbsj}提交了对${this.rowData.jgryxm}出所就医的意见，请尽快审批！`
          resolve(true)
        })
      },
      approvalSuccess(data) {
        console.log(data,"流程-----")
        this.saveData(data.data.approvedCompleteTask)
      },

      saveData(data){
        this.formData.dataSources?'':this.$set(this.formData,'dataSources',0)
        // let params=this.formData
        let params= {}
        params.jgrybm = this.rowData.jgrybm
        params.jgryxm = this.rowData.jgryxm
        params.status = "03"
        // params.ywzryjJbr = getUserCache.getUserName();
        // params.ywzryjJbsj = this.getCurrentTimeFormatted();
        // params.ywzrbz = data.approvalContent
        if(data.isApprove == "1"){
          params.spzt = "2"
        }else{
          params.spzt = "3"
        }
        this.$store.dispatch('authPostRequest',{
          url: this.$path.app_outHospitalJjsyRegister,
          params:params
        }).then(resp=>{
          this.loading=false
          this.loadingSave=false
          if (resp.code==0){
            this.$Message.success('提交成功!');
            if(data.isApprove == "1"){
              this.handleNext()
            }else{
              this.handleClose()
            }
          }else{
            this.$Notice.error({
              title:'错误提示',
              desc:resp.msg
            })
          }
        })
      },
      getDetail(jgrybm){
        this.$store.dispatch('authGetRequest',{
          url: this.$path.app_outHospitalGetByJgrybm,
          params: {
            jgrybm:jgrybm,
            current_step:this.rowData.current_step
          }
        }).then(resp => {
          if(resp.code == 0){
            if(resp.data){
              this.formData = resp.data
            }
          }else{
            this.$Modal.error({
              title: '温馨提示',
              content: resp.msg || '操作失败'
            })
          }
        })
      },
      getPrisonerSelectCompomenOne(rybh){
        this.$store.dispatch('authGetRequest',{
          url: this.$path.app_getPrisonerSelectCompomenOne,
          params: {
            jgrybm:rybh,
            ryzt:'ALL'
          }
        }).then(resp => {
          if(resp.code == 0){
            if(resp.data){
              this.combineInfoData = resp.data
            }
          }else{
            this.$Modal.error({
              title: '温馨提示',
              content: resp.msg || '操作失败'
            })
          }
        })
      },
    },
    mounted(){
      this.actInstId = this.rowData.act_inst_id
      this.getDetail(this.rowData.jgrybm)
      this.getPrisonerSelectCompomenOne(this.rowData.jgrybm)
    }
  }
</script>

<style scoped>
  @import "~@/assets/style/formInfo.css";
  .fm-content-info{
    padding: 26px 0 26px 0 !important;
  }
  .action-gather{
    display: flex;
  }
</style>
