<template>
    <div class="recipe-tem-component">
        <!-- 模板头部区域 -->
        <div class="template-header">
            <div class="header-title">
                <Icon type="md-restaurant" size="24" color="#2b5fda" />
                <span v-if="recipeEditInfo.id">编辑菜谱模板</span>
                <span v-else>新建菜谱模板</span>
                <div class="template-info" v-if="recipeEditInfo.id">
                    <Icon type="md-document" color="#19be6b" />
                    <span>{{ recipeTemform.templateName || '未命名模板' }}</span>
                </div>
                <div class="template-info new" v-else>
                    <Icon type="md-add-circle" color="#2b5fda" />
                    <span>请拖拽菜品安排菜谱，完成后点击保存</span>
                </div>
            </div>
        </div>

        <div class="selection">

            <div class="catering-list">
                <div class="dishes-panel">
                    <div class="panel-header">
                        <Icon type="md-nutrition" size="20" color="#2b5fda" />
                        <span>菜品库</span>
                    </div>

                    <!-- 搜索区域 -->
                    <div class="search-section">
                        <Input v-model="dashesName"
                               placeholder="搜索菜品名称"
                               size="large"
                               @on-enter="handleSearchDashes"
                               @on-change="handleSearchChange">
                            <Icon type="ios-search" slot="prefix" />
                            <Button slot="append"
                                    icon="ios-search"
                                    @click="handleSearchDashes"
                                    :loading="searchLoading">搜索</Button>
                        </Input>
                        <div class="search-tips" v-if="dashesName && dashesList.length === 0">
                            <Icon type="md-information-circle" color="#ff9900" />
                            <span>未找到相关菜品，请尝试其他关键词</span>
                        </div>
                    </div>

                    <!-- 分类标签页 -->
                    <Tabs :value="currentActiveTab" @on-click="handleTabChange" class="dishes-tabs">
                        <TabPane v-for="(itemFath) in caterClassifyList" :key="itemFath.id"
                            :label="itemFath.cateName" :name="itemFath.id">

                            <!-- 添加菜品区域 -->
                            <div class="add-dish-section">
                                <div class="add-dish-form">
                                    <Input ref="elInput"
                                           v-model="adddashesName"
                                           size="large"
                                           placeholder="请输入新菜品名称"
                                           @on-enter="handleAddDashes"
                                           @on-change="validateDishName">
                                        <Icon type="md-add" slot="prefix" />
                                    </Input>
                                    <Button type="primary"
                                            icon="ios-add"
                                            size="large"
                                            @click="handleAddDashes"
                                            :disabled="!adddashesName.trim()"
                                            :loading="addLoading">
                                        添加菜品
                                    </Button>
                                </div>
                                <div class="add-tips" v-if="addDishError">
                                    <Icon type="md-close-circle" color="#ed4014" />
                                    <span>{{ addDishError }}</span>
                                </div>
                            </div>

                            <!-- 菜品列表 -->
                            <div class="dishes-list-container">
                                <draggable id="left-draggable"
                                           :list="dashesList"
                                           :group="{ name: 'cook', pull: 'clone', put: false }"
                                           :move="checkMove"
                                           drag-class="dragging-item"
                                           ghost-class="ghost-item"
                                           :touch-start-threshold="5"
                                           animation="200"
                                           @start="onDragStart"
                                           @end="onDragEnd">
                                    <div v-for="(item) in dashesList"
                                         :key="item.id"
                                         class="dish-card"
                                         :class="{ 'dragging': isDragging }"
                                         @mouseenter="onDishHover(item)"
                                         @mouseleave="onDishLeave(item)">
                                        <div class="dish-category" :style="{ backgroundColor: getDishCategoryColor(item) }">
                                            {{ getCategoryName(item.cateId) }}
                                        </div>
                                        <div class="dish-name" :title="item.cookName">
                                            {{ item.cookName }}
                                        </div>
                                        <div class="dish-actions">
                                            <Tooltip content="编辑菜品" placement="top">
                                                <Icon type="md-create"
                                                      class="action-icon edit-icon"
                                                      @click="handleUpateDahesName(item)" />
                                            </Tooltip>
                                            <Tooltip content="删除菜品" placement="top">
                                                <Icon type="md-trash"
                                                      class="action-icon delete-icon"
                                                      @click="handleDeleteDahesName(item)" />
                                            </Tooltip>
                                        </div>
                                        <div class="drag-handle">
                                            <Icon type="md-menu" />
                                        </div>
                                    </div>
                                </draggable>

                                <div class="empty-state" v-if="dashesList.length === 0">
                                    <Icon type="md-restaurant" size="48" color="#c5c8ce" />
                                    <p>暂无菜品</p>
                                    <p>请添加菜品或切换分类查看</p>
                                </div>
                            </div>
                        </TabPane>
                    </Tabs>
                </div>
            </div>
            <div class="recipes-content-box" v-if="addOrEdit == 'edit'">
                <div class="recipe-panel">
                    <div class="panel-header">
                        <Icon type="md-calendar" size="20" color="#2b5fda" />
                        <span>周菜谱安排</span>
                        <div class="panel-actions">
                            <Tooltip content="清空所有菜谱" placement="top">
                                <Button size="small" @click="clearAllRecipes">
                                    <Icon type="md-trash" />
                                    清空
                                </Button>
                            </Tooltip>
                        </div>
                    </div>

                    <div class="recipe-table-container">
                        <table class="recipe-table">
                            <thead>
                                <tr class="table-header">
                                    <th class="meal-header">
                                        <div class="header-content">
                                            <Icon type="md-time" color="#2b5fda" />
                                            <span>餐次</span>
                                        </div>
                                    </th>
                                    <th v-for="(item, index) in weekList" :key="index" class="day-header">
                                        <div class="day-content">
                                            <div class="day-name">{{ item.name }}</div>
                                            <Button v-if="index > 0"
                                                    :disabled="copyDayUpdateFlag(index)"
                                                    size="small"
                                                    type="primary"
                                                    ghost
                                                    @click="copyUpdateDay(index)">
                                                <Icon type="md-copy" />
                                                复制昨日
                                            </Button>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(menuitem, menuIndex) in cookMeals" :key="menuIndex" class="meal-row">
                                    <td class="meal-cell">
                                        <div class="meal-info">
                                            <div class="meal-name">{{ menuitem.name }}</div>
                                            <Tooltip content="删除此餐次" placement="right">
                                                <Icon type="md-close-circle"
                                                      class="delete-meal-icon"
                                                      @click="delUpdateMeals(menuIndex)" />
                                            </Tooltip>
                                        </div>
                                    </td>
                                    <td v-for="(item, index) in cookSubSNew.filter(item => item.mealName === menuitem.name)[0] && cookSubSNew.filter(item => item.mealName === menuitem.name)[0].cookWeekSubs"
                                        :key="index"
                                        class="dish-cell"
                                        :class="{
                                            'drop-zone-active': isDragOver && !isDragOverInvalid,
                                            'drop-zone-invalid': isDragOverInvalid,
                                            'has-dishes': item.cooks && item.cooks.length > 0
                                        }">
                                        <draggable v-model="item.cooks"
                                                   :group="{ name: 'cook', put: true }"
                                                   :move="checkMove"
                                                   animation="200"
                                                   ghost-class="ghost-item"
                                                   @add="onDishAdded"
                                                   class="drop-zone-full"
                                                   @dragover.native.prevent="onDragOver"
                                                   @dragleave.native="onDragLeave"
                                                   @drop.native="onDrop"
                                                   v-if="item && item.cooks !== undefined">
                                            <div class="dish-item"
                                                 v-for="(info, idx) in (item.cooks || [])"
                                                 :key="idx"
                                                 @mouseenter="onDishItemHover"
                                                 @mouseleave="onDishItemLeave">
                                                <div class="dish-category-bar" :style="{ backgroundColor: getDishCategoryColor(info) }"></div>
                                                <div class="dish-item-content">
                                                    <div class="dish-item-name" :title="info.name">{{ info.name }}</div>
                                                    <Tooltip content="移除菜品" placement="top">
                                                        <Icon type="md-close"
                                                              class="remove-dish-icon"
                                                              @click="delSubUpdate(menuIndex, index, idx)" />
                                                    </Tooltip>
                                                </div>
                                            </div>

                                            <div class="drop-placeholder" v-if="!item.cooks || item.cooks.length === 0">
                                                <Icon type="md-add" color="#c5c8ce" />
                                                <span>拖拽菜品到此处</span>
                                            </div>

                                            <div class="dish-count" v-if="item.cooks && item.cooks.length > 0">
                                                {{ item.cooks.length }} 道菜
                                            </div>
                                        </draggable>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="recipes-content-box" v-if="addOrEdit == 'add'">
                <div class="recipe-panel">
                    <div class="panel-header">
                        <Icon type="md-calendar" size="20" color="#2b5fda" />
                        <span>周菜谱安排</span>
                        <div class="panel-actions">
                            <Tooltip content="清空所有菜谱" placement="top">
                                <Button size="small" @click="clearAllRecipes">
                                    <Icon type="md-trash" />
                                    清空
                                </Button>
                            </Tooltip>
                        </div>
                    </div>

                    <div class="recipe-table-container">
                        <table class="recipe-table">
                            <thead>
                                <tr class="table-header">
                                    <th class="meal-header">
                                        <div class="header-content">
                                            <Icon type="md-time" color="#2b5fda" />
                                            <span>餐次</span>
                                        </div>
                                    </th>
                                    <th v-for="(item, index) in weekList" :key="index" class="day-header">
                                        <div class="day-content">
                                            <div class="day-name">{{ item.name }}</div>
                                            <Button v-if="index > 0"
                                                    :disabled="copyDayFlag(index)"
                                                    size="small"
                                                    type="primary"
                                                    ghost
                                                    @click="copyDay(index)">
                                                <Icon type="md-copy" />
                                                复制昨日
                                            </Button>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(menuitem, menuIndex) in cookMeals" :key="menuIndex" class="meal-row">
                                    <td class="meal-cell">
                                        <div class="meal-info">
                                            <div class="meal-name">{{ menuitem.name }}</div>
                                            <Tooltip content="删除此餐次" placement="right">
                                                <Icon type="md-close-circle"
                                                      class="delete-meal-icon"
                                                      @click="delMeals(menuIndex)" />
                                            </Tooltip>
                                        </div>
                                    </td>
                                    <td v-for="(item, index) in cookSubS.filter(item => item.mealName === menuitem.name)[0] && cookSubS.filter(item => item.mealName === menuitem.name)[0].cookWeekSubs"
                                        :key="index"
                                        class="dish-cell"
                                        :class="{
                                            'drop-zone-active': isDragOver && !isDragOverInvalid,
                                            'drop-zone-invalid': isDragOverInvalid,
                                            'has-dishes': item.cooks && item.cooks.length > 0
                                        }">
                                        <draggable v-model="item.cooks"
                                                   :group="{ name: 'cook', put: true }"
                                                   :move="checkMove"
                                                   animation="200"
                                                   ghost-class="ghost-item"
                                                   @add="onDishAdded"
                                                   class="drop-zone-full"
                                                   @dragover.native.prevent="onDragOver"
                                                   @dragleave.native="onDragLeave"
                                                   @drop.native="onDrop"
                                                   v-if="item && item.cooks !== undefined">
                                            <div class="dish-item"
                                                 v-for="(info, idx) in (item.cooks || [])"
                                                 :key="idx"
                                                 @mouseenter="onDishItemHover"
                                                 @mouseleave="onDishItemLeave">
                                                <div class="dish-category-bar" :style="{ backgroundColor: getDishCategoryColor(info) }"></div>
                                                <div class="dish-item-content">
                                                    <div class="dish-item-name" :title="info.name">{{ info.name }}</div>
                                                    <Tooltip content="移除菜品" placement="top">
                                                        <Icon type="md-close"
                                                              class="remove-dish-icon"
                                                              @click="delSub(menuIndex, index, idx)" />
                                                    </Tooltip>
                                                </div>
                                            </div>

                                            <div class="drop-placeholder" v-if="!item.cooks || item.cooks.length === 0">
                                                <Icon type="md-add" color="#c5c8ce" />
                                                <span>拖拽菜品到此处</span>
                                            </div>

                                            <div class="dish-count" v-if="item.cooks && item.cooks.length > 0">
                                                {{ item.cooks.length }} 道菜
                                            </div>
                                        </draggable>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- 修改菜品弹框 -->
        <handleModal :isShowModal="isChangeDashesModal" :title="'修改菜品'" :width="400" @handleSubmit="handleDashesSubmit"
            @handleCancel="handleDashesCancel">
            <template v-slot:form>
                <Form ref="formDashes" :model="formDashes" :label-width="120">
                    <FormItem label="菜品名称" prop="cookName"
                        :rules="{ required: true, message: '请输入菜品名称', trigger: 'blur' }">
                        <Input v-model="formDashes.cookName" placeholder="请输入菜品名称"></Input>
                    </FormItem>
                </Form>
            </template>
        </handleModal>

        <!-- 保存模板弹框 -->
        <handleModal :isShowModal="isSaveTemplateModal" :title="'保存菜谱模板'" :width="500" @handleSubmit="handleSaveTemplateSubmit"
            @handleCancel="handleSaveTemplateCancel">
            <template v-slot:form>
                <Form ref="formTemplate" :model="formTemplate" :label-width="120">
                    <FormItem label="模板名称" prop="templateName"
                        :rules="{ required: true, message: '请输入模板名称', trigger: 'blur' }">
                        <Input v-model="formTemplate.templateName"
                               placeholder="请输入菜谱模板名称"
                               size="large"
                               maxlength="50"
                               show-word-limit>
                            <Icon type="md-create" slot="prefix" />
                        </Input>
                    </FormItem>
                    <FormItem label="模板描述" prop="description">
                        <Input v-model="formTemplate.description"
                               type="textarea"
                               placeholder="请输入模板描述（可选）"
                               :rows="3"
                               maxlength="200"
                               show-word-limit></Input>
                    </FormItem>
                </Form>
                <div class="template-preview">
                    <div class="preview-title">
                        <Icon type="md-eye" color="#2b5fda" />
                        <span>菜谱预览</span>
                    </div>
                    <div class="preview-content">
                        <div class="preview-stats">
                            <div class="stat-item">
                                <span class="stat-label">餐次数量：</span>
                                <span class="stat-value">{{ cookMeals.length }} 餐</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">菜品总数：</span>
                                <span class="stat-value">{{ getTotalDishCount() }} 道</span>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </handleModal>
        <div class="bsp-base-fotter">
            <Button type="primary" @click="handleSubmitTem">保存</Button>
            <Button @click="goBack">返回</Button>
        </div>
    </div>
</template>

<script>
import dishesList from './dishesList.vue'
import handleModal from './handleModal.vue'
import { mapActions } from 'vuex'
import draggable from "vuedraggable";
export default {
    name: "recipeTem",
    props: {
        caterClassifyList: [Array],
        dashesList: [Array],
        recipeEditInfo: [Object],
        addOrEdit: [String]
    },
    data() {

        return {
            currentTabIndex: 0,
            dashesName: "",
            isChangeDashesModal: false,
            formDashes: {
                cookName: ""
            },
            dashesItem: {},
            cateId: "",
            adddashesName: "",
            weekList: [],
            numTonList: [],
            recipeTemform: {
                templateName: "",
            },
            cookSubSNew: [],
            updateArr: [],
            // 新增的交互状态
            searchLoading: false,
            addLoading: false,
            addDishError: "",
            isDragging: false,
            isDragOver: false,
            isDragOverInvalid: false, // 拖拽到无效区域（重复菜品）
            autoSaveTimer: null,
            // 保存模板弹框相关
            isSaveTemplateModal: false,
            formTemplate: {
                templateName: "",
                description: ""
            },
            // 分类颜色管理
            categoryColorMap: {}, // 存储每个分类的颜色
            colorPool: [
                '#3097ff', // 蓝色
                '#ed8433', // 橙色
                '#33bb83', // 绿色
                '#8e6bee', // 紫色
                '#f56c6c', // 红色
                '#409eff', // 天蓝色
                '#67c23a', // 草绿色
                '#e6a23c', // 金黄色
                '#f78989', // 粉红色
                '#9c88ff', // 淡紫色
                '#36cfc9', // 青色
                '#ff9c6e', // 橘色
                '#73d13d', // 亮绿色
                '#ff85c0', // 玫红色
                '#597ef7', // 靛蓝色
            ],
            cookSubS: [
                {
                    cookWeekSubs: [
                        {
                            weekId: "1",
                            cooks: [

                            ]
                        },
                        {
                            weekId: "2",
                            cooks: []
                        },
                        {
                            weekId: "3",
                            cooks: []
                        },
                        {
                            weekId: "4",
                            cooks: []
                        },
                        {
                            weekId: "5",
                            cooks: []
                        },
                        {
                            weekId: "6",
                            cooks: []
                        },
                        {
                            weekId: "7",
                            cooks: []
                        },
                    ],
                    mealName: "早餐",
                    mealNo: 1
                },
                {
                    cookWeekSubs: [
                        {
                            weekId: "1",
                            cooks: []
                        },
                        {
                            weekId: "2",
                            cooks: []
                        },
                        {
                            weekId: "3",
                            cooks: []
                        },
                        {
                            weekId: "4",
                            cooks: []
                        },
                        {
                            weekId: "5",
                            cooks: []
                        },
                        {
                            weekId: "6",
                            cooks: []
                        },
                        {
                            weekId: "7",
                            cooks: []
                        },
                    ],
                    mealName: "午餐",
                    mealNo: 2
                },
                {
                    cookWeekSubs: [
                        {
                            weekId: "1",
                            cooks: []
                        },
                        {
                            weekId: "2",
                            cooks: []
                        },
                        {
                            weekId: "3",
                            cooks: []
                        },
                        {
                            weekId: "4",
                            cooks: []
                        },
                        {
                            weekId: "5",
                            cooks: []
                        },
                        {
                            weekId: "6",
                            cooks: []
                        },
                        {
                            weekId: "7",
                            cooks: []
                        },
                    ],
                    mealName: "晚餐",
                    mealNo: 3
                }
            ],
            cookMeals: [
                {
                    name: "早餐",
                    weekId: "1,2,3,4,5,6,7",
                    sortNo: 1
                },
                {
                    name: "午餐",
                    weekId: "1,2,3,4,5,6,7",
                    sortNo: 2
                },
                {
                    name: "晚餐",
                    weekId: "1,2,3,4,5,6,7",
                    sortNo: 3
                }
            ],
        }

    },

    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        goBack() {
            this.$parent.tableTem = true
        },
        // 获取分类颜色 - 与菜品管理模块保持一致，优先使用数据库中的颜色
        getCategoryColor(cateId) {
            // 首先查找分类数据中是否有颜色信息
            const category = this.caterClassifyList.find(item => item.id === cateId)
            if (category && category.cateColor) {
                // 如果数据库中有颜色，直接使用并更新本地缓存
                this.$set(this.categoryColorMap, cateId, category.cateColor)
                return category.cateColor
            }

            // 如果本地缓存中有颜色，使用缓存的颜色
            if (this.categoryColorMap[cateId]) {
                return this.categoryColorMap[cateId]
            }

            // 如果都没有，分配一个默认颜色（但不保存到数据库）
            const usedColors = Object.values(this.categoryColorMap)
            const availableColors = this.colorPool.filter(color => !usedColors.includes(color))

            let assignedColor
            if (availableColors.length > 0) {
                assignedColor = availableColors[Math.floor(Math.random() * availableColors.length)]
            } else {
                assignedColor = this.colorPool[Math.floor(Math.random() * this.colorPool.length)]
            }

            // 只保存到本地缓存，不保存到数据库
            this.$set(this.categoryColorMap, cateId, assignedColor)
            this.saveCategoryColors()
            return assignedColor
        },

        // 初始化分类颜色 - 优先使用数据库中的颜色
        initializeCategoryColors() {
            // 从localStorage加载已保存的颜色映射
            const savedColors = localStorage.getItem('categoryColorMap')
            if (savedColors) {
                try {
                    this.categoryColorMap = JSON.parse(savedColors)
                } catch (e) {
                    console.warn('加载颜色映射失败:', e)
                }
            }

            if (this.caterClassifyList && this.caterClassifyList.length > 0) {
                this.caterClassifyList.forEach((item, index) => {
                    // 优先使用数据库中的颜色
                    if (item.cateColor) {
                        this.$set(this.categoryColorMap, item.id, item.cateColor)
                    } else if (!this.categoryColorMap[item.id]) {
                        // 如果数据库中没有颜色且本地缓存也没有，分配一个默认颜色
                        const colorIndex = index % this.colorPool.length
                        this.$set(this.categoryColorMap, item.id, this.colorPool[colorIndex])
                    }
                })

                // 保存颜色映射到localStorage
                this.saveCategoryColors()
            }
        },

        // 保存颜色映射到localStorage
        saveCategoryColors() {
            try {
                localStorage.setItem('categoryColorMap', JSON.stringify(this.categoryColorMap))
            } catch (e) {
                console.warn('保存颜色映射失败:', e)
            }
        },

        // 获取分类样式对象（用于动态设置背景色）
        getCategoryStyle(categoryId) {
            const color = this.getCategoryColor(categoryId)
            return {
                backgroundColor: color
            }
        },

        // 获取菜品的分类颜色 - 优先使用API返回的cateInfo.cateColor
        getDishCategoryColor(dishInfo) {
            // 优先使用API返回的cateInfo中的颜色
            if (dishInfo.cateInfo && dishInfo.cateInfo.cateColor) {
                return dishInfo.cateInfo.cateColor
            }

            // 如果菜品信息中直接包含cateColor（兼容旧数据结构），使用它
            if (dishInfo.cateColor) {
                return dishInfo.cateColor
            }

            // 如果有cateId，通过分类ID获取颜色
            if (dishInfo.cateId) {
                return this.getCategoryColor(dishInfo.cateId)
            }

            // 如果都没有，根据cookType使用默认颜色（兼容旧数据）
            const defaultColors = {
                '1': '#3097ff', // 主食 - 蓝色
                '2': '#ed8433', // 荤菜 - 橙色
                '3': '#33bb83', // 素菜 - 绿色
                '4': '#8e6bee'  // 辅食 - 紫色
            }
            return defaultColors[dishInfo.cookType] || '#3097ff'
        },

        // 获取分类名称
        getCategoryName(cateId) {
            const category = this.caterClassifyList.find(item => item.id === cateId)
            return category ? category.cateName : '未知分类'
        },

        // 确保数据结构完整性
        ensureDataIntegrity() {
            // 确保 cookSubS 中的每个项都有 cooks 数组
            this.cookSubS.forEach(meal => {
                if (meal.cookWeekSubs) {
                    meal.cookWeekSubs.forEach(day => {
                        if (!day.cooks) {
                            this.$set(day, 'cooks', [])
                        }
                    })
                }
            })

            // 确保 cookSubSNew 中的每个项都有 cooks 数组
            this.cookSubSNew.forEach(meal => {
                if (meal.cookWeekSubs) {
                    meal.cookWeekSubs.forEach(day => {
                        if (!day.cooks) {
                            this.$set(day, 'cooks', [])
                        }
                    })
                }
            })
        },

        // 已移除getItemClass方法，请使用getCategoryStyle或getDishCategoryColor方法
        // 保存菜谱模板 - 显示弹框
        handleSubmitTem() {
            // 检查是否有菜品安排
            const hasDishes = this.checkHasDishes();
            if (!hasDishes) {
                this.$Message.warning('请先安排菜品再保存模板');
                return;
            }

            // 添加调试信息
            console.log('保存模板 - recipeEditInfo:', this.recipeEditInfo);
            console.log('保存模板 - addOrEdit:', this.addOrEdit);
            console.log('保存模板 - recipeEditInfo.id:', this.recipeEditInfo ? this.recipeEditInfo.id : 'undefined');

            if (this.recipeEditInfo && this.recipeEditInfo.id) {
                // 编辑模式，直接保存
                console.log('进入编辑模式，直接保存');
                this.saveTemplate();
            } else {
                // 新建模式，弹框输入模板名称
                console.log('进入新建模式，显示弹框');
                this.formTemplate.templateName = "";
                this.formTemplate.description = "";
                this.isSaveTemplateModal = true;
                console.log('显示保存模板弹框:', this.isSaveTemplateModal);
            }
        },

        // 检查是否有菜品安排
        checkHasDishes() {
            const cookSubs = this.addOrEdit === 'edit' ? this.cookSubSNew : this.cookSubS;
            for (let meal of cookSubs) {
                for (let day of meal.cookWeekSubs) {
                    if (day.cooks && day.cooks.length > 0) {
                        return true;
                    }
                }
            }
            return false;
        },

        // 获取菜品总数
        getTotalDishCount() {
            let total = 0;
            const cookSubs = this.addOrEdit === 'edit' ? this.cookSubSNew : this.cookSubS;
            for (let meal of cookSubs) {
                for (let day of meal.cookWeekSubs) {
                    if (day.cooks) {
                        total += day.cooks.length;
                    }
                }
            }
            return total;
        },

        // 保存模板弹框确认
        handleSaveTemplateSubmit() {
            this.$refs['formTemplate'].validate((valid) => {
                if (valid) {
                    this.recipeTemform.templateName = this.formTemplate.templateName;
                    this.saveTemplate();
                    this.isSaveTemplateModal = false;
                } else {
                    this.$Message.error('请填写完整信息');
                }
            });
        },

        // 保存模板弹框取消
        handleSaveTemplateCancel() {
            this.isSaveTemplateModal = false;
            this.$refs['formTemplate'].resetFields();
        },

        // 实际保存模板
        saveTemplate() {
            let url = ""
            let msg = ""
            let id = ""
            let cookSubs = []

            if (this.recipeEditInfo.id) {
                url = this.$path.catering_recipe_tem_update
                msg = "菜谱模板更新成功"
                cookSubs = this.cookSubSNew
                id = this.recipeEditInfo.id
            } else {
                url = this.$path.catering_recipe_tem_create
                msg = "菜谱模板创建成功"
                cookSubs = this.cookSubS
                id = ""
            }

            this.authPostRequest({
                url: url,
                params: {
                    cookMeals: this.cookMeals,
                    cookSubs: cookSubs,
                    templateName: this.recipeTemform.templateName,
                    description: this.formTemplate.description || "",
                    id: id
                }
            }).then(res => {
                if (res.success) {
                    this.$Message.success(msg);
                    this.$parent.tableTem = true;
                }
            }).catch(err => {
                console.error('保存模板失败:', err);
                this.$Message.error('保存失败，请重试');
            });
        },
        // 搜索菜品
        handleSearchDashes() {
            this.searchLoading = true;
            // 无论是否输入内容都进行搜索，空字符串会返回所有菜品
            this.$parent.handleDefaultDashesList(this.dashesName.trim());

            setTimeout(() => {
                this.searchLoading = false;
            }, 500);
        },

        // 搜索输入变化
        handleSearchChange(value) {
            // 实时搜索，包括空值情况
            this.$parent.handleDefaultDashesList(value.trim());
        },



        // 验证菜品名称
        validateDishName(value) {
            this.addDishError = "";
            if (value && value.length > 20) {
                this.addDishError = "菜品名称不能超过20个字符";
            }
        },

        // 清空所有菜谱
        clearAllRecipes() {
            this.$Modal.confirm({
                title: '确认清空',
                content: '确定要清空所有菜谱安排吗？此操作不可恢复。',
                onOk: () => {
                    if (this.addOrEdit === 'edit') {
                        this.cookSubSNew.forEach(meal => {
                            meal.cookWeekSubs.forEach(day => {
                                day.cooks = [];
                            });
                        });
                    } else {
                        this.cookSubS.forEach(meal => {
                            meal.cookWeekSubs.forEach(day => {
                                day.cooks = [];
                            });
                        });
                    }
                    this.$Message.success('已清空所有菜谱');
                }
            });
        },

        // 拖拽开始
        onDragStart() {
            this.isDragging = true;
            this.isDragOverInvalid = false;
        },

        // 拖拽结束
        onDragEnd() {
            this.isDragging = false;
            this.isDragOverInvalid = false;
        },

        // 拖拽悬停
        onDragOver(evt) {
            evt.preventDefault();
            evt.stopPropagation();
            if (!this.isDragOverInvalid) {
                this.isDragOver = true;
            }
        },

        // 拖拽离开
        onDragLeave(evt) {
            evt.preventDefault();
            evt.stopPropagation();
            // 延迟设置，避免在子元素间移动时误触发
            setTimeout(() => {
                this.isDragOver = false;
                this.isDragOverInvalid = false;
            }, 50);
        },

        // 拖拽放置
        onDrop(evt) {
            evt.preventDefault();
            evt.stopPropagation();
            this.isDragOver = false;
            this.isDragOverInvalid = false;
        },

        // 菜品添加成功
        onDishAdded(evt) {
            try {
                const newIndex = evt.newIndex;

                // 安全地获取目标列表
                let targetList = null;
                if (evt.to && evt.to.__vue__ && evt.to.__vue__.$parent && evt.to.__vue__.$parent.item) {
                    targetList = evt.to.__vue__.$parent.item.cooks;
                }

                if (!targetList || !Array.isArray(targetList)) {
                    console.warn('无法获取目标列表，拖拽操作可能失败');
                    return;
                }

                // 获取拖拽的菜品数据
                let draggedData = null;
                if (evt.clone && evt.clone.__vue__ && evt.clone.__vue__.$parent) {
                    const sourceList = evt.clone.__vue__.$parent.dashesList;
                    if (sourceList && evt.oldIndex >= 0 && evt.oldIndex < sourceList.length) {
                        draggedData = sourceList[evt.oldIndex];
                    }
                }

                // 确保菜品数据格式正确
                if (draggedData && newIndex >= 0 && newIndex < targetList.length) {
                    const newItem = targetList[newIndex];
                    if (newItem) {
                        // 合并菜品数据，确保包含所有必要字段
                        targetList[newIndex] = {
                            ...newItem,
                            id: draggedData.id,
                            cookName: draggedData.cookName,
                            name: draggedData.cookName,
                            cookType: draggedData.cookType || '1', // 默认为主食
                            cateId: draggedData.cateId,
                            cateColor: draggedData.cateColor, // 兼容旧数据结构
                            cateInfo: draggedData.cateInfo // 传递新的cateInfo对象
                        };
                    }
                }

                this.$Message.success('菜品添加成功');
            } catch (error) {
                console.error('菜品添加过程中发生错误:', error);
                this.$Message.error('菜品添加失败，请重试');
            }
        },

        // 菜品悬停
        onDishHover() {
            // 可以添加悬停效果
        },

        // 菜品离开
        onDishLeave() {
            // 移除悬停效果
        },

        // 菜品项悬停
        onDishItemHover() {
            // 菜品项悬停效果
        },

        // 菜品项离开
        onDishItemLeave() {
            // 移除菜品项悬停效果
        },
        handleTabChange(id) {
            this.cateId = id
            this.$emit('handleTabChange', id)
        },
        // 更新菜品
        handleUpateDahesName(item) {
            this.isChangeDashesModal = true
            this.dashesItem = item
            this.authGetRequest({ url: this.$path.catering_get_dashes, params: { id: item.id } }).then(res => {
                if (res.success) {
                    this.formDashes.cookName = res.data.cookName
                }
            })
        },
        // 删除菜品
        handleDeleteDahesName(item) {
            this.$Modal.confirm({
                title: '温馨提示',
                content: '请确认是否删除？',
                onOk: () => {
                    this.authGetRequest({ url: this.$path.catering_delete_dashes, params: { ids: item.id } }).then(res => {
                        if (res.success) {
                            this.$Message.success('菜品删除成功')
                            this.$parent.handleDefaultDashesList()
                        }
                    })
                }
            })
        },
        // 创建菜品
        handleAddDashes() {
            if (!this.adddashesName.trim()) {
                this.addDishError = "请输入菜品名称";
                return;
            }

            if (this.adddashesName.length > 20) {
                this.addDishError = "菜品名称不能超过20个字符";
                return;
            }

            // 检查是否重复
            const isDuplicate = this.dashesList.some(dish =>
                dish.cookName.trim() === this.adddashesName.trim()
            );

            if (isDuplicate) {
                this.addDishError = "菜品名称已存在";
                return;
            }

            this.addLoading = true;
            this.addDishError = "";

            this.authPostRequest({
                url: this.$path.catering_create_dashes,
                params: {
                    cateId: this.cateId ? this.cateId : this.caterClassifyList[0].id,
                    cookName: this.adddashesName.trim(),
                    "sort": 0
                }
            }).then(res => {
                if (res.success) {
                    this.$Message.success("菜品创建成功");
                    this.adddashesName = "";
                    this.addDishError = "";
                    this.$parent.handleDefaultDashesList();
                } else {
                    this.addDishError = res.msg || "创建失败，请重试";
                }
            }).catch(err => {
                this.addDishError = "网络错误，请重试";
                console.error('添加菜品失败:', err);
            }).finally(() => {
                this.addLoading = false;
            });
        },
        handleDashesSubmit() {
            this.$refs['formDashes'].validate((valid) => {
                if (valid) {
                    this.authPostRequest({ url: this.$path.catering_update_dashes, params: { cateId: this.cateId ? this.cateId : this.caterClassifyList[0].id, cookName: this.formDashes.cookName, id: this.dashesItem.id, sort: 0 } }).then(res => {
                        if (res.success) {
                            this.$Message.success('菜品名称更新成功')
                            this.isChangeDashesModal = false
                            this.$parent.handleDefaultDashesList()
                        }
                    })
                }
            })
        },
        handleDashesCancel() {
            this.isChangeDashesModal = false
            this.$refs['formDashes'].resetFields();
        },
        handleGetZdCPZS() {
            this.authGetRequest({ url: "/bsp-com/static/dic/pam/ZD_PCGL_CPZS.js" }).then(res => {
                let week = eval('(' + res + ')')
                this.weekList = week()
            })
        },
        handleGetZdDSLX() {
            this.authGetRequest({ url: "/bsp-com/static/dic/pam/ZD_PCGL_DSLX.js" }).then(res => {
                let numTon = eval('(' + res + ')')
                this.numTonList = numTon()
            })
        },
        checkMove(evt) {
            // 防止拖拽回左侧菜品列表
            if (evt.to.id === "left-draggable") {
                return false;
            }

            // 检查是否重复菜品 - 在拖拽过程中进行检查
            if (evt.draggedContext && evt.relatedContext) {
                const draggedItem = evt.draggedContext.element;
                const targetList = evt.relatedContext.list;

                if (draggedItem && targetList) {
                    // 检查目标列表中是否已存在相同菜品
                    const isDuplicate = targetList.some(item =>
                        (item.cookName && draggedItem.cookName && item.cookName === draggedItem.cookName) ||
                        (item.name && draggedItem.cookName && item.name === draggedItem.cookName) ||
                        (item.id && draggedItem.id && item.id === draggedItem.id)
                    );

                    if (isDuplicate) {
                        // 设置无效拖拽状态，用于显示红色边框
                        this.isDragOverInvalid = true;
                        return false;
                    } else {
                        // 重置无效状态
                        this.isDragOverInvalid = false;
                    }
                }
            }

            return true;
        },
        copyUpdateDay(index) {
            for (let i = 0; i < this.cookSubSNew.length; i++) {
                const item = this.cookSubSNew[i];
                if (item.cookWeekSubs[index]) {
                    item.cookWeekSubs[index].cooks = Object.assign([], item.cookWeekSubs[index - 1].cooks);
                }
            }
        },
        copyDay(index) {
            for (let i = 0; i < this.cookSubS.length; i++) {
                const item = this.cookSubS[i];
                if (item.cookWeekSubs[index]) {
                    item.cookWeekSubs[index].cooks = Object.assign([], item.cookWeekSubs[index - 1].cooks);
                }
            }
        },
        copyDayFlag(day) {
            return this.hasSubListDTOSForWeekId(day - 1);
        },

        copyDayUpdateFlag(day) {
            return this.hasSubListDTOSForWeekIdUpdate(day - 1);
        },

        hasSubListDTOSForWeekIdUpdate(index) {
            for (let i = 0; i < this.cookSubSNew.length; i++) {
                const item = this.cookSubSNew[i];
                if (item.cookWeekSubs[index] && item.cookWeekSubs[index].cooks.length > 0) {
                    return false;
                }
            }
            return true;
        },
        hasSubListDTOSForWeekId(index) {
            for (let i = 0; i < this.cookSubS.length; i++) {
                const item = this.cookSubS[i];
                if (item.cookWeekSubs[index] && item.cookWeekSubs[index].cooks.length > 0) {
                    return false;
                }
            }
            return true;
        },
        delSubUpdate(menuIndex, index, idx) {
            this.cookSubSNew[menuIndex].cookWeekSubs[index].cooks.splice(idx, 1);
            this.$Message.success('菜品已移除');
        },
        delSub(menuIndex, index, idx) {
            this.cookSubS[menuIndex].cookWeekSubs[index].cooks.splice(idx, 1);
            this.$Message.success('菜品已移除');
        },
        // 删除餐次 - 编辑模式
        delUpdateMeals(menuIndex) {
            this.$Modal.confirm({
                title: '确认删除',
                content: '确定要删除此餐次吗？删除后该餐次的所有菜品安排将被清空。',
                onOk: () => {
                    this.cookMeals.splice(menuIndex, 1);
                    this.cookSubSNew.splice(menuIndex, 1);
                    this.$Message.success('餐次已删除');
                }
            });
        },
        // 删除餐次 - 新增模式
        delMeals(menuIndex) {
            this.$Modal.confirm({
                title: '确认删除',
                content: '确定要删除此餐次吗？删除后该餐次的所有菜品安排将被清空。',
                onOk: () => {
                    this.cookMeals.splice(menuIndex, 1);
                    this.cookSubS.splice(menuIndex, 1);
                    this.$Message.success('餐次已删除');
                }
            });
        },
        // 菜谱模板详情
        async handleGetRecipeTemDetail() {
            this.authGetRequest({ url: this.$path.catering_recipe_tem_get, params: { id: this.recipeEditInfo.id } }).then(res => {
                if (res.success) {
                    this.recipeTemform.templateName = res.data.templateName;
                    this.cookMeals = res.data.cookMeals
                    this.cookSubSNew = JSON.parse(JSON.stringify(res.data.cookSubs))

                    // 为编辑模式下的菜品设置正确的cookType
                    this.setCookTypeForEditMode()
                }
            })
        },

        // 为编辑模式下的菜品设置正确的cookType
        setCookTypeForEditMode() {
            if (this.cookSubSNew && this.caterClassifyList) {
                this.cookSubSNew.forEach(meal => {
                    if (meal.cookWeekSubs) {
                        meal.cookWeekSubs.forEach(day => {
                            if (day.cooks) {
                                day.cooks.forEach(cook => {
                                    // 如果已经有cookType，跳过
                                    if (cook.cookType) {
                                        return;
                                    }

                                    // 根据菜品的cateId设置cookType - 使用动态映射
                                    if (cook.cateId) {
                                        const category = this.caterClassifyList.find(cat => cat.id === cook.cateId);
                                        if (category) {
                                            // 使用动态cookType映射，而不是硬编码分类名称
                                            cook.cookType = this.getCookTypeByCategory(category);
                                        } else {
                                            // 找不到对应分类，默认为主食
                                            cook.cookType = '1';
                                        }
                                    } else {
                                        // 如果没有cateId，默认为主食
                                        cook.cookType = '1';
                                    }
                                });
                            }
                        });
                    }
                });
            }
        },

        // 根据分类动态获取cookType - 已优化，减少硬编码依赖
        getCookTypeByCategory(category) {
            // 优先使用分类对象中的cookType字段
            if (category.cookType) {
                return category.cookType;
            }

            // 如果分类有ID，可以根据ID进行映射（这里可以扩展为从配置中读取）
            if (category.id) {
                // 可以根据实际业务需求，从配置文件或API获取映射关系
                // 这里暂时保留基本的兼容性映射
                const commonCookTypeMap = {
                    '主食': '1',
                    '荤菜': '2',
                    '素菜': '3',
                    '辅食': '4'
                };

                // 根据分类名称映射，如果找不到则默认为主食
                return commonCookTypeMap[category.cateName] || '1';
            }

            // 默认返回主食类型
            return '1';
        }
    },

    components: {
        dishesList,
        handleModal,
        draggable
    },

    async created() {
        this.handleGetZdCPZS()
        this.handleGetZdDSLX()
        // 确保数据结构完整性
        this.ensureDataIntegrity()
        // 初始化分类颜色
        this.initializeCategoryColors()
        this.$nextTick(async () => {
            if (this.recipeEditInfo.id) {
                await this.handleGetRecipeTemDetail()
            }
        })
    },

    computed: {
        currentComponent() {
            return this.caterClassifyList[this.currentTabIndex].component
        },
        // 计算当前激活的标签页
        currentActiveTab() {
            if (this.cateId) {
                return this.cateId
            }
            // 如果没有选择分类，默认选择第一个
            if (this.caterClassifyList && this.caterClassifyList.length > 0) {
                return this.caterClassifyList[0].id
            }
            return ""
        }
    },
    watch: {
        cookSubS: {
            handler(newV, oldV) {
                // 确保数据结构完整性
                this.ensureDataIntegrity()

                // 只在开发环境下输出调试信息，并处理undefined情况
                if (process.env.NODE_ENV === 'development') {
                    console.log('cookSubS 新值:', newV);
                    console.log('cookSubS 旧值:', oldV || '初始化');
                }
            },
            deep: true,
            immediate: true
        },

        cookSubSNew: {
            handler(newV) {
                this.updateArr = newV
                // 确保数据结构完整性
                this.ensureDataIntegrity()

                // 只在开发环境下输出调试信息
                if (process.env.NODE_ENV === 'development') {
                    console.log('cookSubSNew 更新:', newV, '分类列表:', this.caterClassifyList);
                }
            },
            deep: true
        },

        // 监听 addOrEdit 变化，同步子组件的分类选择状态
        addOrEdit: {
            handler(newVal) {
                if (newVal === 'add' || newVal === 'edit') {
                    // 切换到新增或编辑模式时，同步重置子组件的分类选择
                    this.cateId = ""
                }
            },
            immediate: false
        },

        // 监听分类数据变化，当分类数据加载完成后设置cookType和颜色
        caterClassifyList: {
            handler(newVal) {
                if (newVal && newVal.length > 0) {
                    // 分类数据加载完成后，初始化颜色映射
                    this.initializeCategoryColors()

                    if (this.addOrEdit === 'edit' && this.cookSubSNew.length > 0) {
                        // 为编辑模式的菜品设置正确的cookType
                        this.$nextTick(() => {
                            this.setCookTypeForEditMode()
                        })
                    }
                }
            },
            immediate: false
        }

    },

    beforeDestroy() {
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }
    }

}

</script>

<style scoped lang="less">
.recipe-tem-component {
    width: 100%;
    height: 100vh; // 使用视口高度
    background: #f8f9fa;
    display: flex;
    flex-direction: column;

    // 模板头部区域
    .template-header {
        background: #ffffff;
        border: 1px solid #cee0f0;
        border-bottom: none;
        padding: 20px;
        flex-shrink: 0; // 防止头部被压缩

        .header-title {
            display: flex;
            align-items: center;
            padding: 0;

            > span {
                font-size: 18px;
                font-weight: 600;
                color: #2b3346;
                margin-left: 8px;
            }

            .template-info {
                margin-left: auto;
                display: flex;
                align-items: center;
                padding: 6px 16px;
                border-radius: 16px;
                background: #f0f9ff;
                color: #19be6b;
                font-size: 14px;

                &.new {
                    background: #eff6ff;
                    color: #2b5fda;
                }

                span {
                    margin-left: 6px;
                    font-size: 14px;
                    font-weight: normal;
                }
            }
        }
    }

    .selection {
        width: 100%;
        flex: 1; // 使用弹性布局，自动计算高度
        height: 0; // 配合flex: 1 实现正确的高度计算
        display: flex;
        border: 1px solid #cee0f0;
        border-top: none;

        .catering-list {
            width: 380px;
            height: 100%;
            max-height: 100%; // 确保不超过父容器高度
            background-color: #fff;
            border-right: 1px solid #cee0f0;
            display: flex;
            flex-direction: column;
            overflow: hidden; // 防止内容溢出

            .dishes-panel {
                height: 100%;
                max-height: 100%; // 确保不超过父容器高度
                display: flex;
                flex-direction: column;
                overflow: hidden; // 防止内容溢出

                .panel-header {
                    padding: 16px 20px;
                    border-bottom: 1px solid #e9edf5;
                    display: flex;
                    align-items: center;
                    background: #f8f9fa;
                    flex-shrink: 0; // 防止头部被压缩

                    span {
                        font-size: 16px;
                        font-weight: 600;
                        color: #2b3346;
                        margin-left: 8px;
                    }
                }

                .search-section {
                    padding: 16px 20px;
                    border-bottom: 1px solid #e9edf5;
                    flex-shrink: 0; // 防止搜索区域被压缩

                    .search-tips {
                        margin-top: 8px;
                        display: flex;
                        align-items: center;
                        color: #ff9900;
                        font-size: 14px;

                        span {
                            margin-left: 4px;
                        }
                    }
                }

                .dishes-tabs {
                    flex: 1;
                    display: flex;
                    flex-direction: column;

                    :deep(.ivu-tabs-bar) {
                        flex-shrink: 0; // 防止标签页头部被压缩
                    }

                    :deep(.ivu-tabs-content) {
                        flex: 1;
                        height: 0;
                    }

                    :deep(.ivu-tabs-tabpane) {
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                    }

                    .add-dish-section {
                        padding: 16px 20px;
                        border-bottom: 1px solid #e9edf5;
                        background: #fafbfc;
                        flex-shrink: 0; // 防止添加菜品区域被压缩

                        .add-dish-form {
                            display: flex;
                            gap: 8px;
                        }

                        .add-tips {
                            margin-top: 8px;
                            display: flex;
                            align-items: center;
                            color: #ed4014;
                            font-size: 14px;

                            span {
                                margin-left: 4px;
                            }
                        }
                    }

                    .dishes-list-container {
                        flex: 1;
                        height: 0; // 配合flex: 1 实现正确的高度计算
                        padding: 16px 20px;
                        overflow-y: auto; // 只显示垂直滚动条
                        overflow-x: hidden; // 隐藏水平滚动条

                        // 自定义滚动条样式
                        &::-webkit-scrollbar {
                            width: 6px;
                        }

                        &::-webkit-scrollbar-track {
                            background: #f1f1f1;
                            border-radius: 3px;
                        }

                        &::-webkit-scrollbar-thumb {
                            background: #c1c1c1;
                            border-radius: 3px;

                            &:hover {
                                background: #a8a8a8;
                            }
                        }

                        .dish-card {
                            display: flex;
                            align-items: center;
                            margin-bottom: 12px;
                            background: #ffffff;
                            border: 1px solid #e9edf5;
                            border-radius: 6px;
                            overflow: hidden;
                            transition: all 0.3s ease;
                            cursor: grab;
                            position: relative;

                            &:hover {
                                border-color: #2b5fda;
                                box-shadow: 0 2px 8px rgba(43, 95, 218, 0.15);
                                transform: translateY(-1px);
                            }

                            &.dragging {
                                opacity: 0.8;
                                transform: rotate(2deg);
                            }

                            .dish-category {
                                width: 60px;
                                height: 48px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: #ffffff;
                                font-size: 12px;
                                font-weight: 600;
                                text-align: center;
                                line-height: 1.2;
                            }

                            .dish-name {
                                flex: 1;
                                padding: 0 12px;
                                font-size: 14px;
                                color: #2b3346;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }

                            .dish-actions {
                                padding: 0 12px;
                                display: flex;
                                gap: 8px;
                                opacity: 0;
                                transition: opacity 0.3s ease;

                                .action-icon {
                                    font-size: 16px;
                                    cursor: pointer;
                                    transition: color 0.3s ease;

                                    &.edit-icon:hover {
                                        color: #2b5fda;
                                    }

                                    &.delete-icon:hover {
                                        color: #ed4014;
                                    }
                                }
                            }

                            .drag-handle {
                                padding: 0 8px;
                                color: #c5c8ce;
                                cursor: grab;

                                &:active {
                                    cursor: grabbing;
                                }
                            }

                            &:hover .dish-actions {
                                opacity: 1;
                            }
                        }

                        .empty-state {
                            text-align: center;
                            padding: 40px 20px;
                            color: #c5c8ce;

                            p {
                                margin: 8px 0;
                                font-size: 14px;

                                &:first-of-type {
                                    font-size: 16px;
                                    color: #8d99a5;
                                }
                            }
                        }
                    }
                }
            }
        }


        .recipes-content-box {
            flex: 1;
            height: auto; // 根据内容自动调整高度
            background: #ffffff;
            display: flex;
            flex-direction: column;

            .recipe-panel {
                height: auto; // 根据内容自动调整高度
                display: flex;
                flex-direction: column;

                .panel-header {
                    padding: 16px 20px;
                    border-bottom: 1px solid #e9edf5;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    background: #f8f9fa;
                    flex-shrink: 0; // 防止头部被压缩

                    span {
                        font-size: 16px;
                        font-weight: 600;
                        color: #2b3346;
                        margin-left: 8px;
                    }

                    .panel-actions {
                        display: flex;
                        gap: 8px;
                    }
                }

                .recipe-table-container {
                    flex: 1;
                    height: auto; // 根据内容自动调整高度
                    overflow: visible; // 不显示滚动条
                    padding: 20px;

                    .recipe-table {
                        width: 100%;
                        min-width: 1000px; // 减小最小宽度，适应更多屏幕
                        border-collapse: separate;
                        border-spacing: 0;
                        border: 1px solid #e9edf5;
                        border-radius: 6px;
                        overflow: hidden;
                        background: #ffffff;

                        thead {
                            .table-header {
                                background: #f8f9fa;

                                th {
                                    padding: 16px 12px;
                                    border-bottom: 1px solid #e9edf5;
                                    border-right: 1px solid #e9edf5;
                                    text-align: center;
                                    font-weight: 600;
                                    color: #2b3346;

                                    &:last-child {
                                        border-right: none;
                                    }

                                    &.meal-header {
                                        width: 120px;
                                        background: #eff6ff;

                                        .header-content {
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            gap: 4px;
                                        }
                                    }

                                    &.day-header {
                                        width: 160px;

                                        .day-content {
                                            display: flex;
                                            flex-direction: column;
                                            align-items: center;
                                            gap: 8px;

                                            .day-name {
                                                font-size: 16px;
                                                font-weight: 600;
                                                color: #2b3346;
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        tbody {
                            .meal-row {
                                &:nth-child(odd) {
                                    background: #fafbfc;
                                }

                                .meal-cell {
                                    width: 120px;
                                    padding: 16px 12px;
                                    border-bottom: 1px solid #e9edf5;
                                    border-right: 1px solid #e9edf5;
                                    background: #eff6ff;
                                    vertical-align: top;

                                    .meal-info {
                                        display: flex;
                                        flex-direction: column;
                                        align-items: center;
                                        gap: 8px;

                                        .meal-name {
                                            font-size: 16px;
                                            font-weight: 600;
                                            color: #2b5fda;
                                        }

                                        .delete-meal-icon {
                                            font-size: 18px;
                                            color: #c5c8ce;
                                            cursor: pointer;
                                            transition: color 0.3s ease;

                                            &:hover {
                                                color: #ed4014;
                                            }
                                        }
                                    }
                                }

                                .dish-cell {
                                    width: 160px;
                                    padding: 8px;
                                    border-bottom: 1px solid #e9edf5;
                                    border-right: 1px solid #e9edf5;
                                    vertical-align: top;
                                    position: relative;
                                    background: #fafbfc;

                                    &:last-child {
                                        border-right: none;
                                    }

                                    &.drop-zone-active {
                                        background: #f0f9ff;
                                        border-color: #2b5fda;

                                        .drop-zone-full {
                                            border-color: #2b5fda;
                                            background: rgba(43, 95, 218, 0.05);
                                        }
                                    }

                                    &.drop-zone-invalid {
                                        background: #fff2f0;
                                        border-color: #ed4014;

                                        .drop-zone-full {
                                            border-color: #ed4014;
                                            background: rgba(237, 64, 20, 0.05);
                                            cursor: not-allowed;
                                        }
                                    }

                                    &.has-dishes {
                                        background: #f0f9ff;
                                        border-color: #d1e7ff;

                                        .drop-zone-full {
                                            background: rgba(43, 95, 218, 0.02);
                                            border-color: #b3d7ff;
                                        }
                                    }

                                    .drop-zone-full {
                                        min-height: 120px;
                                        border: 2px dashed #e9edf5;
                                        border-radius: 6px;
                                        padding: 8px;
                                        transition: all 0.3s ease;
                                        position: relative;
                                        background: #ffffff;

                                        &:hover {
                                            border-color: #2b5fda;
                                            background: rgba(43, 95, 218, 0.02);
                                        }

                                        .dish-item {
                                            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                                            border: 1px solid #d1e7ff;
                                            border-radius: 6px;
                                            margin-bottom: 6px;
                                            position: relative;
                                            overflow: hidden;
                                            transition: all 0.3s ease;
                                            box-shadow: 0 1px 3px rgba(43, 95, 218, 0.1);

                                            &:hover {
                                                border-color: #2b5fda;
                                                box-shadow: 0 3px 8px rgba(43, 95, 218, 0.2);
                                                transform: translateY(-1px);
                                            }

                                            .dish-category-bar {
                                                position: absolute;
                                                left: 0;
                                                top: 0;
                                                width: 6px;
                                                height: 100%;
                                                border-radius: 0 3px 3px 0;

                                                // 颜色现在通过 :style 动态设置，不再使用硬编码的CSS类
                                                // 这样可以支持任意数量的分类和自定义颜色
                                            }

                                            .dish-item-content {
                                                padding: 10px 12px 10px 18px;
                                                display: flex;
                                                align-items: center;
                                                justify-content: space-between;

                                                .dish-item-name {
                                                    flex: 1;
                                                    font-size: 14px;
                                                    font-weight: 500;
                                                    color: #2b3346;
                                                    overflow: hidden;
                                                    text-overflow: ellipsis;
                                                    white-space: nowrap;
                                                }

                                                .remove-dish-icon {
                                                    font-size: 16px;
                                                    color: #c5c8ce;
                                                    cursor: pointer;
                                                    opacity: 0;
                                                    transition: all 0.3s ease;
                                                    padding: 2px;
                                                    border-radius: 50%;

                                                    &:hover {
                                                        color: #ffffff;
                                                        background: #ed4014;
                                                        opacity: 1;
                                                    }
                                                }
                                            }

                                            &:hover .remove-dish-icon {
                                                opacity: 0.7;
                                            }
                                        }

                                        .drop-placeholder {
                                            display: flex;
                                            flex-direction: column;
                                            align-items: center;
                                            justify-content: center;
                                            height: 80px;
                                            color: #8d99a5;
                                            font-size: 14px;
                                            background: rgba(43, 95, 218, 0.02);
                                            border: 1px dashed #d1e7ff;
                                            border-radius: 4px;
                                            margin: 4px 0;

                                            span {
                                                margin-top: 6px;
                                                font-weight: 500;
                                            }
                                        }

                                        .dish-count {
                                            position: absolute;
                                            bottom: 6px;
                                            right: 6px;
                                            background: linear-gradient(135deg, #2b5fda, #1e4ba8);
                                            color: #ffffff;
                                            font-size: 11px;
                                            font-weight: 600;
                                            padding: 3px 8px;
                                            border-radius: 12px;
                                            box-shadow: 0 2px 4px rgba(43, 95, 218, 0.3);
                                            z-index: 10;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 拖拽相关样式
.dragging-item {
    opacity: 0.9;
    transform: rotate(1deg) scale(1.02);
    z-index: 1000;
    box-shadow: 0 8px 24px rgba(43, 95, 218, 0.4) !important;
    border: 2px solid #2b5fda !important;
}

.ghost-item {
    opacity: 0.4;
    background: linear-gradient(135deg, #f0f9ff, #e6f3ff) !important;
    border: 2px dashed #2b5fda !important;
    border-radius: 6px !important;
}

// 拖拽目标区域样式增强
:deep(.sortable-ghost) {
    background: rgba(43, 95, 218, 0.1) !important;
    border: 2px dashed #2b5fda !important;
    border-radius: 6px !important;
}

:deep(.sortable-chosen) {
    background: rgba(43, 95, 218, 0.05) !important;
    border: 1px solid #2b5fda !important;
}

// 菜品分类颜色 - 现在使用动态颜色，这些样式已废弃
// 保留注释以备参考
// .staple-food { background-color: #3097ff; }
// .Meat-dishes { background-color: #ed8433; }
// .vegetable-dish { background-color: #33bb83; }
// .Complementary-foods { background-color: #8e6bee; }

// 保存模板弹框样式
.template-preview {
    margin-top: 16px;

    .preview-title {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 600;
        color: #2b3346;

        span {
            margin-left: 6px;
        }
    }

    .preview-content {
        background: #f8f9fa;
        border: 1px solid #e9edf5;
        border-radius: 6px;
        padding: 16px;

        .preview-stats {
            display: flex;
            gap: 24px;

            .stat-item {
                display: flex;
                align-items: center;

                .stat-label {
                    color: #8d99a5;
                    font-size: 14px;
                }

                .stat-value {
                    color: #2b5fda;
                    font-weight: 600;
                    font-size: 16px;
                }
            }
        }
    }
}

// 底部按钮区域
.bsp-base-fotter {
    padding: 16px 20px;
    background: #ffffff;
    border-top: 1px solid #e9edf5;
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-shrink: 0; // 防止被压缩
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

</style>
