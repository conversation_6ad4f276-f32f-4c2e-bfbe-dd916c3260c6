let menuMode=serverConfig.menuMode
import main from  '@/components/app-main/index.vue'
import mainNew from '@/components/main-menu-new/main.vue'
export default [
  {
    path: '/sygl',
    name: 'sygl',
    meta: {
      title: '送药管理'
    },
    component:menuMode=='side'?mainNew:main,// () => import('@/components/app-main/index.vue'),
    children: [
      {
        path: 'sytx',
        name: 'sytx',
        meta: {
          title: '送药提醒',
          menu: true,
          bread: true
        },
        component: () => import('@/view/sygl/sytx/index.vue')
      },
      {
        path: 'yytz',
        name: 'yytz',
        meta: {
          title: '用药台账',
          menu: true,
          bread: true
        },
        component: () => import('@/view/sygl/yytz/index.vue')
      },
      {
        path: '/hearthly',
        name: 'hearthly',
        meta: {
          title: '医疗档案',
          menu: true,
          bread: true
        },
        component: () => import('@/view/hearthly/index.vue')
      },
      {
        path: 'adviceManage',
        name: 'adviceManage',
        meta: {
          title: '生效医嘱',
          menu: true,
          bread: true
        },
        component: () => import('@/view/sygl/adviceManage/index.vue')
      }
    ]
  }
]
