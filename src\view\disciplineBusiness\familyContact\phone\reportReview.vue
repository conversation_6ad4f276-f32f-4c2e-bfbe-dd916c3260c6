<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <div class="bb-wrap" style="width: 100%;">
      <div class="bb-wrap-left" style="margin-left: 0;">
        <p class="sys-sub-title" style="margin-top: 0;">人员信息</p>
        <ryxx :ryxxObj="ryxxObj" :jgrybm="jgrybm" v-if="jgrybm" style="margin-top: -16px;"/>
        <record :jgrybm="jgrybm"/>
      </div>
      <div class="bb-wrap-right fm-content-info" style="width: 70%;padding-left: 40px;">
        <p class="sys-sub-title" style="margin-top: 13px;">亲情电话登记</p>
        <div class="detail-right-top">
          <detail :formData="formData" style="width: 100%;"/>
        </div>
        <div class="detail-right-bottom">
          <p class="sys-sub-title" style="margin-top: 20px;">亲情电话审批</p>
          <Form v-if="saveType === 'approval' || saveType === 'info'" ref="releaseForm" :model="approvalData" :label-width="120"
                :label-colon="true" class="base-form-container">
            <Row>
              <Col span="24">
                <FormItem label="审批结果" prop="status"
                          :rules="[{ trigger: 'blur,change', message: '请选择', required: saveType === 'info'? false : true}]"
                          style="width: 100%;">
                  <RadioGroup v-model="approvalData.status">
                    <Radio label="02" :disabled="saveType ==='info'">同意</Radio>
                    <Radio label="03" :disabled="saveType ==='info'">不同意</Radio>
                  </RadioGroup>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="24">
                <FormItem label="备注" style="width: 100%;"
                          :rules="[{ trigger: 'blur,change', message: '请选择', required: approvalData.status === '03'? true: false}]">
                  <Input type="textarea" :autosize="{minRows: 3,maxRows: 5}"
                         v-model="approvalData.approvalComments" placeholder="" maxlength=""
                         style="width: 100%;"></Input>
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
    </div>

    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回</Button>
      <Button style="margin: 0 20px" type="primary" :loading="loading" v-if="saveType === 'approval'" @click="handleSubmit()">
        保 存
      </Button>
    </div>
  </div>
</template>

<script>
import ryxx from "./ryxx.vue"
import record from "./record.vue"
import { prisonSelect } from 'sd-prison-select'
import detail from "./detail.vue"

export default {
  components: {ryxx, prisonSelect, record, detail},
  props: {
    curId: {
      default: '',
      type: String
    },
    jgrybm: {
      default: '',
      type: String
    },
    saveType: {
      default: 'add',
      type: String
    },
  },
  data() {
    return {
      editTitle: '亲情电话登记',
      ryxxObj: {},
      loading: false,
      formData: {},
      approvalData: {
        id: this.curId
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close', false)
    },
    handleSubmit() {
      if (!this.approvalData.status) {
        this.$Message.error('审批结果不能为空')
        return
      }
      if (this.approvalData.status === '03' && !this.approvalData.approvalComments){
        this.$Message.error('审批备注不能为空')
        return
      }
      this.loading = true
      this.$store.dispatch("authPostRequest", {
        url: this.$path.familyContact_phone_approval,
        params: this.approvalData
      }).then(res => {
        if (res.success) {
          this.loading = false
          this.handleClose()
          this.$Message.success('操作成功')
        } else {
          this.$Message.error(res.msg || '保存失败！')
          this.loading = false
        }
      })
    },
    getData() {
      this.$store.dispatch("authGetRequest", {
        url: this.$path.familyContact_phone_get,
        params: {
          id: this.curId
        }
      }).then(res => {
        if (res.success) {
          this.formData = res.data
          this.approvalData.status = this.formData.status
          this.approvalData.approvalComments = this.formData.approvalComments
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败'
          })
        }
      })
    },
  },
  mounted() {
    this.getData()
  }
}
</script>

<style scoped lang="less">

.detail-right-top {
  margin-bottom: 2px;
}

.detail-right-bottom {
  height: 70%;
}

.bb-wrap {
  overflow: hidden;
  height: 100%; // calc( ~'100% - 0px');
  display: flex;
}

.bb-wrap-left {
  padding-top: 16px;
  width: 400px;
  border-right: 1px solid #cee0f0;
  height: 100%;
}

.el-select-dropdown__item {
  text-indent: 16px !important;
}
</style>
