<template>
  <div>
    <div class="table-container" v-if="tableContainer">
      <s-DataGrid ref="grid" funcMark="mdmgl" :customFunc="true" :params="params">
        <template slot="customHeadFunc" slot-scope="{ func }">
          <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':mdmgl:add')"
            @click.native="handleAdd('add')">新增</Button>
        </template>
        <template slot="customRowFunc" slot-scope="{ func, row, index }">
          <Button type="primary" v-if="func.includes(globalAppCode + ':mdmgl:detail')"
            @click.native="handleDetail(index, row)">详情</Button>
        </template>
      </s-DataGrid>
    </div>
    <div v-if="addFormInfo">
      <div class="dj-container">
        <div class="left-upload-img" v-if="!selectRoomIds">
          <img src="@/assets/images/jytp.png" alt="">
          <span @click="handleSelectRoomId">点击选择监室</span>
        </div>
        <div class="left-upload-img-2" v-else>
          <!-- <img :src="roomInfo.roomSex == '1' ? '../../../assets/icons/man.svg' : '../../../assets/icons/woman.svg'"
            alt=""> -->
          <img v-if="roomInfo.roomSex == '1'" src="../../../assets/icons/man.svg" alt="">
          <img v-else src="../../../assets/icons/woman.svg" alt="">
          <span>{{ roomInfo.roomName }}</span>
        </div>
        <div>
          <div class="fm-content-wrap" style="border-top:1px solid #cee0f0 ;padding: 0">
            <p class="fm-content-wrap-title">
              <Icon type="md-list-box" size="24" color="#2b5fda" />登记信息
            </p>

            <div class="add-form-header">
              <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="150">
                <Row>
                  <Col span="23">
                  <FormItem label="检查民警" prop="checkPoliceSfzh"
                    :rules="{ required: true, message: '检查民警不能为空', trigger: 'blur, change' }">

                    <user-selector v-model="formValidate.checkPoliceSfzh" tit="用户选择"
                      :text.sync="formValidate.checkPolice" returnField="idCard" numExp='num==1'
                      msg="至少选中1人"></user-selector>
                  </FormItem>
                  </Col>
                  <Col span="23">
                  <FormItem label="检查时间" prop="checkTime"
                    :rules="{ required: true, type: 'date', message: '请选择检查时间', trigger: 'change' }">
                    <DatePicker type="datetime" v-model="formValidate.checkTime" format="yyyy-MM-dd HH:mm:ss" />
                  </FormItem>
                  </Col>
                  <Col span="23">
                  <FormItem label="情况记录" prop="situationRecord"
                    :rules="{ required: true, message: '请输入情况记录', trigger: 'blur' }">
                    <Input v-model="formValidate.situationRecord" placeholder="请输入情况记录" type="textarea"
                      :autosize="{ minRows: 2, maxRows: 5 }"></Input>
                  </FormItem>
                  </Col>
                </Row>
              </Form>
            </div>
          </div>
        </div>
      </div>
      <div class="bsp-base-fotter">
        <Button @click="handleReset('formValidate')" style="margin-right: 10px;">取消</Button>
        <Button type="primary" @click="handleSubmit('formValidate')">确认</Button>
      </div>
    </div>
    <div v-if="detailContainer">
      <Form ref="formData" inline>
        <div class="fm-content-box">
          <p class="fm-content-wrap-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />登记信息
          </p>
          <Row>
            <Col span="3" class="col-title"><span>监室号</span></Col>
            <Col span="9"><span>
              {{ detailInfo.roomInfo.roomName }}
            </span>
            </Col>
            <Col span="3" class="col-title"><span>数据来源</span></Col>
            <Col span="9"><span>
              {{ detailInfo.dataSourcesName }}
            </span></Col>
          </Row>

          <Row>
            <Col span="3" class="col-title"><span>检查民警</span></Col>
            <Col span="9"><span>
              {{ detailInfo.checkPolice }}
            </span>
            </Col>
            <Col span="3" class="col-title"><span>检查时间</span></Col>
            <Col span="9"><span>
              {{ detailInfo.checkTime }}
            </span></Col>
          </Row>

          <Row>
            <Col span="3" class="col-title"><span>情况记录</span></Col>
            <Col span="21"><span>
              {{ detailInfo.situationRecord }}
            </span>
            </Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>登记人</span></Col>
            <Col span="9"><span>
              {{ detailInfo.addUserName }}
            </span>
            </Col>
            <Col span="3" class="col-title"><span>登记时间</span></Col>
            <Col span="9"><span>
              {{ detailInfo.addUser }}
            </span></Col>
          </Row>

          <Row>
            <Col span="3" class="col-title"><span>照片信息</span></Col>
            <Col span="21"><span>
              <el-image :src="detailInfo.snapPhoto" :preview-src-list="[detailInfo.snapPhoto]" fit="cover"
                style="width: 100px; height: 100px;margin-top: 10px;"></el-image>
            </span>
            </Col>
          </Row>

        </div>
      </Form>
      <div class="bsp-base-fotter">
        <Button @click="handleCancel" style="margin-right: 10px;">取消</Button>
      </div>
    </div>
    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
      title="监室列表">
      <div class="select-use">
        <roomSelect v-if="openModal" ref="prisonSelect" ryzt="ALL" :isMultiple="false" />
      </div>
      <div slot="footer">
        <Button type="primary" @click="useSelect" class="save">确 定</Button>
        <Button @click="openModal = false" class="save">关 闭</Button>
      </div>
    </Modal>

  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions, mapState } from 'vuex'
import { userSelector } from 'sd-user-selector'
// import { fileUpload } from 'sd-minio-upfile'
// import cusSelect from '../components/cusSelect.vue
import { roomSelect } from 'sd-room-select'
export default {
  name: "facetoface",
  data() {

    return {
      tableContainer: true,
      detailContainer: false,
      addFormInfo: false,
      params: {},
      modalVisible: false,
      formValidate: {
        // roomId: "0000000000000100030012", // TODO写死的
        checkPoliceSfzh: this.$store.state.common.idCard,
        checkPolice: this.$store.state.common.userName,
        checkTime: new Date(),
        dataSources: "1",
        situationRecord: "",
      },
      ruleValidate: {
        checkPoliceSfzh: [{ required: true, message: '参加民警不能为空', trigger: 'blur, change' }],
        checkTime: [{ required: true, type: 'date', message: '请选择检查时间', trigger: 'change' }],
        situationRecord: [{ required: true, message: '检查内容不能为空', trigger: 'blur' }]
      },
      situationRecordName: '',
      openModal: false,
      selectRoomIds: "",
      roomInfo: {
        roomSex: "",
        roomName: ""
      },
      detailInfo: {}
    }

  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleAdd() {
      this.tableContainer = false
      this.addFormInfo = true
    },
    handleDetail(index, row) {
      this.authGetRequest({ url: this.$path.faceToFace_detail, params: { id: row.id } }).then(res => {
        if (res.success) {
          this.tableContainer = false
          this.detailContainer = true
          this.detailInfo = res.data
          // this.detailInfo.snapPhoto = this.detailInfo.snapPhoto.replace("192.168.3.251", "172.26.0.22")
        }
      })
    },
    handleCancel() {
      this.tableContainer = true
      this.detailContainer = false
    },
    formatDate(date) {
      // 将Date对象格式化为"yyyy-MM-dd HH:mm:ss"格式
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleReset(name) {
      this.$refs[name].resetFields();
      // this.formValidate = {}
      this.tableContainer = true
      this.addFormInfo = false
      this.selectRoomIds = ""
      this.roomInfo = {}
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          console.error(valid);
          // if (this.selectUseIds) {
          this.formValidate.checkTime = this.formatDate(this.formValidate.checkTime)
          this.authPostRequest({ url: this.$path.faceToFace_create, params: { ...this.formValidate, roomId: this.selectRoomIds } }).then(res => {
            if (res.success) {
              // this.formValidate = {}
              this.tableContainer = true
              this.addFormInfo = false
              this.$refs[name].resetFields();
              this.$refs.grid.query_grid_data(1)
              this.$Message.success('新增成功')
            } else {
              this.$Message.error(res.message)
            }
          })
        } else {
          this.$Message.error('验证未通过');
        }
      })
    },
    handleGetlistByOrgCode() {
      this.authGetRequest({ url: this.$path.faceToFaceConfig_listByOrgCode }).then(res => {
        if (res.success) {
          this.formValidate.situationRecord = res.data.map((item, index) => { return `${index + 1}、${item.checkItem}` }).join(',')
        }
      })
    },
    useSelect() {

      this.roomInfo = this.$refs.prisonSelect.checkedRoom[0]
      this.selectRoomIds = this.roomInfo.id
      this.openModal = false
      console.error(this.$refs.prisonSelect.checkedRoom);

      // this.openModal = false
    },
    handleSelectRoomId() {
      this.openModal = true
    },
  },

  components: {
    sDataGrid,
    userSelector,
    roomSelect
  },

  created() {
    this.handleGetlistByOrgCode()
  },

  computed: {},

}

</script>

<style scoped lang="less">
@import "~@/assets/style/formInfo.css";

//
/deep/.fm-content-wrap-title {
  border-bottom: 1px solid #cee0f0;
  background: #eff6ff;
  line-height: 40px;
  padding-left: 10px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: bold;
  font-size: 16px;
  color: #00244A;
  /* margin-bottom: 16px; */
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-bottom: 1px solid #CEE0F0;
}

.dj-container {
  display: flex;
  justify-content: space-between;
  align-items: center;

  // .left-upload-img-2 {
  //    display: flex;
  //   flex-direction: column;
  //   align-items: center;
  //   margin-right: 20px;
  //   width: 286px;
  //   // height: 262px;
  //   background: url('../../../assets/images/fkBg.svg');
  //   background-size: contain;
  //   /* 或 contain */
  //   background-position: center center;
  //   background-repeat: no-repeat;

  //   img {
  //     padding-top: 20px;
  //   }

  //   span {
  //     display: inline-block;
  //     margin-bottom: 10px;
  //     color: #2b60d9;
  //     cursor: pointer;
  //   }
  // }

  .left-upload-img-2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 20px;
    width: 240px;
    height: 190px;
    margin-top: 20px;
    background: url('../../../assets/images/fkBg.svg');
    background-size: 100% 100%;
    background-repeat: no-repeat;

    img {
      width: 128px;
      height: 148px;
      margin-bottom: 10px;
      margin-top: 20px;
    }

    span {
      display: inline-block;
      color: #2b60d9;
      padding-bottom: 10px;
      cursor: pointer;
    }
  }

  .left-upload-img {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 20px;
    width: 286px;
    // height: 262px;
    background: url('../../../assets/images/fkBg.svg');
    background-size: contain;
    /* 或 contain */
    background-position: center center;
    background-repeat: no-repeat;

    img {
      padding-top: 20px;
    }

    span {
      display: inline-block;
      margin-bottom: 10px;
      color: #2b60d9;
      cursor: pointer;
    }
  }
}
</style>
