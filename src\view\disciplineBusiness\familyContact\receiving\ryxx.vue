<template>
  <div class='jgrySelect-info'>
    <div class='jgrySelect-flex'>
      <img :src="formData.frontPhoto? http+formData.frontPhoto:defaultImg"
           style="width: 88px;height:110px;margin-right: 10px;"/>
      <div>
        <p><span class='xm'>{{ formData.xm ? formData.xm : '-' }}</span>&nbsp;&nbsp;<span
          class='xm'>{{ formData.roomName }}</span></p>
        <p><span>证件号码：</span><span>{{ formData.zjhm }}</span></p>
        <p><span>出生日期：</span><span>{{ formData.csrq }}</span></p>
        <p><span>籍  贯：</span><span>{{ formData.jgName }}</span></p>
        <p><span>民  族：</span><span>{{ formData.mzName }}</span></p>
      </div>
    </div>
    <div class='jgrySelect-flex' style='margin-top:16px;'>
      <div style='width:10px;padding:16px 26px 0 16px;background:#e6e9f2;border-radius:6px;margin-right:16px'>案件名称
      </div>
      <div>
        <p><span>涉嫌罪名：</span><span>{{ formData.xszm }}</span></p>
        <p><span>案件编号：</span><span>{{ formData.ajbh }}</span></p>
        <p><span>诉讼环节：</span><span>{{ formData.sshjName }}</span></p>
        <p><span>入所时间：</span><span>{{ formData.rssj }}</span></p>
        <p><span>关押期限：</span><span>{{ formData.gyqx }}</span></p>
        <p><span>办案单位：</span><span>{{ formData.basj }}</span></p>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    jgrybm: String
  },
  data() {
    return {
      formData: {},
      defaultImg: require('@/assets/images/main.png'),
      http: serverConfig.severHttp
    }
  },
  watch: {
    'jgrybm': {
      handler(n, o) {
        if (n) {
          this.getPrisonerSelectCompomenOne()
        }
      }, deep: true, immediate: true
    }
  },
  methods: {
    getPrisonerSelectCompomenOne() {
      let params = {
        jgrybm: this.jgrybm,
        ryzt: 'ZS'
      }
      this.$store.dispatch('authGetRequest', {
        url: '/acp-com/base/pm/prisoner/getPrisonerSelectCompomenOne',
        params: params
      }).then(resp => {
        if (resp.code === 0) {
          this.formData = resp.data
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
  }
}
</script>

<style lang='less' scoped>
.jgrySelect-info {
  border: 1px solid #dcdee2;
  padding: 16px 0 31px 16px;
  margin-bottom: 16px;
  // margin:16px 16px 0 0;
  border-radius: 6px;

  .xm {
    font-size: 18px;
    font-weight: 700;
  }
}

.jgrySelect-flex {
  display: flex;
}
</style>
