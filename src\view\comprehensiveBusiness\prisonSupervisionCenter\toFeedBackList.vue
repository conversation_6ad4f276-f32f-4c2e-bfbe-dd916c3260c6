<template>
  <div>
    <ui-condition-box v-model="param"  :instance="$refs['uiCardTable']">
      <ui-condition-item label="反馈标题" prop="title">
        <ui-input v-model="param.title"    class="com-condition-width-1"></ui-input>
      </ui-condition-item>
      <ui-condition-item label="反馈人" prop="answerName">
        <ui-input v-model="param.answerName"    class="com-condition-width-1"></ui-input>
      </ui-condition-item>
      <ui-condition-item label="反馈时间" :prop="['answerStartTime','answerEndTime']">
        <ui-date-picker
          class="com-condition-width-2"
          type="datetimerange"
          v-model="param.answerStartTime"
          :end-time="param.answerEndTime"
          @end-change="param.answerEndTime = $event"
        ></ui-date-picker>
      </ui-condition-item>
    </ui-condition-box>
    <com-hr/>
    <div class="com-table-wrapper">
      <ui-card-table
        :height="$getTableHeight()"
        :get-data-method="operate"
        :get-data-param="param"
        :data.sync="tableData"
        ref="uiCardTable"
        @on-change="changeSelect"
      >
        <div slot="operateBox">
          <ui-button :disabled="selectData.length === 0" @click="handleSend(selectData)">提交</ui-button>
          <ui-button :disabled="selectData.length === 0" type="danger" @click="handleDelete(selectData)">删除</ui-button>
        </div>

        <ui-table slot="table" full :columns="columns" :data="tableData" @on-selection-change="tableSelect">
          <template slot="operate"   slot-scope="{row}" >
            <span class="com-table-btn"  @click="toPage(row)">信息补充</span>
          </template>
        </ui-table>

        <div slot="card" class="table-card-container">
          <div class="table-card-item" v-for="item in tableData" :key="item.id">
            <div class="table-card-head-box">
              <ui-checkbox  class="head-check"  v-model="checkData[item.id]"  @on-change="cardSelect"></ui-checkbox>
              <div class="head-item">
                <label>反馈标题：</label>
                <span>{{item.title}}</span>
              </div>
            </div>
            <div class="table-card-content-box">
              <div class="content-row">
                <div class="content-item">
                  <label>反馈人：</label>
                  <span>{{item.answerUserName}}</span>
                </div>
                <div class="content-item">
                  <label>反馈时间：</label>
                  <span>{{item.answerTime}}</span>
                </div>
                <div class="content-item">
                  <label>关联督导数：</label>
                  <span>{{item.countNumber}}</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-item">
                  <label>详情：</label>
                  <span>{{item.detail}}</span>
                </div>
              </div>
              <div class="content-row">
                <div class="content-item w100">
                  <label>附件：</label>
                  <ui-multi-uploader :value="trainsFromData(item.files)" readonly/>
                </div>
              </div>
              <div class="content-operate">
                <div class="operate">
                  <ui-button @click="toPage(item)" class="mr-10">信息补充</ui-button>
                  <ui-button type="danger" @click="handleDelete([item])">删除</ui-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ui-card-table>
    </div>
  </div>
</template>

<script>
import {fileTranslate} from "@/util";
import { answerSupervisePage, delAnswerForm, sendAnswerForm } from "@/axios/zhjgBranchWork";
import {selectData} from "@/mixins";
export default {
  data() {
    return {
      userInfo: this.$store.state.userInfo,
      columns: [
        {
          type: "selection",
          width: 60,
        },
        {
          title: "序号",
          key: "index",
          width: 80,
        },
        {
          title: "反馈单位",
          key: "unitName",
        },
        {
          title: "反馈人",
          key: "answerUserName",
        },
        {
          title: "反馈时间",
          key: "answerTime",
          width: 220,
        },
        {
          title: "关联督导数",
          key: "countNumber",
        },
        {
          title: "反馈详情",
          key: "detail",
        },
        {
          title: "操作",
          slot: "operate",
          width: 180,
        },
      ],
      tableData: [],
      param: {},
    };
  },
  mixins: [selectData],
  methods: {
    toPage(item) {
      this.$toPage({
        name: this.$hasAuth("JSDD_SPFK") ? "prisonApprovalFeedBack" : "prisonSendFeedBack",
        params: {id: item.id }
      });
    },
    handleSend(arr) {
      let message = "是否完成反馈单?";
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].leaderIdea === undefined || arr[i].leaderIdea === null) {
          message = "存在未经领导审批反馈单,是否完成反馈单?";
        }
      }
      this.$showConfirmModal({
        confirmHandler: () => {
          let param =  arr.map(item => item.id);
          sendAnswerForm(param).then(() => {
            this.$emit("refreshNum");
            this.$refs.uiCardTable.changePageNumber(1);
            this.$operateSuccessMessage();
          }).catch(err => this.$messageError(err));
        },
        contentText: message,
      });
    },
    handleDelete(arr) {
      this.$Modal({
        title: "信息提醒",
        content: "确定要删除反馈单吗?",
        confirm: () => {
          let id = arr.map(item => item.id);
          delAnswerForm(id).then(() => {
            this.$emit("refreshNum");
            this.$refs.uiCardTable.changePageNumber(1);
            this.$operateSuccessMessage();
          }).catch(err => this.$messageError(err));
        },
      });
    },
    operate(param) {
      param.unitId = this.userInfo.prisonId;
      param.unitName =  this.userInfo.prisonName;
      param.status = "0";
      param.userId = "";
      this.resetSelect();
      return  answerSupervisePage(param);
    },
    trainsFromData(data) {
      return fileTranslate(data);
    },
  },
};
</script>
<style lang="less">
  @import "../../assets/styles/component/tableCard.less";
</style>
