<template>
    <div class="home-com">
        <p class="home-title" style="display: flex;justify-content: space-between;padding: 15px 16px;">
            <span>重点人员</span>       
            <ul class="tabList">
                <li :class="['tabTag', { active: item.check }]" v-for="(item, i) in sxDate" :key="i" @click="changeTab(item)">{{ item.title }}</li>
            </ul>
        </p>
        <div class="zyqk-wrap" >
            <div v-for="(item,index) in personArr" :key="index" class="personchild" >
                <p class="flex-personArr"><img :src="item.imgUrl" style="width:24px"  /><span>{{ item.title }}</span></p>
                <span class="num">{{ curTitle=='本月'?item.month_count:item.year_count }}</span>
            </div>
        </div>
        <div class="person-pie" style="height: 83%;">
             <div class="person-pie-left w50">
                <p>风险人员分布情况</p>
                <keyPersonnel :itemData="riskData" :curTitle="curTitle" />
             </div>
             <div class="person-pie-right w50">
                <p>重刑人员分布情况</p>
                <zxPerson :curTitle="curTitle" :itemData="zxData" />
             </div>

        </div>
        <Spin size="large" fix v-if="spinShow"></Spin>

    </div>
</template>

<script>
import { getUserCache, formatDateparseTime } from '@/libs/util'
import keyPersonnel from './keyPersonnel.vue'
import zxPerson from './zxPerson.vue'
export default {  
    components:{keyPersonnel,zxPerson}, 
   data(){
    return{
      	orgType:'01',  // localStorage.getItem('orgType'),
        appCode:serverConfig.APP_CODE,
        curTotal:0,
        curTotalCs:0,
        curN:0,
        curL:0,
        ljjy:0,
        curcs:0,
        nPercent:0,
        sxDate: [
        { title: '本月', check: true },
        { title: '本年', check: false }
      ],
      orgType: localStorage.getItem('orgType'),
      personArr:[],
      js:[
        {title:'重点关注',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/9.png'),modelId:this.globalAppCode+':sy-zdry-zdgzry',type:'all'},
        {title:'戒具使用',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/10.png'),modelId:this.globalAppCode+':sy-zdry-jjsyry',type:'all'},
        {title:'重病号',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/11.png'),modelId:this.globalAppCode+':sy-zdry-zbhry',type:'all'},
        {title:'禁闭',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/12.png'),modelId:this.globalAppCode+':sy-zdry-jbry',type:'all'},
        {title:'单独关押',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/14.png'),modelId:this.globalAppCode+':sy-zdry-ddgyry',type:'all'},
        {title:'外籍人员',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/15.png'),modelId:this.globalAppCode+':sy-zdry-wjry',type:'all'},
        {title:'高风险',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/13.png'),modelId:this.globalAppCode+':sy-zdry-gfxry',type:'01'},

      ],
      ks:[
        {title:'重点关注',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/9.png'),modelId:this.globalAppCode+':sy-zdry-zdgzry',type:'all'},
        {title:'戒具使用',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/10.png'),modelId:this.globalAppCode+':sy-zdry-jjsyry',type:'all'},
        {title:'重病号',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/11.png'),modelId:this.globalAppCode+':sy-zdry-zbhry',type:'all'},
        {title:'禁闭',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/12.png'),modelId:this.globalAppCode+':sy-zdry-jbry',type:'all'},
        {title:'艾滋病',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/13.png'),modelId:this.globalAppCode+':sy-zdry-azbry',type:'02'},
        {title:'单独关押',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/14.png'),modelId:this.globalAppCode+':sy-zdry-ddgyry',type:'all'},
        {title:'外籍人员',month_count:0,year_count:0,imgUrl:require('@/assets/homeSite/15.png'),modelId:this.globalAppCode+':sy-zdry-wjry',type:'all'},
      ],
      dataArr:[],
      dataAll:[],
      spinShow:false,
      yyDicList:[],
      curTitle:'本月',
      riskData:[],
      zxData:[]
    }
   },
   mounted(){
    if(this.orgType && this.orgType=='01'){
      this.personArr=this.ks
    }else{
      this.personArr=this.js
    }
    this.personArr.forEach((item,index)=>{
      this.getData (item.modelId,index)
    })
    this.getData (this.globalAppCode+':sy-zdry-fxryfbqk',)
    this.getData (this.globalAppCode+':sy-zdry-zxry',)
   },
   methods:{
     
    changeTab(item) {
      this.$set(this,'curTitle',item.title)
      this.sxDate.forEach(val => {
        this.$set(val, 'check', false)
      })
      this.$set(item, 'check', true)
      this.getData()
    },

    getData(mark,index) {
        this.spinShow=true
        let params={
          modelId: mark
        }
          this.$store.dispatch("postRequest", {
            url:this.$path.get_query_grid,
            params: params,
          }).then(res => {
            if (res.success) {
                this.spinShow=false
             if(res.rows && res.rows.length>0 && index){
               Object.assign(this.personArr[index],res.rows[0])
             }else{
                if(mark==this.globalAppCode+':sy-zdry-fxryfbqk'){
                  this.riskData=res.rows
                }else{
                  this.zxData=res.rows
                }
             }
            }else{
              this.spinShow=false
            }
          })
      },
   }
}
</script>

<style lang="less" scoped>
.zyqk-wrap-pie{
    width: 100%;
    height: 50%;
    margin-top: 8px;
}
.flex-personArr{
    display: flex;
    align-items: center;
    img{
        margin-right: 6px;
    }
}
.person-pie{
    width: 100%;
    display: flex;
    margin: 16px;
    padding-top: 16px;
    .w50{
        width: 50%;
        height: 100%;
        p{

        }
    }
}
.dqzy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
    line-height: 50px;
}
.curTotal{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 24px;
    color: #2B3646;
    line-height: 30px;
}
.flex-box-cs{
    display: flex;
    justify-content: space-between;
}
.curNum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 20px;
    color: #2B3346;
    line-height: 28px;
}
/deep/ .ivu-progress-success-bg{
    background:  linear-gradient( 90deg, #5284E1 0%, #97B9E8 100%) !important;
}
.ljjy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #2B3346;
}
.tabList {
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #CFD9F0;
    display: flex !important;
    justify-content: space-between;
    display: inline-block;
    // height: 24px;
    .tabTag {
      cursor: pointer;
      width:44px;
      background: #fafafa;
      text-align: center;
      display: inline-block;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #4c6a99;
    //   line-height: 28px;
    }
    .active {
      background: #538ef9 !important;
      color: #fff !important;
    }
  }
  .yy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #2B3646;
    // line-height: 40px;
  }
  .yyNum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
  }
  .zyqk-wrap{
     width: 100%;
     display: flex;
     flex-wrap: wrap;
     margin: 16px;
     height: 100px;
  }
  .personchild{
    width: 22%;
    height: 54px;
    background: linear-gradient( 195deg, #E7F6FF 0%, #B9D7FF 100%);
    border-radius: 4px 4px 4px 4px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-right: 10px;
    padding: 12px 8px;
    margin-bottom: 8px;
    .num{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #2B3646;
    }
  }
</style>