<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <div class="bsp-base-form">
      <div class="bsp-base-tit" style="font-size: 24px;font-weight: bold;">
        {{ editTitle }}
      </div>
      <div class="prison-select-center">
        <div class="prison-select-center-top">
          <headerDetail :formData="formData" :jgryxm="jgryxm" :roomName="roomName"/>
        </div>
        <div class="prison-select-center-bottom">
          <div class="prison-select-center-bottom-left">
            <p class="sys-sub-title">审批信息</p>
            <div style="align-items: center;overflow: auto;height: 400px;">
              <record :formData="formData"  />
            </div>
          </div>
          <div class="prison-select-center-bottom-right">
            <div class="bb-wrap-right fm-content-info">
              <p class="sys-sub-title">审核</p>
              <Form v-if="saveType === 'approval'" ref="releaseForm" :model="approvalData" :label-width="160"
                    :label-colon="true" class="base-form-container">
                <Row>
                  <Col span="24" v-if="formData.approverStatus === '01'">
                    <FormItem label="审核结果" prop="result"
                              :rules="[{ trigger: 'blur,change', message: '请选择', required: saveType === 'approval',}]"
                              style="width: 100%;">
                      <RadioGroup v-model="approvalData.result">
                        <Radio label="5" :disabled="saveType !== 'approval'">通过</Radio>
                        <Radio label="2" :disabled="saveType !== 'approval'">另行处理</Radio>
                      </RadioGroup>
                    </FormItem>
                  </Col>
                  <Col span="24" v-if="formData.status !== '01'">
                    <FormItem label="审核结果" prop="result"
                              :rules="[{ trigger: 'blur,change', message: '请选择', required: saveType === 'approval',}]"
                              style="width: 100%;">
                      <RadioGroup v-model="approvalData.result">
                        <Radio label="5" :disabled="saveType !== 'approval'">同意</Radio>
                        <Radio label="2" :disabled="saveType !== 'approval'">不通过</Radio>
                      </RadioGroup>
                    </FormItem>
                  </Col>
                </Row>
                <Row>
                  <Col span="24" v-if="formData.status === '01'">
                    <FormItem label="另行处理情况登记" style="width: 75%;"
                              :rules="[{ trigger: 'blur,change', message: '请输入另行处理情况登记',
                               required: saveType === 'approval' && approvalData.result === '2' }]">
                      <Input type="textarea" :autosize="{minRows: 3,maxRows: 3}"
                             v-model="approvalData.approvalComments" placeholder="" maxlength=""></Input>
                    </FormItem>
                  </Col>
                  <Col span="24" v-if="formData.status !== '01'">
                    <FormItem label="审批意见" style="width: 75%;"
                              :rules="[{ trigger: 'blur,change', message: '请输入审批意见',
                               required: saveType === 'approval' && approvalData.result === '2' }]">
                      <Input type="textarea" :autosize="{minRows: 3,maxRows: 3}"
                             v-model="approvalData.approvalComments" placeholder="" maxlength=""></Input>
                    </FormItem>
                  </Col>
                </Row>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回</Button>
      <Button style="margin: 0 20px" type="primary" v-if="saveType === 'approval'" :loading="loading"
              @click="handleSubmit()">保 存
      </Button>
    </div>
  </div>
</template>

<script>
import headerDetail from './headerDetail.vue'
import record from "./record.vue";
export default {
  components: {
    record,
    headerDetail
  },
  props: {
    curId: {
      default: '',
      type: String
    },
    jgrybm: {
      default: '',
      type: String
    },
    jgryxm: {
      default: '',
      type: String
    },
    saveType: {
      default: 'add',
      type: String
    },
    roomName: {
      default: '',
      type: String
    }
  },
  data() {
    return {
      editTitle: '寄信审核',
      openModal: false,
      loading: false,
      formData: {},
      approvalData: {
        approvalComments: '',
        id: this.curId
      }
    }
  },
  methods: {
    handleSubmit() {
      if (!this.approvalData.result) {
        this.$Message.error('请选择审核结果')
        return
      }
      if (this.approvalData.result === '2' && !this.approvalData.approvalComments) {
        if (this.formData.status === '01') {
          this.$Message.error('请填写另行处理情况登记')
        } else {
          this.$Message.error('请填写审批意见')
        }
        return
      }
      this.loading = true
      this.$store.dispatch("authPostRequest", {
        url: this.$path.familyContact_send_approval,
        params: this.approvalData
      }).then(res => {
        if (res.success) {
          this.loading = false
          this.handleClose()
          this.$Message.success('操作成功')
        } else {
          this.$Message.error(res.msg || '保存失败！')
          this.loading = false
        }
      })
    },
    handleClose() {
      this.$emit('close', false)
    },
    getData() {
      this.$store.dispatch("authGetRequest", {
        url: this.$path.familyContact_send_get,
        params: {
          id: this.curId
        }
      }).then(res => {
        if (res.success) {
          this.formData = res.data
          this.formData.jgryxm = this.jgryxm
          this.formData.roomName = this.roomName
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败'
          })
        }
      })
    }
  },
  mounted() {
    this.getData()
  }
}
</script>

<style scoped lang="less">

.prison-select-center-bottom-left {
  width: 40%;
  background: #fff;
  height: 100%;
}

.prison-select-center-bottom-right {
  width: 60%;
  background: #fff;
  margin-left: 5px;
  height: 100%;
}

.prison-select-center {
  background-color: #e9eef5;
  border-radius: 6px;
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.prison-select-center-top {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
  display: flex;
  padding: 5px;
}

.prison-select-center-bottom {
  display: flex;
  flex: 1;
  min-height: 0;
  height: 100%;
}
</style>
