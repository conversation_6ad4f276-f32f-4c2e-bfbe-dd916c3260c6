<template>
  <div class="safety-checks-container">
    <div class="table-container" v-if="tableContainer">
      <s-DataGrid ref="grid" funcMark="yrshzdswgllb" :customFunc="true" :params="params">
        <template slot="customHeadFunc" slot-scope="{ func }">
          <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':yrshzdswgllb:add')"
            @click.native="handleAddSafetyCheck('add')">新增</Button>
        </template>
    
        <template slot="customRowFunc" slot-scope="{ func, row, index }">
          <Button type="text" style="color: #2B5FD9;" v-if="func.includes(globalAppCode + ':yrshzdswgllb:edit')"
            @click.native="handleDispose(index, row)">编辑</Button>
          <Button type="text" style="color: #2B5FD9;" v-if="func.includes(globalAppCode + ':yrshzdswgllb:delete')"
            @click.native="handleInstructions(index, row)">删除</Button>
        </template>
      </s-DataGrid>
    </div>
    <Modal v-model="modalVisible" title="添加事务选项">
      <!-- 表单内容 -->
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80">
        <FormItem label="监室事务" prop="eventName">
          <Input v-model="formValidate.eventName" placeholder="监室事务名称"></Input>
        </FormItem>
        <FormItem label="状态" prop="eventStatus">
          <Select v-model="formValidate.eventStatus" placeholder="启用状态">
            <Option :value="1">启用</Option>
            <Option :value="0">不启用</Option>
          </Select>
        </FormItem>
      </Form>
      <!-- 自定义底部按钮（关键） -->
      <template #footer>
        <Button type="default" @click="handleReset('formValidate')">取消</Button>
        <Button type="primary" @click="handleSubmit('formValidate')">确认</Button>
      </template>
    </Modal>
  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
export default {
  name: "checkconfiguration",
  data() {
    return {
      tableContainer: true,
      modalVisible: false,
      params: {},
      formValidate: {
        eventName: "",
        eventStatus: 0,
        id: ''
      },
      isEnabledVoice: 0,
      ruleValidate: {
        eventName: [
          { required: true, message: '监室事务名称不能为空', trigger: 'blur' }
        ],
        eventStatus: [
          { required: true, type: "number", message: '请选择启用状态', trigger: 'change' }
        ],
      },
      id: "",

    }
  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleAddSafetyCheck() {
      this.modalVisible = true
    },
    change(row) {
      console.error(row);
      
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let url = ""
          let msg = ""
          if(this.id) {
            url =  this.$path.lifeDictEvent_update
            msg = "监室事务更新成功"
            this.formValidate.id = this.id
          } else {
            url =  this.$path.lifeDictEvent_create
            msg = "监室事务创建成功"
            this.formValidate.id  = ""
          }
          this.authPostRequest({ url: url, params: this.formValidate }).then(res => {
            if(res.success) {
              this.modalVisible = false
              this.$refs[name].resetFields();
              this.$Message.success(msg);
              this.on_refresh_table()
            }
          })
        } else {
          this.$Message.error('验证失败');
          return;
        }
      })
    },
    handleInstructions(index, row) {
      this.$Modal.confirm({
			  title: '温馨提示',
			  content: '请确认是否删除？',
			  onOk: () => {
				  this.$store.dispatch('authGetRequest',{
					  url: this.$path.lifeDictEvent_delete,
					  params: {
						  ids: row.id
					  }
				  }).then(res => {
					  if(res.success) {
						  this.on_refresh_table()
					  }
				  })
			  }
		  })
    },
    handleReset(name) {
      this.$refs[name].resetFields();
      this.modalVisible = false
    },
    handleDispose(index, { id }) {
      this.authGetRequest({ url: this.$path.lifeDictEvent_get, params: { id } }).then(res => {
        if(res.success) {
          this.id = res.data.id
          this.formValidate = res.data;
          this.modalVisible = true
        }
      })
    },
    on_refresh_table () {
      this.$refs.grid.query_grid_data(1)
    }
  },

  components: {
    sDataGrid,
  },

  created() {
  },

  computed: {},
  watch: {
  },

}

</script>

<style scoped lang="less"></style>
