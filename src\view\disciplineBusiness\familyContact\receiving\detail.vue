<template>
  <!-- 详情页面 -->
  <div class="detail-wrap">
    <div class="bsp-base-form">
      <div class="bsp-base-tit">
        {{ editTitle }}
      </div>
      <div class="prison-select-center">
        <!-- 三列布局：人员信息 + 基本信息 + 流程轨迹 -->
        <div class="three-column-layout">
          <!-- 左列：被监管人员信息 -->
          <div class="left-column">
            <div class="personnel-card">
              <h3 class="personnel-card-title">
                <i class="personnel-card-icon">👤</i>
                被监管人员
              </h3>
              <div class="personnel-card-content">
                <personnel-selector
                  :value="jgrybm"
                  mode="detail"
                  title="被监管人员"
                  :show-case-info="true"
                />
              </div>
            </div>
          </div>

          <!-- 中列：基本信息 -->
          <div class="middle-column">
            <headerDetail :formData="formData" :jgryxm="jgryxm" :roomName="roomName"/>
          </div>

          <!-- 右列：流程轨迹 -->
          <div class="right-column">
            <div class="timeline-container">
              <h3 class="timeline-title">
                <i class="timeline-title-icon">📋</i>
                流程轨迹
              </h3>
              <div class="timeline-wrapper">
                <record :formData="formData" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回</Button>
    </div>
  </div>
</template>

<script>

import viewImg from "_c/main/components/viewImg/viewImg.vue"
import personnelSelector from "@/components/personnel-selector"
import headerDetail from "./headerDetail.vue"
import record from "./record.vue"
export default {
  props: {
    jgryxm: {
      default: '',
      type: String
    },
    jgrybm: {
      default: '',
      type: String
    },
    roomName: {
      default: '',
      type: String
    },
    curId: {
      default: '',
      type: String
    }
  },
  components: {headerDetail, viewImg, record, personnelSelector},
  data() {
    return {
      showattUrl: true,
      formData: {},
      defaultListsuper: [],
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      editTitle: '收信信息'
    }
  },
  methods: {
    handleClose() {
      this.$emit('close', false)
    },
    getData() {
      this.$store.dispatch("authGetRequest", {
        url: this.$path.familyContact_get,
        params: {
          id: this.curId
        }
      }).then(res => {
        if (res.success) {
          this.formData = res.data
          this.formData.jgryxm = this.jgryxm
          this.formData.roomName = this.roomName
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败'
          })
        }
      })
    }
  },
  mounted() {
    this.getData()
  }
}
</script>
<style scoped lang="less">
@import '~@/assets/style/variables.less';

/* 重置 bsp-base-form 的绝对定位，避免布局问题 */
.detail-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;

  .bsp-base-form {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .bsp-base-tit {
    flex-shrink: 0;
  }

  .bsp-base-fotter {
    position: relative !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    flex-shrink: 0;
    margin-top: auto;
  }
}

.prison-select-center {
  background-color: @background-color-light;
  border-radius: @border-radius-lg;
  width: 100%;
  padding: @padding-md;
  flex: 1;
  overflow: hidden;
}

/* 三列布局 */
.three-column-layout {
  display: flex;
  gap: @margin-sm;
  height: 100%;
  width: 100%;
  box-sizing: border-box;

  .left-column {
    width: 35%;
    flex-shrink: 0;
    min-width: 380px; /* 确保人员组件有足够的最小宽度 */
  }

  .middle-column {
    width: 32%;
    flex-shrink: 0;
  }

  .right-column {
    width: 33%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    min-width: 320px; /* 确保流程轨迹有足够的最小宽度 */
    max-width: 33%; /* 防止溢出 */
  }
}

/* 人员卡片样式 */
.personnel-card {
  background: @background-color-base;
  border-radius: @border-radius-lg;
  box-shadow: @box-shadow-light;
  border: 1px solid @border-color-light;
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    box-shadow: @box-shadow-medium;
    transform: translateY(-1px);
  }

  .personnel-card-title {
    background: linear-gradient(135deg, @primary-color 0%, @primary-color-hover 100%);
    color: @background-color-base;
    margin: 0;
    padding: 12px @padding-md;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    height: 44px; /* 匹配人员组件标题高度 */
    box-sizing: border-box;

    .personnel-card-icon {
      margin-right: 12px;
      font-size: @font-size-md;
    }
  }

  .personnel-card-content {
    padding: @padding-md;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止内容溢出 */

    /* 人员选择器组件适配 */
    /deep/ .personnel-selector {
      width: 100%;
      max-width: 100%;

      .personnel-header {
        .section-title h4 {
          display: none; /* 隐藏重复的标题 */
        }
      }
    }
  }
}

/* 流程轨迹容器样式 */
.timeline-container {
  background: @background-color-base;
  border-radius: @border-radius-lg;
  box-shadow: @box-shadow-light;
  border: 1px solid @border-color-light;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;

  &:hover {
    box-shadow: @box-shadow-medium;
  }

  .timeline-title {
    background: linear-gradient(135deg, @primary-color 0%, @primary-color-hover 100%);
    color: @background-color-base;
    margin: 0;
    padding: 12px @padding-lg;
    font-size: 14px;
    font-weight: 600;
    border-radius: @border-radius-lg @border-radius-lg 0 0;
    display: flex;
    align-items: center;
    position: relative;
    height: 44px; /* 匹配人员组件标题高度 */
    box-sizing: border-box;

    .timeline-title-icon {
      margin-right: 12px;
      font-size: @font-size-lg;
    }

    &::after {
      content: '';
      position: absolute;
      left: @padding-lg;
      bottom: -2px;
      width: 40px;
      height: 2px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 1px;
    }
  }

  .timeline-wrapper {
    padding: @padding-md;
    flex: 1;
    overflow-y: auto;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f8f9fa;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #dee2e6;
      border-radius: 3px;

      &:hover {
        background: #adb5bd;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: @screen-lg) {
  .three-column-layout {
    gap: @margin-sm;

    .left-column {
      width: 34%;
      min-width: 350px;
    }

    .middle-column {
      width: 33%;
    }

    .right-column {
      width: 33%;
      min-width: 280px;
    }
  }
}

@media (max-width: @screen-md) {
  .three-column-layout {
    flex-direction: column;

    .left-column,
    .middle-column,
    .right-column {
      width: 100%;
      min-width: auto;
    }

    .left-column {
      order: 1;
    }

    .middle-column {
      order: 2;
    }

    .right-column {
      order: 3;
      max-height: 400px;
    }
  }

  .personnel-card-content {
    /deep/ .personnel-selector {
      .unified-cards-container {
        .personnel-card,
        .case-card {
          min-width: auto;
        }
      }
    }
  }
}

@media (max-width: @screen-sm) {
  .prison-select-center {
    padding: @padding-sm;
  }

  .three-column-layout {
    gap: @margin-sm;
  }
}
</style>
