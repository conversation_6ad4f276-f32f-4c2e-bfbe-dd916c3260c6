<!-- 事件档案 -->
<template>
  <div style="height: 100%">
    <div
      style="
        width: 100%;
        height: 50px;
        padding: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #e0edf6;
      "
    >
      <p style="font-weight: 600">{{ formName }}</p>
      <div>
        <Button @click="close">取消</Button>
        <Button
          type="primary"
          @click="printPage(formId)"
          style="margin-left: 10px"
          >打印</Button
        >
      </div>
    </div>
    <div class="modal-height">
      <div class="archive-wrapper">
        <report style="height: 100%" :parameter="parameter" ref="report" />
      </div>
    </div>
    <Spin size="large" fix style="z-index: 10" v-if="pldySpin"></Spin>
  </div>
</template>

<script>
// import headerWrap from "@/components/main/header.vue"
import report from "@/components/wsdyyl/printContent/report.vue";
import Vue from "vue";
import print from "print-js";
import vueToPdf from "vue-to-pdf";
Vue.use(vueToPdf);
import { getToken } from "@/libs/util";
export default {
  name: "mngArchives",
  components: {
    report,
  },
  props: {
    dicName: String,
    formId: String,
    businessId: String,
    formName: String,
  },
  data() {
    return {
      components: "report",
      pldySpin: false,
      curTab: this.$route.query.formId
        ? this.$route.query.formId
        : "1911795700199460864", // "reportGYEL",
      modal: false,
      appCode: serverConfig.APP_CODE,
      parameter: {
        businessId: this.$route.query.id ? this.$route.query.id : "",
        formId: this.$route.query.formId
          ? this.$route.query.formId
          : "1911795700199460864", //'xxx',
      },
      tabList: [],
      keyword: "",
      dicCode: this.$route.query.dicCode ? this.$route.query.dicCode : "",
    };
  },
  computed: {
    loading() {
      return false; // !this.tabList.every((item) => item.finish);
    },
    filterTabList() {
      return this.tabList.filter((item) => item.name.includes(this.keyword));
    },
  },
  mounted() {
    if (this.dicName) {
      this.$set(this, "dicCode", this.dicName);
    }
    if (this.formId) {
      this.$set(this, "curTab", this.formId);
      this.$set(this.parameter, "formId", this.formId);
    }
    if (this.businessId) {
      this.$set(this.parameter, "businessId", this.businessId);
    }
    this.dicNameMethod(this.dicCode, this.appCode);
  },
  methods: {
    // 呈请环节翻译 ZD_ICP_SGGD
    dicNameMethod(dicName, appCode) {
      let name = [];
      return new Promise((resolve, reject) => {
        this.$store
          .dispatch("axiosGetRequest", {
            url: "/bsp-com/static/dic/" + appCode + "/" + `${dicName}` + ".js",
          })
          .then((res) => {
            if (res.status === 200) {
              let arr = [];
              let func = { getData: eval("(" + res.data + ")") };
              arr = func.getData();
              this.tabList = arr;
              console.log(arr, "arr");
              resolve(arr);
            } else {
              // this.loading = false
              // this.errorModal({ title: '温馨提示', content: res.msg })
            }
          });
      });
    },
    open(info) {
      this.info = info;
      this.modal = true;
    },
    toPage(item) {
      // if (!item.finish) return;
      // this.$refs[item.code].$el.scrollIntoView();
      this.curTab = item.code;
      this.$set(this.parameter, "formId", item.code);
    },
    onScroll(e) {
      const { scrollTop } = e.target;
      let height = 0;
      for (let i = 0; i < this.tabList.length; i++) {
        const name = this.tabList[i].name;
        const clientHeight = this.$refs[name].$el.clientHeight;
        if (scrollTop >= height + clientHeight) {
          height += clientHeight;
        } else {
          this.curTab = name;
          break;
        }
      }
    },
    // 同时多个请求会超出限制，队列加载
    nextLoading(index) {
      this.$set(this.tabList[index], "finish", true);
      const nextLoadIndex = this.tabList.findIndex((item) => !item.load);
      if (nextLoadIndex === -1) return;
      this.$set(this.tabList[nextLoadIndex], "load", true);
    },
    printPage(formId) {
      let _this = this;
      _this.pldySpin = true;
      let iframeUrl =
        `${
          this.$path.pdf_getPdf
        }?plugIns=MultipleRowTableRenderPolicy&formId=${formId}&businessId=${
          this.parameter.businessId
        }&access_token=${getToken()}` + "#toolbar=0";
      printJS({
        printable: iframeUrl,
        type: "pdf",
        header: "打印",
        onLoadingEnd: async () => {
          _this.pldySpin = false;
        },
      });
    },
    close() {
      this.$router.go(-1);
    },
    goBack() {
      this.$emit("goBack");
    },
  },
};
</script>
<style lang="less" scoped>
.modal-cls {
  /deep/.ivu-modal {
    top: 50px;
  }
}
.archive-wrapper {
  height: 100%;
  width: 100%;
  overflow: hidden;
  padding-bottom: 16px;
  display: flex;
  .archive-catalogue {
    flex: none;
    width: 350px;
    background: #fff;
    padding: 10px;
    height: 100%;
    .catalogue-header {
      font-weight: bold;
      font-size: 18px;
      color: #415060;
      line-height: 40px;
      margin-bottom: 10px;
      padding-left: 6px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .catalogue-input {
      margin-bottom: 10px;
    }
    .catalogue-content {
      height: ~"calc(100% - 128px)";
      overflow: auto !important;
      p {
        width: 100%;
        padding: 0 16px;
        line-height: 40px;
        border-radius: 4px;
        color: #272c2c;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &.active {
          color: #2b5fda; // #00afb8;
          background: #cfe2fa; // #e5f7f7;
        }
        &.no-click {
          cursor: not-allowed;
        }
        &:not(.no-click):hover {
          // background: #e5f7f7;
          background: #cfe2fa;
        }
      }
    }
    .catalogue-close {
      display: flex;
      justify-content: center;
    }
  }
  .archive-content {
    flex: 1;
    position: relative;
    background: #f5f7fa;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
    border-radius: 4px;
    overflow: auto;
    .archive-paper-wrapper {
      margin-bottom: 16px;
      // padding: 40px;
      background: #ffffff;
      border-radius: 4px;
      position: relative;
    }
  }
}
.print-content {
  position: fixed;
  right: 9999px;
}
.modal-height {
  // height: ~"calc(100vh - 112px)";
  height: 100%;
  width: 100%;
  overflow: auto;
  display: flex;
  padding: 10px;
  background: #eee;
  .left {
    width: 250px;
    padding: 10px;
    background: #fff;
    border-right: 1px solid #eee;
  }
  .right {
    flex: 1;
    padding: 10px;
    background: #fff;
  }
}
</style>
