<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <div class="fm-content-info bsp-base-content" style="top:30px !important;padding:unset !important;">
      <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="150" label-colon
        style="padding: 0 .625rem;">
        <div class="fm-content-box">
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />基本信息
          </p>
          <Row>
            <Col span="3" class="col-title"><span>监室号</span></Col>
            <Col span="5"><span>{{ combineInfoData.roomName }}</span></Col>
            <Col span="3" class="col-title"><span>姓名</span></Col>
            <Col span="5"><span>{{ combineInfoData.xm }}</span></Col>
            <Col span="3" class="col-title"><span>曾用名/别名/绰号</span></Col>
            <Col span="5"><span>{{ combineInfoData.bm }}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>性别</span></Col>
            <Col span="5"><span>{{ combineInfoData.xbName }}</span></Col>
            <Col span="3" class="col-title"><span>出生日期</span></Col>
            <Col span="5"><span>{{ combineInfoData.csrq }}</span></Col>
            <Col span="3" class="col-title"><span>涉嫌罪名</span></Col>
            <Col span="5"><span>{{ combineInfoData.sxzm }}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>收押登记时间</span></Col>
            <Col span="5"><span>{{ rowData.add_time }}</span></Col>
            <Col span="0" class="col-title"><span></span></Col>
            <Col span="8"> <Button style="margin: 0 13px 0 16px" type="primary"
              @click.native="showBaseInfoDialog()">查看入所登记详情</Button></Col>
          </Row>
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />健康检查结论
          </p>
          <Row>
            <Col span="3" class="col-title"><span>医生意见</span></Col>
            <Col span="5"><span>{{  scalesList.find(item => item.code == combineInfoData.ysyj).name }}</span></Col>
            <Col span="3" class="col-title"><span>检查人</span></Col>
            <Col span="5"><span>{{ combineInfoData.jcr }}</span></Col>
            <Col span="3" class="col-title"><span>检查时间</span></Col>
            <Col span="5"><span>{{ combineInfoData.jcsj }}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>备注</span></Col>
            <Col span="21"><span>{{ combineInfoData.bz }}</span></Col>
          </Row>

        </div>

        <div class="bsp-base-subtit" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda" />领导审批结论
          </p>
          <div class="form">
            <approvalInfo :actInstId="this.rowData.act_inst_id"></approvalInfo>
          </div>

          <!--          <div class="form">-->
          <!--            <Row>-->
          <!--              <Col span="16">-->
          <!--                <FormItem label="审批结果"  prop="spzt" :rules="[{ trigger: 'blur,change', message: '审批结果为必填', required: true }]">-->
          <!--                  <s-dicgrid v-model="formData.spzt" @change="$refs.formData.validateField('spzt')" :isSearch="true"  dicName="ZD_SLDSPZT" />-->
          <!--                </FormItem>-->
          <!--              </Col>-->
          <!--            </Row>-->
          <!--            <Row>-->
          <!--              <Col span="16">-->
          <!--                <FormItem label="审批意见" prop="approvalResult" :rules="[{ trigger: 'blur', message: '审批意见为必填', required: true }]">-->
          <!--                  <Input v-model="formData.approvalResult" placeholder="请填写"  type="textarea" :autosize="{minRows: 2,maxRows: 5}"></Input>-->
          <!--                </FormItem>-->
          <!--              </Col>-->
          <!--            </Row>-->
          <!--            <Row>-->
          <!--              <Col span="8">-->
          <!--                <FormItem label="经办人" prop="jbr" :rules="[{ trigger: 'blur', message: '经办人为必填', required: true }]">-->
          <!--                  <Input type="text" v-model="formData.jbr" placeholder="系统自动获取登录人信息"/>-->
          <!--                </FormItem>-->
          <!--              </Col>-->
          <!--              <Col span="8">-->
          <!--                <FormItem label="经办时间" prop="jbsj">-->
          <!--                  <div class="ivu-form-item-label">{{formData.jbsj}}</div>-->
          <!--                </FormItem>-->
          <!--              </Col>-->

          <!--            </Row>-->
          <!--          </div>-->
        </div>
      </Form>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
      <Button style="margin: 0 20px" type="primary" :loading="loading" @click="handleApproval()">审批</Button>
    </div>

    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-sy-modal" width="80%"
      title="入所登记详情">
      <div style="height: 75vh;overflow: auto;">
        <div class="fm-content-info bsp-base-content" style="padding:0px !important;">
          <baseInfoDetail :rowData="rowData" :inDialog="true"></baseInfoDetail>
        </div>
      </div>
    </Modal>


    <s-general-audit v-if="this.rowData.act_inst_id && showAudit" :key="timer" ref="approvalAudit"
      @audit-close="audit_close" :showFileUpload="false" :beforeOpen="beforeOpen" :actInstId="this.rowData.act_inst_id"
      :showcc="false" :businessId="getBussinId" :module="module" :extraOrgId="extraOrgId" :extraRegId="extraRegId"
      extraCityId="" :selectUsers="selectUsers" :modalWidth="modalWidth" :auditComplete="approvalSuccess"
      approvalContent="">
    </s-general-audit>
  </div>
</template>

<script>
import approvalInfo from "./approvalInfo";
import { mapActions } from "vuex";
import { getUserCache } from '@/libs/util'
import baseInfoDetail from '../../treatmentEnterManage/recordManage/detail'
import { sGeneralAudit } from 'gxx-general-audit'

import { startApproval } from 'gs-start-approval'
export default {
  components: {
    baseInfoDetail, startApproval, approvalInfo, sGeneralAudit
  },
  props: {
    rowData: {
      type: [Array, Object],
      default: {}
    },
    entireProcess: {
      default: false,
    }
  },
  data() {
    return {
      baseInfoData: {

      },
      combineInfoData: {

      },
      formData: {
      },
      loading: false,
      ruleValidate: {},
      openModal: false,
      timer: '',
      actInstId: "",
      getBussinId: '',
      showAudit: false,
      module: serverConfig.APP_MARK,
      selectUsers: '',
      extraOrgId: this.$store.state.common.orgCode,
      extraRegId: this.$store.state.common.regCode,
      modalWidth: '600',
      scalesList:[]
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    getCurrentTimeFormatted() {
      const now = new Date();

      const year = now.getFullYear();             // 获取年份
      const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，要加1，并补零
      const day = String(now.getDate()).padStart(2, '0');        // 日期补零

      const hours = String(now.getHours()).padStart(2, '0');     // 小时补零
      const minutes = String(now.getMinutes()).padStart(2, '0'); // 分钟补零
      const seconds = String(now.getSeconds()).padStart(2, '0'); // 秒数补零

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleClose() {
      this.$router.replace({
        query: {
          ...this.$route.query,
          eventCode: undefined  // 设置为 undefined 即可删除该参数
        }
      });
      this.$emit('close', false)
    },
    handleNext() {
      this.$emit('nextStep', false)
    },
    getBaseInfoDetail(id) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_detainRegKssGet,
        params: {
          id: id
        }
      }).then(resp => {
        if (resp.code == 0) {
          this.baseInfoData = resp.data
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    showBaseInfoDialog() {
      this.openModal = true;
    },
    getCombineInfoDetail(rybh) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_getCombineInfo,
        params: {
          rybh: rybh
        }
      }).then(resp => {
        if (resp.code == 0) {
          if (resp.data) {
            this.combineInfoData = resp.data
          }
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    // handleSubmit(){
    //   this.$refs['formData'].validate((valid) => {
    //     if (valid) {
    //       this.saveData()
    //     } else {
    //       this.loading=false
    //       this.$Message.error('请填写完整!!');
    //     }
    //   })
    // },
    handleApproval() {
      this.openAuidt()
    },
    openAuidt() {
      this.timer = new Date().getTime()
      this.showAudit = true
      this.getBussinId = this.rowData.rybh
      setTimeout(() => {
        this.$refs['approvalAudit'].openAudit()
      }, 500)
    },
    audit_close() {
      this.$refs.approvalAudit.isOpen = false
      this.getBussinId = ''
      this.actInstId = ''
      this.showAudit = false
      // 重新加载子组件
      this.timer = new Date().getTime()
    },
    beforeOpen() {
      return new Promise((resolve, reject) => {

        resolve(true)
      })
    },
    approvalSuccess(data) {
      console.log(data, "流程-----")
      this.saveData(data.data.bpmTrail)
    },

    saveData(data) {
      this.formData.dataSources ? '' : this.$set(this.formData, 'dataSources', 0)
      let params = this.formData
      params.id = this.rowData.id
      params.rybh = this.rowData.rybh
      params.approvalResult = data.approvalContent
      if (data.isApprove == "1") {
        params.spzt = "2"
      } else {
        params.spzt = "3"
      }
      // params.spzt = data.isApprove
      this.loading = true
      console.log("开始提交")
      console.log(this.formData, 'params----')
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_updateLeaderApprovalStatus,
        params: params
      }).then(resp => {
        this.loading = false
        if (resp.code == 0) {
          this.updateStepInfo(params.spzt)
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    updateStepInfo(spzt) {
      let params = {
        currentStep: this.rowData.current_step,
        actInstId: this.rowData.act_inst_id,
        prison: 'ek',
        rybh: this.rowData.rybh,
        spzt: spzt,
        status: "03"
      }
      //调用接口
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_detainRegKssUpdateStepInfo,
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.$Message.success('提交成功!');
          this.handleClose();
        } else {
          this.$Message.error(resp.msg);
        }
      })
    },
    handleGetZD_RSJCYSYJ() {
      this.$store
        .dispatch("authGetRequest", {
          url: "/bsp-com/static/dic/acp/ZD_RSJCYSYJ.js",
        })
        .then((res) => {
          let scales = eval("(" + res + ")");
          this.scalesList = scales();
        });
    },
  },
  mounted() {
    this.actInstId = this.rowData.act_inst_id
    this.formData.jbsj = this.getCurrentTimeFormatted();
    this.$set(this.formData, 'jbr', getUserCache.getUserName())
    this.getCombineInfoDetail(this.rowData.rybh)
    this.handleGetZD_RSJCYSYJ()
  }
}
</script>

<style scoped>
@import "~@/assets/style/formInfo.css";
</style>
