<template>
  <div>
    <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="150" label-colon>
      <!-- <div class="subtit"><Icon type="md-list-box" size="24" color="#2b5fda" />原押所送押信息</div>
      <div class="form">
        <Row>
          <Col span="8">
            <FormItem label="送押单位(原押所)" prop="yysSydw" >
              <Input type="text" v-model="formData.yysSydw" placeholder="请填写"/>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="送押民警(原押所)" prop="yysSymj" >
              <Input type="text" v-model="formData.yysSymj" placeholder="请填写"/>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="送押民警联系电话(原押所)" prop="yysSymjlxdh" >
              <Input type="text" v-model="formData.yysSymjlxdh" placeholder="请填写"/>
            </FormItem>
          </Col>
        </Row>
      </div> -->
      <div class="subtit"><Icon type="md-list-box" size="24" color="#2b5fda" />收治信息</div>
      <div class="form">
        <Row>
          <Col span="8">
            <FormItem v-if="!quickRegistration" label="是否佩戴眼镜" :label-width="150" prop="sfpdyj" :rules="[{ trigger: 'blur,change', message: '请选择是否佩戴眼镜', required: true }]">
              <RadioGroup v-model="formData.sfpdyj">
                <Radio label="1">是</Radio>
                <Radio label="0">否</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem v-else label="带入病室人" prop="drjsr" >
              <Input type="text" v-model="formData.drjsr" placeholder="请填写"/>
            </FormItem>
          </Col>
          <Col span="8">
            <!-- :rules="[{ trigger: 'blur,change', message: '病区号为必填', required: true }]" -->
            <FormItem  v-if="!quickRegistration" label="病区" prop="areaId">
              <Select v-model="formData.areaId" @on-change="areaChange">
                <Option v-for="item in areaCodeList" :value="item.areaId" :key="item.areaId">{{ item.areaName }}</Option>
              </Select>
            </FormItem>
            <FormItem v-else label="带入病室时间" prop="drjssj">
              <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="$refs.formData.validateField('drjssj')"  v-model="formData.drjssj" size="small"  placeholder="请选择" style="width: 100%;" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="病室号" prop="jsh" >
              <Select v-model="formData.jsh" @on-change="orgChange">
                <Option v-for="item in orgCodeList" :value="item.roomCode" :key="item.roomCode">{{ item.roomName }}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <FormItem label="分配床位" prop="cwh" >
              <Input type="text" v-model="formData.cwh" placeholder="请填写"/>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="分配识别服" prop="sfpbsf" >
              <Select v-model="formData.sfpbsf">
                <Option v-for="item in suitNumList" :value="item" :key="item">{{ item }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="识别服颜色" prop="suitColor" >
              <s-dicgrid v-model="formData.suitColor" @change="$refs.formData.validateField('suitColor')" :isSearch="true"  dicName="ZD_SBFBS" />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8" v-if="!quickRegistration">
            <FormItem label="带入病室人" prop="drjsr" >
              <Input type="text" v-model="formData.drjsr" placeholder="请填写"/>
            </FormItem>
          </Col>
          <Col span="8" v-if="!quickRegistration">
            <FormItem label="带入病室时间" prop="drjssj">
              <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="$refs.formData.validateField('drjssj')"  v-model="formData.drjssj" size="small"  placeholder="请选择" style="width: 100%;" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem  v-if="!quickRegistration" label="收治民警姓名" prop="symjxm" :rules="[{ trigger: 'blur', message: '收治民警姓名为必填', required: true }]">
              <Input type="text" v-model="formData.symjxm" placeholder="请填写"/>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="16">
            <FormItem label="备注" prop="bz">
              <Input v-model="formData.bz" placeholder="请填写"  type="textarea" :autosize="{minRows: 2,maxRows: 5}"></Input>
            </FormItem>
          </Col>
        </Row>
      </div>
      <div class="subtit"><Icon type="md-list-box" size="24" color="#2b5fda" />涉密信息</div>
      <div class="form">
        <Row>
          <Col span="8">
            <FormItem label="是否涉密" :label-width="150" prop="sfsm" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '请选择是否涉密', required: true }]:[]">
              <RadioGroup v-model="formData.sfsm">
                <Radio label="1">是</Radio>
                <Radio label="0">否</Radio>
              </RadioGroup>
            </FormItem>
          </Col>
          <Col span="8" v-if="formData.sfsm=='1'">
            <FormItem label="人员代号" prop="rydh" >
              <Input type="text" v-model="formData.rydh" placeholder="请填写"/>
            </FormItem>
          </Col>
          <Col span="8" v-if="formData.sfsm=='1'">
            <FormItem label="涉密原因" prop="smyy" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '涉密原因为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.smyy" @change="$refs.formData.validateField('smyy')" :isSearch="true"  dicName="ZD_SYDJSMYY" />
            </FormItem>
          </Col>
        </Row>
        <Row v-if="formData.sfsm=='1'">
          <Col span="16">
            <FormItem label="备注" prop="smbz">
              <Input v-model="formData.smbz" placeholder="请填写"  type="textarea" :autosize="{minRows: 2,maxRows: 5}"></Input>
            </FormItem>
          </Col>
        </Row>
      </div>
      <div class="subtit"><Icon type="md-list-box" size="24" color="#2b5fda" />救济物品发放</div>
      <div class="form">
        <Row>
          <Col span="8">
            <FormItem label="救济日期" prop="jjrq" >
              <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="$refs.formData.validateField('jjrq')"  v-model="formData.jjrq" size="small"  placeholder="请选择" style="width: 100%;" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="救济原因" prop="jjyy" >
              <s-dicgrid v-model="formData.jjyy" @change="$refs.formData.validateField('jjyy')" :isSearch="true"  dicName="ZD_SYDJJJYY" />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="16">
            <FormItem label="领取物品" prop="jjlqwp">
              <Input v-model="formData.jjlqwp" placeholder="请填写"  type="textarea" :autosize="{minRows: 2,maxRows: 5}"></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <FormItem label="经办人" prop="jbr" :rules="!quickRegistration?[{ trigger: 'blur', message: '经办人为必填', required: true }]:[]">
              <Input type="text" v-model="formData.jbr" placeholder="系统自动获取登录人信息"/>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="经办时间" prop="jbsj">
              <div class="ivu-form-item-label">{{formData.jbsj}}</div>
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>

  </div>
</template>

<script>
  import {mapActions} from "vuex";
  import { getUserCache } from '@/libs/util'

  export default {
    props:{
      formData:{
        type: [Array,Object],
        default: {}
      },
      quickRegistration:{
        default: false,
      },
    },
    data(){
      return {
        orgCodeList: [],
        suitNumList:[],
        areaCodeList: [],
        ruleValidate: {},
      }
    },
    methods:{
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      getAreaByOrgCode(){
        let params = {
        }
        let orgCode = getUserCache.getOrgCode()
        params.orgCode = orgCode
        this.$store.dispatch('authGetRequest',{
          url: this.$path.app_areaGetAreaByOrgCode,
          params:params
        }).then(resp=>{
          this.loading=false
          if (resp.code==0){
            if(resp.data && resp.data && resp.data.length > 0){
              this.areaCodeList = resp.data.map(item => ({
                areaId: item.areaCode,
                areaName: item.areaName,
              }));
              if(this.areaCodeList.length > 0){
                this.formData.areaId = this.areaCodeList[0].areaId
                this.formData.areaName = this.areaCodeList[0].areaName
                this.getAreaList(this.formData.areaId)
              }
            }
          }else{
            this.$Notice.error({
              title:'错误提示',
              desc:resp.msg
            })
          }
        })
      },
      getAreaList(areaId){
        let params = {
          // sameCase:true,
          pageNo:1,
          pageSize:100,
          // roomType:'7'
        }
        if(areaId){
          params.areaId = areaId
        }
        let orgCode = getUserCache.getOrgCode()
        params.orgCode = orgCode
        this.$store.dispatch('authPostRequest',{
          url: this.$path.app_getAreaPrisonRoomPage,
          params:params
        }).then(resp=>{
          this.loading=false
          if (resp.code==0){
            if(resp.data && resp.data.list && resp.data.list.length > 0){
              this.orgCodeList  = resp.data.list.map(item => ({
                roomCode: item.roomCode,
                roomName: item.roomName
              }));
              // this.areaCodeList = resp.data.list.map(item => ({
              //   areaId: item.areaId,
              //   areaName: item.areaName,
              // }));
            }

          }else{
            this.$Notice.error({
              title:'错误提示',
              desc:resp.msg
            })
          }
        })
      },
      getSuitNumByRoomId(roomId){
        let params = {
          roomId:roomId
        }
        this.$store.dispatch('authGetRequest',{
          url: this.$path.app_getSuitNumByRoomId,
          params:params
        }).then(resp => {
          if(resp.code == 0){
            if(resp.data && resp.data.length > 0){
              this.suitNumList = resp.data
            }
          }else{
            this.$Notice.error({
              title:'错误提示',
              desc:resp.msg
            })
          }
        })
      },
      getAreaListEvent(orgCodeList){
        this.orgCodeList = orgCodeList
      },
      orgChange(data) {
        if(data) {
          this.formData.jsh = data
          let org = this.orgCodeList.find(t=>t.roomCode == data);
          this.formData.roomName = org.roomName;
          this.getSuitNumByRoomId(this.formData.jsh)
        }
      },
      areaChange(data) {
        if(data) {
          this.formData.areaId = data
          let area = this.areaCodeList.find(t=>t.areaId == data);
          this.formData.areaName = area.areaName;
          this.formData.jsh = null
          this.formData.roomName = null
          this.suitNumList = []
          this.formData.sfpbsf = ""
          this.getAreaList(this.formData.areaId)
        }
      },
      validate() {
        return new Promise((resolve) => {
          this.$refs.formData.validate(valid => {
            resolve(valid); // valid 是 boolean，表示是否验证通过
          });
        });
      }
    },
    mounted() {
      this.getAreaByOrgCode()
    }
  }
</script>

<style scoped>

</style>
