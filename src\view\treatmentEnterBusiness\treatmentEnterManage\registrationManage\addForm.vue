<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <div class="bsp-base-content" style="top:0px !important;">
      <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="150" label-colon>
        <div class="steps-box">
          <Steps :current="current">
            <Step v-for="(step, index) in steps" :key="index">
              <template #title>
                <div @click="stepClick(index)" style="cursor: pointer">{{ step.title }}</div>
              </template>
            </Step>
<!--            <Step title="基本信息"></Step>-->
<!--            <Step title="案件信息"> </Step>-->
<!--            <Step title="办案人信息"></Step>-->
<!--            <Step title="收押信息"></Step>-->
<!--            <Step title="社会关系"></Step>-->
<!--            <Step title="其他信息"></Step>-->
          </Steps>
        </div>
        <div class="bsp-base-subtit" v-show="current === 0" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
         <baseInfo :formData="formData" ref="baseInfoForm" :quickRegistration="quickRegistration" @getRecordByzjhm="getRecordByzjhm" @getAreaListEvent="getAreaListEvent"></baseInfo>
        </div>
        <div class="bsp-base-subtit" v-show="current === 1">
          <caseInfo :formData="formData" :quickRegistration="quickRegistration" ref="caseInfoForm"></caseInfo>
        </div>
        <div class="bsp-base-subtit" v-show="current === 2">
          <caseHandlerInfo :formData="formData" :quickRegistration="quickRegistration" ref="caseHandlerInfoForm"></caseHandlerInfo>
        </div>
        <div class="bsp-base-subtit" v-show="current === 3">
          <treatmentInfo :formData="formData" :quickRegistration="quickRegistration" ref="treatmentInfoForm"></treatmentInfo>
        </div>
        <div class="bsp-base-subtit" v-show="current === 4">
         <socialRelationshipInfo :formData="formData" ref="socialRelationshipInfoForm"></socialRelationshipInfo>
        </div>
        <div class="bsp-base-subtit" v-show="current === 5">
         <otherInfo :formData="formData" ref="otherInfoForm"></otherInfo>
        </div>

      </Form>
    </div>
    <div class="bsp-base-fotter" >
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
      <Button style="margin: 0 20px" :loading="loadingSave" @click="handleSubmit(false)">暂 存</Button>
      <Button style="margin: 0 20px" v-if="current!==0" type="primary" @click="prev()">上一步</Button>
      <Button style="margin: 0 20px" v-if="current!==5" type="primary" @click="next()">下一步</Button>
      <Button style="margin: 0 20px" v-if="current===5 || quickRegistration" type="primary" :loading="loading" @click="handleSubmit(true)">提交</Button>
    </div>

    <start-approval ref="approval"
                    :assigneeUserId="approvalData.assigneeUserId"
                    :assigneeUserName="approvalData.assigneeUserName"
                    :assigneeOrgId="approvalData.assigneeOrgId"
                    :assigneeOrgName="approvalData.assigneeOrgName"
                    :assigneeAreaId="approvalData.assigneeAreaId"
                    :assigneeAreaName="approvalData.assigneeAreaName"
                    :definition="approvalData.definition"
                    :bindEvent="false"
                    :showcc="false"
                    :error="startError"
                    :businessId="approvalData.businessId"
                    :variables="approvalData.variables"
                    :startUpSuccess="startUpSuccess"
                    :beforeOpen="beforeOpen"
                    :msgUrl="msgUrl"
                    :msgTit="msgTit"
                    :module="module"></start-approval>
  </div>
</template>

<script>
  import {startApproval} from 'gs-start-approval'
  import { sImageUploadLocal } from '@/components/upload/image'
  import { fileUpload } from 'sd-minio-upfile'
  import { getUserCache } from '@/libs/util'
  import caseInfo from './caseInfo'
  import baseInfo from './baseInfo'
  import caseHandlerInfo from "./caseHandlerInfo";
  import treatmentInfo from './treatmentInfo'
  import socialRelationshipInfo from './socialRelationshipInfo'
  import otherInfo from "./otherInfo";
  import {mapActions} from "vuex";
    export default {
      components:{
        sImageUploadLocal,fileUpload,caseInfo,baseInfo,caseHandlerInfo,treatmentInfo,socialRelationshipInfo,otherInfo,startApproval
      },
      props:{
        rowData:{
          type: [Array,Object],
          default: {}
        },
        saveType:{
          default:'add',
          type:String
        },
        quickRegistration:{
          default: false,
        },
        entireProcess: {
          default: false,
        }
      },
      data(){
        return{
          steps: [
            { title: '基本信息' },
            { title: '案件信息' },
            { title: '办案人信息' },
            { title: '收治信息' },
            { title: '社会关系' },
            { title: '其他信息' },
          ],
          current:0,
          formData: {
            sfsm: '0'
          },
          loading:false,
          loadingSave:false,
          ruleValidate: {},
          msgUrl: '/#/detentionBusiness/treatmentEnterRegister',
          msgTit:'【审批】收治入所',
          businessId:this.rowData.rybh,
          module: serverConfig.APP_MARK,
          approvalData: {
            definition: [
              {
                name: '快速入所审批',
                defKey: 'syywksrssp'
              }
            ],
            assigneeOrgId: this.$store.state.common.orgCode,
            assigneeOrgName: this.$store.state.common.orgName,
            assigneeUserId:this.$store.state.common.idCard,
            assigneeUserName:this.$store.state.common.userName,
            businessId:this.rowData.rybh,
            fApp: serverConfig.APP_MARK,
            fXxpt: 'pc',
            variables: {
              eventCode:this.rowData.rybh,
              busType:'105_006'
            }
          },
        }
      },
      methods:{
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        handleClose(){
          this.$emit('close',false)
        },
        handleNext(data){
          this.$emit('nextStep',data)
        },
        stepClick(index){
          this.current = index
        },
        next () {
          if (this.current < 5) {
            this.current += 1;
          }
        },
        prev () {
          if (this.current > 0) {
            this.current -= 1;
          }
        },
        beforeUpload(){},
        fileSuccessFile(){},
        fileRemoveFile(){},
        fileCompleteFile(data,index){
          if(data && data.length>0){
            console.log(data,'sypzwsdz')
            this.$set(this.formData,'sypzwsdz',JSON.stringify(data))
          }
        },
        getCurrentTimeFormatted() {
          const now = new Date();

          const year = now.getFullYear();             // 获取年份
          const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，要加1，并补零
          const day = String(now.getDate()).padStart(2, '0');        // 日期补零

          const hours = String(now.getHours()).padStart(2, '0');     // 小时补零
          const minutes = String(now.getMinutes()).padStart(2, '0'); // 分钟补零
          const seconds = String(now.getSeconds()).padStart(2, '0'); // 秒数补零

          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        getSocialRelationsList(rybh){
          this.$store.dispatch('authPostRequest',{
            url: this.$path.app_getSocialRelationsList,
            params: {
              rybh:rybh
            }
          }).then(resp => {
            if(resp.code == 0){
              this.formData.socialRelations = resp.data
              if(this.formData.socialRelations){
                this.$refs.socialRelationshipInfoForm.updateSocialRelations(this.formData.socialRelations)
              }

            }else{
              this.$Modal.error({
                title: '温馨提示',
                content: resp.msg || '操作失败'
              })
            }
          })
        },
        getDetail(id){
          this.$store.dispatch('authGetRequest',{
            url: this.$path.app_detainRegKssGet,
            params: {
              id:id
            }
          }).then(resp => {
            if(resp.code == 0){
              this.formData = resp.data
              this.formData.id = resp.data.id
              this.formData.rybh = resp.data.rybh
              if(this.formData.xzhjaj){
                this.formData.xzhjaj = this.formData.xzhjaj.toString()
              }
              if(this.formData.xzhj != undefined && this.formData.xzhj != null){
                this.formData.xzhj = this.formData.xzhj.toString()
              }
              if(this.formData.xzhjaj != undefined && this.formData.xzhjaj != null){
                this.formData.xzhjaj = this.formData.xzhjaj.toString()
              }
              if(!this.formData.sfsm){
                this.formData.sfsm = "0"
              }
              this.$refs.caseInfoForm.updataSypzwsdzUrl(this.formData.sypzwsdz)
              if(this.formData.socialRelations){
                this.$refs.socialRelationshipInfoForm.updateSocialRelations(this.formData.socialRelations)
              }
              if(this.formData.jsh){
                this.$refs.treatmentInfoForm.getSuitNumByRoomId(this.formData.jsh)
              }
              this.$refs.otherInfoForm.updataYysBxUrl(this.formData.yysBx)
              // if(this.formData.sypzwsdz){
              //   this.sypzwsdzUrl = JSON.parse(this.formData.sypzwsdz)
              // }
              this.formData.jbsj = this.getCurrentTimeFormatted();
              this.$set(this.formData,'jbr',getUserCache.getUserName())
              this.getSocialRelationsList(this.rowData.rybh)
            }else{
              this.$Modal.error({
                title: '温馨提示',
                content: resp.msg || '操作失败'
              })
            }
          })
        },
        getAreaListEvent(orgCodeList){
          console.log(orgCodeList,"orgCodeList---")
          if(this.$refs.treatmentInfoForm){
            const valid = this.$refs.treatmentInfoForm.getAreaListEvent(orgCodeList);
          }
        },
        getRecordByzjhm(){
          if(!this.formData.zjhm || !this.formData.zjhm.trim()){
            this.$Message.error('请填写证件号码!!');
            return;
          }
          this.$store.dispatch('authGetRequest',{
            url: this.$path.app_getRecordByzjhm,
            params: {
              zjhm:this.formData.zjhm
            }
          }).then(resp => {
            if(resp.code == 0){
              this.formData = resp.data
              if(this.formData.xzhj != undefined && this.formData.xzhj != null){
                this.formData.xzhj = this.formData.xzhj.toString()
              }
              if(this.formData.xzhjaj != undefined && this.formData.xzhjaj != null){
                this.formData.xzhjaj = this.formData.xzhjaj.toString()
              }
              if(!this.formData.sfsm){
                this.formData.sfsm = "0"
              }
              this.$refs.caseInfoForm.updataSypzwsdzUrl(this.formData.sypzwsdz)

              this.$refs.otherInfoForm.updataYysBxUrl(this.formData.yysBx)
              if(this.formData.jsh){
                this.$refs.treatmentInfoForm.getSuitNumByRoomId(this.formData.jsh)
              }
              // if(this.formData.sypzwsdz){
              //   this.sypzwsdzUrl = JSON.parse(this.formData.sypzwsdz)
              // }
              this.formData.jbsj = this.getCurrentTimeFormatted();
              this.$set(this.formData,'jbr',getUserCache.getUserName())
            }else{
              this.$Modal.error({
                title: '温馨提示',
                content: resp.msg || '操作失败'
              })
            }
          })
        },
        async handleSubmit(tag){


          if(this.$refs.baseInfoForm){
            if(tag || !tag && this.current === 0){
              const valid = await this.$refs.baseInfoForm.validate();
              if(!valid){
                this.$Message.error('基本信息请填写完整!!');
                return;
              }
            }
          }
          if(this.$refs.caseInfoForm){
            if(tag || !tag && this.current === 1){
              const valid = await this.$refs.caseInfoForm.validate();
              if(!valid){
                this.$Message.error('案件信息请填写完整!!');
                return;
              }
            }
          }
          if(this.$refs.caseHandlerInfoForm){
            if(tag || !tag && this.current === 2){
              const valid = await this.$refs.caseHandlerInfoForm.validate();
              if(!valid){
                this.$Message.error('办案人信息请填写完整!!');
                return;
              }
            }
          }
          if(this.$refs.treatmentInfoForm){
            if(tag || !tag && this.current === 3){
              const valid = await this.$refs.treatmentInfoForm.validate();
              if(!valid){
                this.$Message.error('收押信息请填写完整!!');
                return;
              }
            }
          }
          if(this.$refs.otherInfoForm){
            if(tag || !tag && this.current === 5){
              const valid = await this.$refs.otherInfoForm.validate();
              if(!valid){
                this.$Message.error('其他信息请填写完整!!');
                return;
              }
            }
          }
          if(tag){
            this.saveData(tag)
          }else{
            this.saveData()
          }
        },
        saveData(tag){
          this.formData.dataSources?'':this.$set(this.formData,'dataSources',0)
          let params=this.formData
          let url = this.$path.app_detainRegKssCreate
          if(this.formData.status && this.formData.status != "01"){
            url = this.$path.app_detainRegKssUpdate
          }
          if(tag){
            this.loading=true
            params.status = "03"
          }else{
            this.loadingSave=true
            params.status = "02"
          }
          if(this.saveType == "add"){
            params.rslx = "01"
            if(this.quickRegistration){
              console.log("快速入所")
              params.rslx = "02"
            }
          }else if(this.rowData.current_step == "01" && this.rowData.status == "01"){
            params.rslx = "01"
            if(this.quickRegistration){
              console.log("快速入所")
              params.rslx = "02"
            }
          }
          this.$store.dispatch('authPostRequest',{
            url: url,
            params:params
          }).then(resp => {
            this.loading=false
            this.loadingSave=false
            if (resp.code==0){
              if(tag){
                this.$Message.success('提交成功!');
              }else{
                this.$Message.success('保存成功!');
              }
              if (this.quickRegistration && tag && !this.rowData.act_inst_id) {
                this.rowData.rybh = resp.data
                this.businessId = this.rowData.rybh
                this.msgUrl = '/#/detentionBusiness/treatmentEnterRegister?eventCode=' + this.rowData.rybh
                this.$refs['approval'].openStartApproval()
              }else{
                this.handleClose();
              }

            }else{
              this.$Notice.error({
                title:'错误提示',
                desc:resp.msg
              })
            }
          })
        },
        startError(data) {
          this.errorModal({ content: '流程启动失败。原因:' + data.msg }).then(() => {
            location.reload()
          })
        },
        startUpSuccess(data) {
          return new Promise((resolve, reject) => {
            let that = this
            setTimeout(() => {
              this.updateRegStatus(data)
            }, 500)
          })
        },
        updateRegStatus(data){
          let params={
            rybh:this.rowData.rybh,
            taskId:"",
            actInstId:data.actInstId
          }
          //调用接口
          this.$store.dispatch('authPostRequest',{
            url: this.$path.app_detainRegKssUpdateWorkflowInfo,
            params:params
          }).then(resp => {
            if (resp.code==0) {
              this.$Message.success('提交成功!');
              this.handleClose();
            } else {
              this.$Message.error(resp.msg);
            }
          })
        },
        beforeOpen () {
          return new Promise((resolve, reject) => {
            this.$set(this.approvalData,'businessId',this.rowData.rybh)
            this.msgUrl= '/#/detentionBusiness/treatmentEnterRegister?eventCode='+this.rowData.rybh
            this.msgTit=`【收押入所审批】民警：${this.formData.jbr}于${this.formData.jbsj}提交了对${this.formData.xm}收押入所的申请，请尽快审批！`
            resolve(true)
          })
        },
      },
      mounted(){
        this.formData.jbsj = this.getCurrentTimeFormatted();
        this.$set(this.formData,'symjxm',getUserCache.getUserName())
        this.$set(this.formData,'jbr',getUserCache.getUserName())
        if(this.saveType == "edit"){
          this.formData = JSON.parse(JSON.stringify(this.rowData))
          this.formData.jbsj = this.getCurrentTimeFormatted();
          this.$set(this.formData,'jbr',getUserCache.getUserName())
          console.log(this.formData,'this.formData');
          if(this.rowData.status == "01"){
            this.getRecordByzjhm();
          }
          if(this.rowData.status == "02"){
            this.getDetail(this.rowData.id)
          }

        }else{
          this.$refs.caseInfoForm.updataSypzwsdzUrl()
          this.$refs.otherInfoForm.updataYysBxUrl()
        }
      }
    }
</script>

<style scoped lang="less">
  .steps-box{
    height: 100px;
    display: flex;
    align-items: center;
  }
  .bsp-imgminio-container{
    width:100% !important;
  }
</style>
