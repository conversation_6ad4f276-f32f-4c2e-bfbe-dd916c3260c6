<template>
  <div>
    <Card title="新增申诉" dis-hover :bordered="false">
 
        <div class="com-form-container">
          <div class="com-module-layout">
            <p class="detail-title">基本信息</p>
            <div class="com-content-wrapper">
              <Form :model="formItem" ref="formData"  :rules="formRules">
                <FormItem label="申诉标题" prop="title" :col="3">
                  <ui-input  v-model="formItem.title"/>
                </FormItem>
                <FormItem label="申诉单位">
                  <com-form-text :value="formItem.unitName"></com-form-text>
                </FormItem>
                <FormItem label="申诉人">
                  <com-form-text :value="formItem.representUserName"></com-form-text>
                </FormItem>
                <FormItem label="申诉时间">
                  <com-form-text :value="formItem.representTime"></com-form-text>
                </FormItem>
                <FormItem label="申诉理由" prop="representReason" :col="3">
                  <ui-input  v-model="formItem.representReason" type="textarea" ></ui-input>
                </FormItem>
                <FormItem label="备注" :col="3">
                  <ui-input   v-model="formItem.remark" type="textarea" />
                </FormItem>
                <FormItem label="附件" :col="3">
                  <ui-multi-uploader v-model="formItem.files"></ui-multi-uploader>
                </FormItem>
              </Form>
            </div>
            <com-hr/>
            <p class="detail-title">关联督导单</p>
      
          </div>
          <div class="com-form-submit">
            <!-- 暂时屏蔽入口，待业务重构完再处理 -->
            <!-- <ui-button  @click="forwardModal = true">转发</ui-button> -->
            <Button  @click="handleBack()">返回</Button>
            <Button  @click="handleSave">保存</Button>
            <Button @click="handleSend">提交</Button>
          </div>
        </div>
       
    </Card>
  </div>
</template>
<script>
// import {keepRepresentForm, facingPage} from "@/axios/zhjgBranchWork";
// import {fileTranslate, cloneDeep} from "@/util";
// import { OPERTYPES } from "@/constant";
// import {forwardForm, transferRecord, relateSupervisionList} from "../../components";
// 传参
// row: 详情信息
// tableData: 督导表格
export default {
  name: "prisonComplainForm",
  components: {
  },
  data() {
    return {

    };
  },
  async created() {
   
  },
  methods: {
  },
};
</script>
