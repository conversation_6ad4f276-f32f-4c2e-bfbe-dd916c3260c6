<template>
  <div>
    <Card title="监所督导" dis-hover :bordered="false">
      <div class="com-form-container">
        <div class="com-module-layout">
          <p class="detail-title">基本信息</p>
          <div class="com-content-wrapper">
            <Form :model="formItem" ref="formData" :rules="formRules" :label-width="120" label-colon>
              <Row>
                <Col span="8">
                <FormItem label="违规时间" prop="outlineTime">
                  <DatePicker type="datetime" v-model="formItem.outlineTime" @on-change="getTemplate"></DatePicker>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="违规地点">
                  <Select v-model="formItem.addressId" @on-change="changeRoom" multiple :label-in-value="true">
                    <Option v-for="item in orgCodeList" :value="item.roomCode" :key="item.roomCode">{{ item.roomName }}
                    </Option>
                  </Select>
                </FormItem>
                </Col>
              </Row>
              <Row>
                <Col span="8">
                <FormItem label="巡查来源" prop="source">
                  <s-dicgrid v-model="formItem.source" @change="$refs.formItem.validateField('source')" :isSearch="true"
                    dicName="ZD_XCLY" />
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="违规对象" prop="outlineObject">
                  <s-dicgrid v-model="formItem.outlineObject" @change="changeOutlineObject" :isSearch="false"
                    dicName="ZD_WGDX" />
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="违规人员">
                  <Input v-model="formItem.outlinePeopleStr" readonly placeholder="请选择监室号">
                  <template slot="append">
                    <Button @click="openPrison = true">选择</Button>
                  </template>
                  </Input>
                </FormItem>
                </Col>

                <Col span="24">
                <FormItem label="违规类型" prop="outlineQuestionStr">
                  <s-dicgrid v-model="formItem.outlineQuestionStr" @values="changeProblem" :isSearch="true"
                    dicName="ZD_WGDJWGNR" :multiple="true" />
                </FormItem>
                </Col>
              </Row>
              <FormItem label="违规详情" prop="detail">
                <Input v-model="formItem.detail" type="textarea" placeholder="请填写"
                  :autosize="{ minRows: 2, maxRows: 5 }" />
              </FormItem>
              <Row>
                <Col span="8">
                <FormItem label="巡查时间" prop="aroundTime">
                  <DatePicker type="datetime" v-model="formItem.aroundTime"></DatePicker>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="接收人">
                  <user-selector v-model="formItem.recipientCards" tit="用户选择" @onSelect="onSelectUs"
                    :text.sync="formItem.recipient" returnField="id">
                  </user-selector>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="整改期限" prop="correctPeriod">
                  <DatePicker type="datetime" v-model="formItem.correctPeriod"></DatePicker>
                </FormItem>
                </Col>
              </Row>
              <FormItem label="备注">
                <Input v-model="formItem.remark" type="textarea" placeholder="请填写"
                  :autosize="{ minRows: 2, maxRows: 5 }" />
              </FormItem>
              <FormItem label="附件">
                <!-- <ui-multi-uploader v-model="formItem.files"></ui-multi-uploader> -->
                <file-upload :defaultList="fileList" :serviceMark="serviceMark" :bucketName="bucketName"
                  :beforeUpload="beforeUpload" @fileSuccess="fileSuccessFile" @fileRemove="fileRemoveFile"
                  @fileComplete="fileCompleteFile" v-if="showFile" />
              </FormItem>
              <Row>
                <Col span="8">
                <FormItem label="违规登记人">
                  <Input v-model="formItem.operateUserName" disabled />
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="登记时间">
                  <DatePicker type="datetime" v-model="formItem.operateTime" disabled></DatePicker>
                </FormItem>
                </Col>
              </Row>
            </Form>
          </div>
        </div>
        <div class="com-form-submit">
          <!-- 暂时屏蔽入口，待业务重构完再处理 -->
          <!-- <Button @click="forwardModal = true" v-if="formId">转发</Button> -->
          <Button type="primary" @click="operateNext">继续督导</Button>
        </div>
      </div>

    </Card>
    <Modal v-model="openPrison" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
      title="人员列表">
      <div class="select-use">
        <prisonSelect ref="prisonSelect" ryzt="ALL" :isMultiple='true' :selectUseIds="formItem.outlinePeople" />
      </div>
      <div slot="footer">
        <Button type="primary" @click="changeObject" class="save">确 定</Button>
        <Button @click="openPrison = false" class="save">关 闭</Button>
      </div>
    </Modal>
  </div>

</template>
<script>
import dayjs from 'dayjs';
import { mapActions } from "vuex";
import { getUserCache } from '@/libs/util'
import { prisonSelect } from "sd-prison-select"
import { fileUpload } from 'sd-minio-upfile'
import { userSelector } from 'sd-user-selector'
// params的参数如下, 后续新增参数需说明
// form 信息初始化
// id  获取支队信息
// row 该督导单的列表信息
export default {
  name: "prisonAddSupervision",
  components: {
    prisonSelect,
    fileUpload,
    userSelector
  },
  // directives: { loading },
  data() {
    const validOutlineTime = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择违规时间"));
      } else if (new Date(value).getTime() > new Date().getTime()) {
        callback(new Error("不得大于当前时间"));
      }
      callback();
    };
    const validAroundTime = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择巡查时间"));
      } else {
        if (new Date(value).getTime() > new Date().getTime()) {
          callback(new Error("不得大于当前时间"));
        }
        if (this.formItem.outlineTime) {
          if (new Date(value).getTime() < new Date(this.formItem.outlineTime).getTime()) {
            callback(new Error("不得小于违规时间"));
          }
        }
      }
      callback();
    };
    const validLimitTime = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择整改期限"));
      } else if (this.formItem.aroundTime) {
        if (new Date(value).getTime() < new Date(this.formItem.aroundTime).getTime()) {
          callback(new Error("不得小于巡查时间"));
        }
      }
      callback();
    };
    return {
      openPrison: false,
      orgCodeList: [],
      formItem: {
        isLeaderAdd: "1",
        // unitId: userInfo.prisonId,
        // unitName: userInfo.prisonName,
        outlineTime: new Date(),
        addressId: [],
        addressName: [],
        addressInit: [],
        source: "01",
        outlineObject: "02",
        outlinePeople: '',
        outlinePeopleStr: '',
        outlinePeopleInit: [],
        outlineQuestion: [],
        outlineQuestionStr: '',
        outlineQuestionInit: [],
        detail: "",
        aroundTime: new Date(),
        recipient: '',
        receivers: [],
        recipientInit: [],
        correctPeriod: new Date(new Date().setDate(new Date().getDate() + 3)),
        remark: "",
        files: [],
        operateTime: new Date(),
        username: getUserCache.getUserName(),
        operateUserId: getUserCache.getIdCard(),
        operateUserName: getUserCache.getUserName(),
      },
      formRules: {
        outlineTime: [{ required: true, trigger: "change", validator: validOutlineTime }],
        source: [{ required: true, trigger: "change", message: "请选择巡查来源" }],
        outlineObject: [{ required: true, trigger: "change", message: "请选择违规对象" }],
        outlineQuestionStr: [{ required: true, trigger: "change",  message: "请选择违规类型", type: "array", min: 1}],
        detail: [{ required: true, trigger: "change", message: "请填写违规详情" }],
        aroundTime: [{ required: true,  trigger: "change", validator: validAroundTime }],
        correctPeriod: [{ required: true,   trigger: "change", validator: validLimitTime }],
      },
      // 文件上传
      showFile: true,
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      fileList: [],     // 违禁品照片列表
    };
  },
  async created() {
    await this.initData();
  },
  methods: {
    // 列表请求
    async initData() {
      this.getAreaList()
    },
    getAreaList() {
      let params = {
        pageNo: 1,
        pageSize: 100,
      }
      params.orgCode = getUserCache.getOrgCode()
      this.$store.dispatch('authPostRequest', { url: this.$path.app_getAreaPrisonRoomPage, params: params }).then(resp => {
        if (resp.code == 0) {
          if (resp.data && resp.data.list && resp.data.list.length > 0) {
            this.orgCodeList = resp.data.list.map(item => ({
              roomCode: item.roomCode,
              roomName: item.roomName
            }));
          }
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    // 违规地点
    changeRoom(arr) {
      this.$set(this.formItem, "addressName", []);
      // todo 后续如需保留获取监室的协管民警，需要后台重新提供接口
      console.log(arr, this.formItem.addressId);
      arr.forEach(item => {
        this.formItem.addressName.push(item.label);
      });
      this.getTemplate();
    },
    getTemplate() {

    },
    // 违规对象的选择
    changeOutlineObject() {
      // this.$set(this.formItem, "outlinePeople", []);
      // this.$set(this.formItem, "outlinePeopleStr", []);
      this.$set(this.formItem, "outlineQuestion", []);
      this.$set(this.formItem, "outlineQuestionStr", []);
      this.$set(this.formItem, "outlineQuestionInit", []);
      this.getTemplate();
    },
    // 违规人员
    changeObject() {
      if (this.$refs.prisonSelect.checkedUse && this.$refs.prisonSelect.checkedUse.length > 0) {
        let outlinePeople = this.$refs.prisonSelect.checkedUse.map(item => item.jgrybm).join(",");
        let outlinePeopleStr = this.$refs.prisonSelect.checkedUse.map(item => item.xm).join(",");
        console.log(outlinePeople, outlinePeopleStr)
        this.$set(this.formItem, "outlinePeople", outlinePeople);
        this.$set(this.formItem, "outlinePeopleStr", outlinePeopleStr);
        this.openPrison = false
      } else {
        this.$Notice.warning({
          title: '提示',
          desc: '请选择人员!'
        })
      }
      this.getTemplate();
    },
    changeProblem(e) {
      console.log(e);

    },
    // 继续督导
    async operateNext() {
      console.log(this.formItem);

    },
    onSelectUs(data) {
      this.formItem.receivers = [];
      data.forEach(item => {
        this.formItem.receivers.push({
          userId: item.id,
          userName: item.name
        });
      });
    },
    fileCompleteFile(data) {
      this.fileList = data || []
      this.formItem.files = data && data.length > 0 ? JSON.stringify(data) : ''
    },
    /**
* 文件上传成功回调
*/
    fileSuccessFile(data) {
      // 文件上传成功处理
    },
    /*
     文件删除回调
     */
    fileRemoveFile(data) {
      // 文件删除处理
    },
    beforeUpload() { },




  },
};
</script>
