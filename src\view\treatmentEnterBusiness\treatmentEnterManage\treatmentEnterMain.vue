<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <el-steps :space="130" :active="current" direction="vertical" class="steps-style" align-center>
      <el-step v-for="(step, index) in steps" :key="index">
        <template #title>
          <div :class="index==current ? 'detention-step detention-step-active' :'detention-step detention-step-unActive'" @click="skipStep(step,index)">
            <span>{{ step.title }}</span>
          </div>
        </template>
      </el-step>
    </el-steps>
    <div v-if="current === 0" class="release-step-box">
      <treatmentEnterForm v-if="steps[current].status != '03'" @close="handleClose" @nextStep="handleNextStep" :entireProcess="entireProcess" :saveType="saveType" :quickRegistration="quickRegistration" :rowData="rowData"></treatmentEnterForm>
      <treatmentEnterDetail v-else @close="handleClose" :rowData="rowData"></treatmentEnterDetail>
    </div>
    <div v-if="current === 1 && !quickRegistration" class="release-step-box">
      <healthCheckForm v-if="steps[current].status != '03'" @close="handleClose" @nextStep="handleNextStep" :entireProcess="entireProcess" :rowData="rowData"></healthCheckForm>
      <healthCheckDetail :entireProcess="entireProcess" v-else @close="handleClose" :rowData="rowData"></healthCheckDetail>
    </div>
    <div v-if="(current === 2 && !quickRegistration) || (current === 1 && quickRegistration)" class="release-step-box">
      <leadershipApprovalForm v-if="steps[current].status != '03'" @close="handleClose" @nextStep="handleNextStep" :entireProcess="entireProcess" :rowData="rowData"></leadershipApprovalForm>
      <leadershipApprovalDetail v-else @close="handleClose" :rowData="rowData"></leadershipApprovalDetail>
    </div>
    <div v-if="current === 3 && !quickRegistration" class="release-step-box">
      <biologicalInfoForm v-if="steps[current].status != '03'" @close="handleClose" @nextStep="handleNextStep" :entireProcess="entireProcess" :rowData="rowData"></biologicalInfoForm>
      <biologicalInfoDetail v-else @close="handleClose" :rowData="rowData"></biologicalInfoDetail>
    </div>
    <div v-if="current === 4 && !quickRegistration" class="release-step-box">
      <belongingsForm v-if="steps[current].status != '03'" @close="handleClose" @nextStep="handleNextStep" :entireProcess="entireProcess" :rowData="rowData"></belongingsForm>
      <belongingsDetail v-else @close="handleClose" :rowData="rowData"></belongingsDetail>
    </div>
  </div>
</template>

<script>
  import treatmentEnterForm from './registrationManage/addForm'
  import healthCheckForm from '../healthCheckManage/registrationManage/addForm'
  import biologicalInfoForm from '../biologicalInfoManage/registrationManage/addForm'
  import belongingsForm from '../belongingsManage/registrationManage/addForm'
  import leadershipApprovalForm from '../leadershipApprovalManage/registrationManage/approvalForm'

  import treatmentEnterDetail from './recordManage/detail'
  import healthCheckDetail from '../healthCheckManage/recordManage/detail'
  import biologicalInfoDetail from '../biologicalInfoManage/recordManage/detail'
  import belongingsDetail from '../belongingsManage/recordManage/detail'
  import leadershipApprovalDetail from '../leadershipApprovalManage/recordManage/recordDetail'


  export default {
    components:{
      treatmentEnterForm,
      healthCheckForm,
      biologicalInfoForm,
      belongingsForm,
      leadershipApprovalForm,
      treatmentEnterDetail,
      healthCheckDetail,
      biologicalInfoDetail,
      belongingsDetail,
      leadershipApprovalDetail
    },
    props:{
      rowData:{
        type: [Array,Object],
        default: {}
      },
      saveType: {
        default: 'add',
      },
      quickRegistration:false,
    },
    data(){
      return{
        current:0,
        realityStep:0,  // 实际已经到的流程
        entireProcess:true,
        steps: [
          { title: '收治登记',name:"rsdj", status: '01' },
          { title: '健康检查',name:"jkjc", status: '01' },
          { title: '领导审批',name:"ldsp", status: '01' },
          { title: '生物信息采集',name:"xxcj", status: '01' },
          { title: '物品保管',name:"wpgl", status: '01' },
        ]
      }
    },
    methods:{
      handleClose(){
        this.$emit('close',false)
      },
      handleNextStep(data){
        if(data && this.current == 0){
          this.rowData.rybh = data
          this.rowData.jgrybm = data
        }
        if(this.current == 0){
          let rsdj = this.steps.find(t=> t.name == "rsdj")
          if(rsdj){
            rsdj.status = "03"
          }
        }else if(this.current == 1){
          let jkjc = this.steps.find(t=> t.name == "jkjc")
          if(jkjc){
            jkjc.status = "03"
          }
        }else if(this.current == 2){
          let xxcj = this.steps.find(t=> t.name == "xxcj")
          if(xxcj){
            xxcj.status = "03"
          }
        }else if(this.current == 3){
          let wpgl = this.steps.find(t=> t.name == "wpgl")
          if(wpgl){
            wpgl.status = "03"
          }
        }
        if(this.current == 2){
          let wpgl = this.steps.find(t=> t.name == "wpgl")
          if(wpgl.status != "03"){
            this.current = 3
          }else{
            this.current = 4
          }
        }else if(this.current == 3){
          let xxcj = this.steps.find(t=> t.name == "xxcj")
          if(xxcj.status != "03"){
            this.current = 2
          }else{
            this.current++
          }
        }
        else{
          this.current++
        }
        this.realityStep = this.current
        if(this.current >= 4){
          this.handleClose()
        }
      },
      skipStep(step,index){
        if(step.name == 'rsdj'){
          this.current = 0
        }
        if(step.name == 'jkjc'){
          if(1 > this.realityStep){
            this.$Message.error('请先完成当前流程!!');
            return;
          }
          this.current = 1

        }
        if(step.name == 'ldsp'){
          if(1 == this.realityStep){
            this.$Message.error('请先完成当前流程!!');
            return;
          }
          if(this.quickRegistration){
            this.current = 1
          }else{
            this.current = 2
          }

        }
        if(step.name == 'xxcj'){
          if(2 > this.realityStep){
            this.$Message.error('请先完成当前流程!!');
            return;
          }
          this.current = 3
        }
        if(step.name == 'wpgl'){
          if(3 > this.realityStep){
            this.$Message.error('请先完成当前流程!!');
            return;
          }
          this.current = 4
        }


        // if(this.realityStep == 2 || this.realityStep == 3){
        //   let xxcj = this.steps.find(t=> t.name == "xxcj")
        //   let wpgl = this.steps.find(t=> t.name == "wpgl")
        //   if(step.name == 'ldsp'){
        //     if(xxcj.status != "03"){
        //       this.$Message.error('请先完成生物信息采集流程!!');
        //       // return;
        //     }
        //     if(wpgl.status != "03"){
        //       this.$Message.error('请先完成物品保管流程!!');
        //       // return;
        //     }
        //   }
        // }else if(index > this.realityStep){
        //   this.$Message.error('请先完成当前流程!!');
        //   // return;
        // }
        // if(this.current != index){
        //   this.current = index
        // }
      },
      getProcessInfo(rybh){
        this.$store.dispatch('authGetRequest',{
          url: this.$path.app_detainRegKssGetInRecordStatus,
          params: {
            rybh:rybh
          }
        }).then(resp => {
          if(resp.code == 0){
            // let data = {
            //   "currentStep": "01",
            //   "rsdj": "03",
            //   "jkjc": "03",
            //   "wpgl": "03",
            //   "xxcj": "03"
            // };
            let data = resp.data;
            if(data){
              let status =  Number(this.rowData.current_step)
              if(isNaN(status)){
                this.current = 0
                this.realityStep =0
              }else{
                this.realityStep = status - 1
              }

              if(status > 0 && status < 3){
                this.current = status - 1;
              }else if(status == 3){
                this.current = 3;
              }else if(status == 4){
                this.current = 4;
              }else if(status == 5){
                if(this.quickRegistration){
                  this.current = 1;
                }else{
                  this.current = 2;
                }

              }else{
                this.current = 0
              }
              if(this.quickRegistration){
                this.steps = [
                  { title: '收押登记',name:"rsdj", status: '01' },
                  // { title: '健康检查',name:"jkjc", status: '01' },
                  // { title: '生物信息采集',name:"xxcj", status: '01' },
                  // { title: '物品保管',name:"wpgl", status: '01' },
                  { title: '领导审批',name:"ldsp", status: '01' }
                ];
                if(this.current == 4){
                  this.current = 1
                }
              }

              let rsdj = this.steps.find(t=> t.name == "rsdj")
              if(rsdj){
                rsdj.status = data.rsdj
              }
              let jkjc = this.steps.find(t=> t.name == "jkjc")
              if(jkjc){
                jkjc.status = data.jkjc
              }
              let wpgl = this.steps.find(t=> t.name == "wpgl")
              if(wpgl){
                wpgl.status = data.wpgl
              }
              let xxcj = this.steps.find(t=> t.name == "xxcj")
              if(xxcj){
                xxcj.status = data.xxcj
              }
            }
          }else{
            this.$Modal.error({
              title: '温馨提示',
              content: resp.msg || '操作失败'
            })
          }
        })
      }
    },
    mounted() {
      if(this.rowData.current_step){
        let status =  Number(this.rowData.current_step)
        if(isNaN(status)){
          this.current = 0
          this.realityStep =0
        }else{
          this.realityStep = status - 1
        }

        if(status > 0 && status < 3){
          this.current = status - 1;
        }else if(status == 3){
          this.current = 3;
        }else if(status == 4){
          this.current = 4;
        }else if(status == 5){
          if(this.quickRegistration){
            this.current = 1;
          }else{
            this.current = 2;
          }
        }else{
          this.current = 0
        }

      }
      if(this.saveType != 'add' && this.rowData.status != "01"){
        console.log(this.rowData, "this.rowData-----流程")
        this.getProcessInfo(this.rowData.rybh)
      }
      if(this.quickRegistration){
        this.steps = [
          { title: '收押登记',name:"rsdj", status: '01' },
          // { title: '健康检查',name:"jkjc", status: '01' },
          // { title: '生物信息采集',name:"xxcj", status: '01' },
          // { title: '物品保管',name:"wpgl", status: '01' },
          { title: '领导审批',name:"ldsp", status: '01' }
        ];
        if(this.current == 4){
          this.current = 1
        }
      }
    }
  }
</script>

<style scoped lang="less">
  .steps-style{
    float: left;
    width:15%;
    margin-left: 20px;
    margin-top: 30px;
  }
  .release-step-box{
    height: 100%;
    width: 80%;
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .detention-step{
    border-radius: 6px;
    border: 1px solid rgba(187,187,187,1);
    height: 79px;
    width: 214px;
    text-align: left;
    display: flex;
    align-items: center;
    cursor: pointer;
    span{
      font-size: 20px;
      margin-left: 20px;
    }
  }
  .detention-step-active{
    background-color: rgba(49,127,245,1);
    span{
      color: rgba(255,255,255,1);

    }
  }
  .detention-step-unActive{
    background-color: rgba(232,232,232,1);
    span{
      color: rgba(16,16,16,1);
    }
  }
</style>
