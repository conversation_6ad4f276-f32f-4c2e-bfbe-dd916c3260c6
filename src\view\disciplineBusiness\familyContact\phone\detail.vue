<template>
  <!-- 详情页面 -->
  <div>
    <Row style="margin-top: 1px">
      <Col span="12">
        <span class="Light-blue">通话对象</span>
        <span class="Light-gray">{{ formData.telObject }}</span>
      </Col>
      <Col span="12">
        <span class="Light-blue">关系</span>
        <span class="Light-gray">{{ formData.relNameName }}</span>
      </Col>
    </Row>
    <Row style="margin-top: 1px">
      <Col span="12">
        <span class="Light-blue">联系电话</span>
        <span class="Light-gray">{{ formData.telNum }}</span>
      </Col>
      <Col span="12">
        <span class="Light-blue">通话日期</span>
        <span class="Light-gray">{{ formData.telTime }}</span>
      </Col>
    </Row>
  </div>
</template>

<script>

export default {
  props: {
    formData: Object
  },
  components: {},
  data() {
    return {
    }
  }
}
</script>

<style scoped lang="less">
.Light-gray {
  background-color: #f5f7fa;
  display: inline-block;
  flex-shrink: 0;
  text-align: center; /* 水平居中 */
  vertical-align: middle; /* 垂直居中 */
  margin-left: 2px;
  line-height: 60px;
  margin-top: 1px;
  width: 69%;
}

.Light-blue {
  line-height: 60px;
  display: inline-block;
  text-align: center; /* 水平居中 */
  vertical-align: middle; /* 垂直居中 */
  flex-shrink: 0;
  background-color: #e4eefc;
  margin-top: 1px;
  width: 30%;
}

</style>
