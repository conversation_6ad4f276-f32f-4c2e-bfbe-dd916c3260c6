<template>
  <div>
    <div class="bsp-base-tit" style="font-size: x-large;margin: 10px;border-bottom: 1px solid;border-color: #e5e7eb;">等级设置</div>
    <div class="card-container" v-if="!showData && riskCards.length > 0" >
      <div v-for="riskCard in riskCards" :key="riskCard.id" class="card-info">
        <RiskCard :riskCard="riskCard" @handle-detail="handleDetail" @handle-edit="handleEdit"/>
      </div>
    </div>
    <noData v-else />
    <!--编辑和详情-->
    <Modal v-model="showForm" class-name="select--modal" width="60%" :title="formTitle">
      <div class="addOffice-wrap">
        <div class="fm-content-info">
          <p class="sys-sub-title">配置信息</p>
          <div class="borrow-detail">
            <Row>
              <Col span="8">
                <span class="content-title">模型名称:</span>
                <span class="content-detail">{{ riskCardModel.name }}</span>
              </Col>
              <Col span="8">
                <span class="content-title">所属监所：</span>
                <span class="content-detail">{{ riskCardModel.orgName }}</span>
              </Col>
              <Col span="8">
              </Col>
            </Row>
            <Row>
              <Col v-for="levelConfig in riskCardModel.levelConfigs" :key="levelConfig.orderId" span="8">
                <span class="content-title">
                  <span style="display: inline-block;margin-right: 4px;color: rgb(245 108 108)">*</span> {{
                    levelConfig.name
                  }} :
                </span>
                <span>
                  <Input v-model="levelConfig.thresholdStartValue" disabled="saveType === 'detail'" style="width: 15%" :disabled="saveType === 'detail'"/>
                </span>
                <span style="margin-left: 10px;margin-right: 10px">
                  ~
                </span>
                <span>
                  <Input v-model="levelConfig.thresholdEndValue" disabled="saveType === 'detail'" style="width: 15%" :disabled="saveType === 'detail'"/>
                </span>
              </Col>
            </Row>
          </div>
          <p class="sys-sub-title" style="margin-top: 5px">权重配置</p>
          <div class="borrow-detail">
            <div v-for="indicator in riskCardModel.indicatorList" style="display: flex;margin-top: 15px;">
              <div style="text-align: right;padding-top: 5px;width: 10%">{{ indicator.name }} :</div>
              <div style="width: 80%;margin-left: 20px">
                <Slider style="" v-model="indicator.weight * 100" :disabled="saveType === 'detail'" show-input :marks="marks"/>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" style="text-align: center">
        <Button @click="closeEvent">取消</Button>
        <Button type="primary" v-if="saveType === 'update'" @click="handleUpdate()" :loading="loading">保存
        </Button>
      </div>
    </Modal>

  </div>
</template>

<script>

import {mapActions} from 'vuex'
import RiskCard from './RiskCard.vue'
import noData from "@/components/bsp-empty/index.vue";

export default {
  components: {
    RiskCard,
    noData
  },
  data() {
    return {
      riskCards: [],
      showData: false,
      showForm: false,
      formTitle: '',
      riskId: '',
      saveType: '',
      loading: false,
      riskCardModel: {},
      marks: {
        0: '0%',
        20: '20%',
        40: '40%',
        60: '60%',
        80: '80%',
        100: '100%'
      }
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),
    closeEvent() {
      this.showForm = false
    },
    handleDetail(riskId) {
      this.getRisKModel(riskId)
      this.showForm = true
      this.formTitle = '模型详情'
      this.saveType = 'detail'
    },
    handleEdit(riskId) {
      this.getRisKModel(riskId)
      this.showForm = true
      this.formTitle = '编辑模型'
      this.saveType = 'update'
    },
    handleUpdate() {
      if (this.riskCardModel.levelConfigs.length > 0) {
        this.riskCardModel.levelConfigs.forEach(item => {
          if (!item.thresholdEndValue) {
            this.$Message.error(`${item.name} 不能为空！`)
            return
          }
        })
      }
      this.authPostRequest({
        url: this.$path.fjcy_integral_riskModel_update,
        params: this.riskCardModel
      }).then(res => {
        if (res.success) {
          this.closeEvent()
          this.$Message.success('保存成功')
          this.getRiskModelList()
        }
      })
    },
    getRisKModel(riskId) {
      this.authGetRequest({
        url: this.$path.fjcy_integral_riskModel_get_one,
        params: {id: riskId}
      }).then(resp => {
        if (resp.success && resp.data) {
          this.riskCardModel = resp.data
        }
      })
    },
    getRiskModelList() {
      this.authGetRequest({
        url: this.$path.fjcy_integral_riskModel_get,
        params: {orgCode: this.$store.state.common.orgCode}
      }).then(resp => {
        if (resp.success && resp.data) {
          this.riskCards = resp.data
        }
      })
    }
  },
  mounted() {
    this.getRiskModelList()
  },
}
</script>

<style scoped>

.slider-demo-block {
  max-width: 600px;
  display: flex;
  align-items: center;
}

.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}

.slider-demo-block .demonstration {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  line-height: 44px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0;
}

.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  overflow-x: auto;
  padding: 24px;
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
  margin-left: auto;
  margin-right: auto;
  height: 100%;
}

.borrow-detail {
  padding: 0 10px 0 10px;
}

.content-title {
  line-height: 40px;
  display: inline-block;
  text-align: left; /* 水平居中 */
  vertical-align: middle; /* 垂直居中 */
  flex-shrink: 0;
  width: 120px;
}


</style>
