let menuMode=serverConfig.menuMode
import main from  '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'


export default [
   {
        meta: {
          title: '提讯',
        },
        path: '/Inquiry',
        name: 'Inquiry',
        redirect:'/Inquiry/casePersonneManagement',
        component:menuMode=='side'?mainNew:main,
        children: [
          {
               meta: {
                  title: '提讯',
                },
                path: 'list',
                name: 'list',
                sider: true,
                bread: true,
                component: () => import('@/view/windowBusiness/Inquiry/Inquirylist/index.vue')
         },
         {
               meta: {
                  title: '办案人员管理',
                },
                path: 'casePersonneManagement',
                name: 'casePersonneManagement',
                sider: true,
                bread: true,
                component: () => import('@/view/windowBusiness/Inquiry/casePersonneManagement/index.vue')
       },
         ]
   },
   {
    meta: {
          title: '律师会见',
        },
        path: '/lawyer',
        name: 'lawyer',
        redirect:'/lawyer/lawyerManagement',
        component: menuMode=='side'?mainNew:main,
        children:[
              {
                meta: {
                  title: '律师会见',
                },
                path: 'lawyerMeeting',
                name: 'lawyerMeeting',
                sider: true,
                bread: true,
                component: () =>import('@/view/windowBusiness/lawyerMeeting/consulMeeting/index.vue')
              },
              {
                meta: {
                  title: '律师管理',
                },
                path: 'lawyerManagement',
                name: 'lawyerManagement',
                sider: true,
                bread: true,
                component: () =>import('@/view/windowBusiness/lawyerMeeting/manage/index.vue')
              },
              {
                meta: {
                  title: '违规登记',
                },
                path: 'violationRecord',
                name: 'violationRecord',
                sider: true,
                bread: true,
                component: () =>import('@/view/windowBusiness/lawyerMeeting/violationRecord/index.vue')
              },
        ]
   },
   {
        meta: {
          title: '使馆领事',
        },
        path: '/embassy',
        name: 'embassy',
        redirect:'/embassy/consulMeeting',
        component:menuMode=='side'?mainNew:main,
        children:[
                {
          meta: {
            title: '使馆领事会见',
          },
          path: 'consulMeeting',
          name: 'consulMeeting',
          sider: true,
          bread: true,
          component: () =>import('@/view/windowBusiness/consulMeeting/consulMeeting/index.vue')
        },
        {
          meta: {
            title: '领事外事管理',
          },
          path: 'affairsManagement',
          name: 'affairsManagement',
          sider: true,
          bread: true,
          component: () =>import('@/view/windowBusiness/consulMeeting/affairsManagement/index.vue')
        },
        ]
   },
   {
        meta: {
          title: '家属会见',
        },
        path: '/familyMeeting',
        name: 'familyMeeting',
        sider: true,
        bread: true,
        component:menuMode=='side'?mainNew:main,
        children:[
                {
          meta: {
            title: '当面会见',
          },
          path: 'faceToFace',
          name: 'faceToFace',
          sider: true,
          bread: true,
          component: () =>import('@/view/windowBusiness/familyMeeting/faceToFace/index.vue')
        },
         {
          meta: {
            title: '单向视频会见',
          },
          path: 'videoConference',
          name: 'videoConference',
          sider: true,
          bread: true,
          component: () =>import('@/view/windowBusiness/familyMeeting/videoConference/index.vue')
        },
        {
          meta: {
            title: '人员社会关系',
          },
          path: 'administration',
          name: 'administration',
          sider: true,
          bread: true,
          component: () =>import('@/view/windowBusiness/familyMeeting/administration/index.vue')
        },
      ]
      },
      {
        meta: {
          title: '家属会见(拘留所)',
        },
        path: '/familyMeetingJls',
        name: 'familyMeetingJls',
        sider: true,
        bread: true,
        redirect:'/familyMeetingJls/faceToFace',
        component: menuMode=='side' ? mainNew : main,
        children:[
                {
          meta: {
            title: '当面会见',
          },
          path: 'faceToFace',
          name: 'faceToFace',
          sider: true,
          bread: true,
          component: () =>import('@/view/windowBusiness/familyMeetingJls/faceToFace/index.vue')
        },
        {
          meta: {
            title: '社会关系管理',
          },
          path: 'administration',
          name: 'administration',
          sider: true,
          bread: true,
          component: () =>import('@/view/windowBusiness/familyMeetingJls/administration/index.vue')
        },
      ]
      },

  ]