<template>
  <div class="arrange-board duty-arrange-board">
    <!-- {{ arrangeList }} -->
    <noData height="100%" style="margin-top: 20%;" v-if="!arrangeList.length > 0" />
    <template v-for="(item, idx) in arrangeList" v-else>
      <div class="arrange-box shift-box" v-if="choiceIdx === idx" :key="idx">
        <div class="header-box shift-header"><span class="class">班次</span><span class="date">日期</span></div>
        <div :class="['content-box','shift-content', getShiftType(shiftItem)]" v-for="(shiftItem, shiftIdx) in item.shiftList" :key="idx + shiftIdx">
          <p class="shift-name">{{shiftItem.shiftName}}</p>
          <template v-if="shiftItem.startTime !== shiftItem.endTime">
            <p class="shift-time start">{{shiftItem.startTime}}</p>
            <i class="triangle"></i>
            <p class="shift-time end">{{shiftItem.endTime}}</p>
          </template>
        </div>
      </div>
      <div class="arrange-box duty-box" v-for="(duty,dutyIdx) in item.dutyList" :key="duty.dutyDate">
        <div :class="['header-box','duty-header', getHeaderType(duty.dutyDate)]">
          <p class="time">{{ new Date(duty.dutyDate).Format("yyyy-MM-dd") }}</p>
          <p class="day">{{new Date(duty.dutyDate).toLocaleDateString([],{weekday:'long'})}}</p>
        </div>
        <div
          v-show="choiceIdx === idx"
          v-for="(info, infoIdx) in duty.dutyInfo"
          :key="duty.dutyDate + info.shiftId"
          :class="getContentType(info, `${idx}-${dutyIdx}-${infoIdx}`)"
          @mouseup="onMouseUp"
          @dragover.prevent="onMouseEnter(info, idx, dutyIdx, infoIdx)"
          @dragleave="onMouseLeave"
          @mouseenter="onMouseEnter(info, idx, dutyIdx, infoIdx)"
          @mouseleave="onMouseLeave"
          @drop="onDrop"
        >
          <div
            :draggable="info.shiftType === shiftStatus.NEXT"
            @mousedown="onMouseDown(idx, dutyIdx, infoIdx, -1)"
            class="arrange-person-group duty-group"
            v-if="info.isGroup"
          >
            <span class="group-name">{{ info.name }}</span>
            <person-arrange
              @on-close="personClose(idx, dutyIdx, infoIdx, -1)"
              class="arrange-person group-person"
              v-for="person in info.prisonerList"
              :key="duty.dutyDate + info.shiftId + person.id"
              :info="person"
              :hover="dragType === 'none'"
              :prev= "info.shiftType !== shiftStatus.NEXT"
            ></person-arrange>
          </div>
          <template  v-if="!info.isGroup">
            <person-arrange
              :draggable="info.shiftType === shiftStatus.NEXT"
              @mousedown.native="onMouseDown(idx, dutyIdx, infoIdx, personIdx)"
              @on-close="personClose(idx, dutyIdx, infoIdx, personIdx)"
              @on-over="personOver(personIdx)"
              @on-leave="personOver(-1)"
              class="arrange-person"
              v-for="(person, personIdx) in info.prisonerList"
              :key="duty.dutyDate + info.shiftId + person.id"
              :info="person"
              :hover="dragType === 'none'"
              :prev= "info.shiftType !== shiftStatus.NEXT"
            ></person-arrange>
          </template>
        </div>
        <div class="choice-box"   :style="{width: 'calc(100% * '+ item.dutyList.length +')', height: curHeight}"  v-if="dutyIdx === 0" v-show="choiceIdx !== idx">
          <p class="choice" @click="choiceIdx = idx">班次变更，点击切换班次</p>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
// import {roomDutyDetail, roomAutoDuty} from "@/axios/zhjgPrisonWork";
import personArrange from "./personArrange";
import { cloneDeep, getWeekRange } from "@/util";
import noData from "@/components/bsp-empty/index.vue"
export default {
  name: "arrangeBoard",
  components: {personArrange,noData},
  data() {
    return {
      choiceIdx: 0,
      arrangeList: [],
      dutyParam: {},
      dragType: "none",
      dropPerson: -1,
      dragPerson: -1,
      dropIdx: "",
      dragIdx: "",
      dropData: {},
      timer: null,
      dutyTimes: {},
      settleList: {},
      shiftStatus: {
        PREV: '0',
        CUR: '1',
        NEXT: '2',
      },
      orgCode: this.$store.state.common.orgCode
    };
  },
  props:{
    roomData: {
      type: Object,
      default: {}
    },
  },
  computed: {
    curHeight() {
      if (!this.arrangeList[this.choiceIdx] || 
          !this.arrangeList[this.choiceIdx].shiftList) {
        return '136px'; // 设置一个默认高度
      }
      return `${this.arrangeList[this.choiceIdx].shiftList.length *  136}px`;
    },
  },
  watch: {
    dutyParam: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.roomId && newVal.dutyWeek) {
          this.dutyParam.roomId = newVal.roomId
          this.getArrangeList();
          
      console.log('this.arrangeList222222', this.arrangeList)
        }
      }
    },
    roomData: {
      handler(value,oldValue) {
        // if(!value.roomCode == oldValue.roomCode){
        //   this.dutyParam.roomId = value.roomCode
        //   this.getArrangeList()
        // }
        if(value && value.roomCode) {
          this.dutyParam.roomId = value.roomCode
          this.getArrangeList()
        }
      },
      deep: true,
      // immediate: true
    }
  },
  methods: {
    getContentType(info, curIdx) {
      return [
        "content-box",
        "duty-content",
        {
          "duty-hover": info.shiftType === this.shiftStatus.NEXT && this.dragType !== "none" && curIdx === this.dropIdx && curIdx !== this.dragIdx,
          "duty-next": info.shiftType === this.shiftStatus.NEXT,
          "duty-current": info.shiftType === this.shiftStatus.CUR,
        }
      ];
    },
    getHeaderType(time) {
      if (new Date(this.dutyParam.dutyDate).Format("yyyy-MM-dd") === time) return "choice-duty";
      if (new Date().Format("yyyy-MM-dd") === time) return "current-duty";
    },
    getShiftType(item) {
      const { startTime: time, noFixedTimeShift } = item;
      if (noFixedTimeShift === 1) return "all-day";
      if ((time >= "00:00" && time < "04:00") || time >= "18:00") return "night-box";
      if (time >= "04:00" && time < "12:00") return "morning-box";
      if (time >= "12:00" && time < "18:00") return "afternoon-box";
    },
    calcDutyTime(addArr, delArr, isCalc) {
      console.log(addArr, delArr, isCalc,'addArr, delArr, isCalc');
      isCalc = isCalc === undefined ? this.dutyParam.currentWeek : isCalc;
      if (!isCalc) return;
      addArr.forEach(item => {
        this.dutyTimes[item.id] = (this.dutyTimes[item.id] || 0) + 1;
      });
      delArr.forEach(item => {
        this.dutyTimes[item.id] = (this.dutyTimes[item.id] || 0) - 1;
      });
    },
    emitDutyTime() {
      this.$emit("change-times", this.dutyTimes);
    },
    emptyCheck() {
      let isEmpty = false;
      this.arrangeList && this.arrangeList.forEach(item => {
        item.dutyList.forEach(duty => {
          duty.dutyInfo.forEach(info => {
            if (!isEmpty && info.shiftType === this.shiftStatus.NEXT) {
              isEmpty = info.prisonerList.length !== 2;
            }
          });
        });
      });
      return isEmpty;
    },
    getSettleArray() {
      let settleList = [];
      console.log(this.settleList,'this.settleList');
      for (let key in  this.settleList) {
        settleList.push({dutyDate: key, dutyInfo: this.settleList[key]});
      }
      return settleList;
    },
    getAutoList() {
      let autoList = {};
      let { roomId, dutyWeek } = this.dutyParam;
      let param = {
        roomId,
        dutyList: this.getSettleArray(),
        dutyStartDate: dutyWeek[0],
        dutyEndDate: dutyWeek[1],
      };
      return roomAutoDuty(param).then(res => {
        let data = res.data;
        data.forEach(item => {
          item.dutyList.forEach(duty => {
            autoList[duty.dutyDate] = duty.dutyInfo;
          });
        });
        return autoList;
      }).catch(err => this.$messageInfo(err));
    },
    async autoArrange() {
      let autoList = await this.getAutoList();
      let dutyTimes = {};
      this.arrangeList && this.arrangeList.forEach((item, idx) => {
        item.dutyList.forEach((duty, dutyIdx) => {
          let dutyInfo = autoList[duty.dutyDate];
          if (dutyInfo) {
            duty.dutyInfo = dutyInfo;
            this.setSettleList([idx, dutyIdx]);
          }
          duty.dutyInfo.forEach(info => {
            info.prisonerList.forEach(person => dutyTimes[person.id] = (dutyTimes[person.id] || 0) + 1);
          });
        });
      });
      if (this.dutyParam.currentWeek) {
        this.dutyTimes = dutyTimes;
        this.emitDutyTime();
      }
    },
    getArrangeList() {
      // return;
      let param = {
        roomId: this.dutyParam.roomId,
        startDate: this.dutyParam.dutyWeek[0],
        endDate: this.dutyParam.dutyWeek[1],
        orgCode: this.orgCode
      };
      this.$store.dispatch('authGetRequest',{
        url: this.$path.acp_duty_dutyRecords,
        params: param
      }).then(res => {
        if(res.success) {
          // return res;
          this.arrangeList = res.data
          console.log(this.arrangeList,'this.arrangeList');
          return this.arrangeList
        } else {
          this.$Message.error('获取排版记录接口失败!!')
        }
      })
    },
    async changeRoom(param, isAuto) {
      this.dutyParam = param;
      this.settleList = {};
      this.arrangeList = await this.getArrangeList();
      this.dealChangeRoom();
      this.choiceIdx =  this.arrangeList.length - 1;
      isAuto && this.autoArrange();
    },
    async changeWeek(param) {
      let [firstDay, lastDay] = this.dutyParam.dutyWeek;
      if (firstDay === param.dutyWeek[0] && lastDay === param.dutyWeek[1]) {
        return this.$set(this.dutyParam, "dutyDate", param.dutyDate);
      } else {
        this.dutyParam = param;
        let list = await this.getArrangeList();
        this.arrangeList = this.dealChangeWeek(list);
        this.choiceIdx =  this.arrangeList && this.arrangeList.length - 1;
      }
      console.log('this.arrangeList111', this.arrangeList)
    },
		autoShift(param) {
			this.$store.dispatch('authGetRequest',{
				url: this.$path.acp_duty_autoShift,
				params: param
			}).then(res => {
				console.log(res)
				if(res.success) {
					this.arrangeList[0] = res.data
					console.log('arrangeList', this.arrangeList)
				} else{
					this.$Message.error(res.msg || '接口操作失败!!')
				}
			})
		},
    dealChangeRoom() {
      this.dutyTimes = {};
      if (!this.dutyParam.currentWeek) return;
      if(this.arrangeList) {
        this.arrangeList.forEach(item => {
          item.dutyList.forEach(duty => {
            duty.dutyInfo.forEach(info => {
              info.prisonerList.forEach(person => this.dutyTimes[person.id]  = (this.dutyTimes[person.id] || 0) + 1);
            });
          });
        });
        this.$emit("change-times", this.dutyTimes);
      }
      
    },
    dealChangeWeek(list) {
      let dutyTimes = {};
      list && list.forEach(item => {
        item.dutyList.forEach(duty => {
          duty.dutyInfo.forEach((info, infoIdx) => {
            if (this.settleList[duty.dutyDate]) {
              if (info.shiftType === this.shiftStatus.NEXT) {
                duty.dutyInfo[infoIdx] = this.settleList[duty.dutyDate][infoIdx];
              } else {
                this.settleList[duty.dutyDate][infoIdx] =  duty.dutyInfo[infoIdx];
              }
            }
            info.prisonerList.forEach(person => dutyTimes[person.id]  = (dutyTimes[person.id] || 0) + 1);
          });
        });
      });
      if (this.dutyParam.currentWeek) {
        this.dutyTimes = dutyTimes;
        this.emitDutyTime();
      }
      return list;
    },
    outsideRule(oldList, newList, personIdx) {
      console.log(oldList, newList, personIdx,'oldList, newList, personIdx');
      let copyOldList = cloneDeep(oldList);
      let copyNewList = cloneDeep(newList);
      let length = copyOldList.prisonerList.length;
      if (copyNewList.isGroup || copyOldList.isGroup || length === 0) {
        console.log(copyNewList,'copyNewListcopyNewList');
        this.calcDutyTime(copyNewList.prisonerList, copyOldList.prisonerList);
        return [this.assignList(copyOldList, copyNewList), true];
      } else {
        let [info] = copyNewList.prisonerList;
        let idx = copyOldList.prisonerList.findIndex(item => item.id === info.id);
        if (idx !== -1) return [oldList, false];
        if (length  === 1) {
          copyOldList.prisonerList.push(info);
          this.calcDutyTime([info], []);
        } else  {
          let delArr = copyOldList.prisonerList.splice(personIdx, 1, info);
          this.calcDutyTime([info], delArr);
        }
        return [copyOldList, true];
      }
    },
    assignList(oldList, newList) {
      let copyOldList = cloneDeep(oldList);
      let copyNewList = cloneDeep(newList);
      copyOldList["id"] = copyNewList["id"] || "";
      copyOldList["groupId"] = copyNewList["groupId"] || "";
      copyOldList["name"] = copyNewList["name"] || "";
      copyOldList["isGroup"] = copyNewList["isGroup"];
      copyOldList["prisonerList"] = copyNewList["prisonerList"] || [];
      return copyOldList;
    },
    onMouseDown(idx, dutyIdx, infoIdx, personIdx) {
      let info = this.arrangeList[idx].dutyList[dutyIdx].dutyInfo[infoIdx];
      if (info.shiftType !== this.shiftStatus.NEXT) return;
      this.dragType = "inside";
      this.dragIdx = `${idx}-${dutyIdx}-${infoIdx}`;
      this.dragPerson = personIdx;
    },
    onMouseUp() {
      if (this.dragType === "outside" && this.dropIdx) {
        let [idx, dutyIdx, infoIdx] = this.dropIdx.split("-");
        let personIdx = this.dropPerson !== -1 ? this.dropPerson  : 0;
        let dutyInfo = this.arrangeList[idx].dutyList[dutyIdx].dutyInfo;
        console.log(dutyInfo[infoIdx],'mouseUp',this.dropData);
        let [info, modify] = this.outsideRule(dutyInfo[infoIdx], this.dropData, personIdx);
        console.log([info, modify],'[info, modify]');
        if (modify) {
          this.$set(dutyInfo, infoIdx, info);
          this.setSettleList([idx, dutyIdx]);
        }
        this.emitDutyTime();
      }
      this.clearStatus();
    },
    onDrop(e) {
      e.preventDefault();
      if (this.dragType === "inside" && this.dropIdx) {
        let [dragIdx, dragDutyIdx, dragInfoIdx] = this.dragIdx.split("-");
        let dragDutyInfo = this.arrangeList[dragIdx].dutyList[dragDutyIdx].dutyInfo;
        let dragInfo = dragDutyInfo[dragInfoIdx];
        let [dropIdx, dropDutyIdx, dropInfoIdx] = this.dropIdx.split("-");
        let dropDutyInfo =  this.arrangeList[dropIdx].dutyList[dropDutyIdx].dutyInfo;
        let dropInfo = dropDutyInfo[dropInfoIdx];
        if (dragInfo.isGroup || dropInfo.isGroup) {
          dragDutyInfo[dragInfoIdx] =  this.assignList(dragInfo, dropInfo);
          dropDutyInfo[dropInfoIdx] =  this.assignList(dropInfo, dragInfo);
        } else  {
          let dragPrisoner = dragInfo.prisonerList[this.dragPerson];
          let idx = dropInfo.prisonerList.findIndex(item => item.id === dragPrisoner.id);
          if (idx !== -1) return;
          if (dropInfo.prisonerList.length !== 2) {
            dragInfo.prisonerList.splice(this.dragPerson, 1);
            dropInfo.prisonerList.push(dragPrisoner);
          } else {
            let personIdx =  this.dropPerson !== -1 ? this.dropPerson  : 0;
            let dropPrisoner = dropInfo.prisonerList[personIdx];
            let otherDragPrisoner = dragInfo.prisonerList[this.dragPerson === 0 ? 1 : 0];
            if (otherDragPrisoner.id === dropPrisoner.id)  {
              if (this.dropPerson !== -1) return;
              personIdx = personIdx === 0 ? 1 : 0;
            }
            let prisoner = dropInfo.prisonerList.splice(personIdx, 1, dragPrisoner)[0];
            dragInfo.prisonerList.splice(this.dragPerson, 1, prisoner);
          }
        }
        this.setSettleList([dragIdx, dragDutyIdx], [dropIdx, dropDutyIdx]);
      }
      this.clearStatus();
    },
    onMouseEnter(info, idx, dutyIdx, infoIdx) {
      this.timer && clearTimeout(this.timer);
      this.dropIdx = info.shiftType === this.shiftStatus.NEXT ? `${idx}-${dutyIdx}-${infoIdx}` : "";
    },
    onMouseLeave() {
      this.timer = setTimeout(() => {
        this.dropIdx = "";
      }, 200);
    },
    onOutsideDrag(dropData) {
      console.log(dropData);
      this.dragType = "outside";
      let {isGroup, ...info} = dropData;
      console.log(info,'...info');
      if (dropData.isGroup) {
        this.dropData = dropData;
        console.log(this.dropData);
      } else {
        this.dropData = {isGroup, prisonerList: [info]};
      }
    },
    personClose(idx, dutyIdx, infoIdx, personIdx) {
      let dutyInfo = this.arrangeList[idx].dutyList[dutyIdx].dutyInfo;
      if (personIdx !== -1) {
        let delArr = dutyInfo[infoIdx].prisonerList.splice(personIdx, 1);
        this.calcDutyTime([], delArr);
      } else {
        this.calcDutyTime([], dutyInfo[infoIdx].prisonerList);
        dutyInfo[infoIdx] = this.assignList(dutyInfo[infoIdx], {});
      }
      this.setSettleList([idx, dutyIdx]);
      this.emitDutyTime();
    },
    personOver(personIdx) {
      this.dropPerson = personIdx;
    },
    isCurrentWeek(date) {
      let [firstDay, lastDay] = getWeekRange(new Date());
      return firstDay <= date  &&  date <= lastDay;
    },
    setSettleList(list1, list2) {
      let arr = list2 ? [list1, list2] : [list1];
      arr.forEach(([idx, dutyIdx]) => {
        let duty = this.arrangeList[idx].dutyList[dutyIdx];
        this.settleList[duty.dutyDate] = duty.dutyInfo;
      });
    },
    checkSettleList(obj) {
      for (let key in this.settleList) {
        let dutyInfo = this.settleList[key];
        dutyInfo.forEach(info => this.checkRule(obj, info, this.isCurrentWeek(key)));
      }
      this.arrangeList && this.arrangeList.forEach((item, idx) => {
        item.dutyList.forEach((duty, dutyIdx)  => {
          if (this.settleList[duty.dutyDate]) return;
          let flag = false;
          duty.dutyInfo.forEach(info => {
            let isChange  = this.checkRule(obj, info);
            flag = flag || isChange;
          });
          if (flag) this.setSettleList([idx, dutyIdx]);
        });
      });
      this.emitDutyTime();
    },
    checkRule(obj, info, isCalc) {
      let isChange = false;
      let {type, personId, groupId} = obj;
      if (info.shiftType !== this.shiftStatus.NEXT) return;
      if (type === "del" && info.isGroup && info.id === groupId) {
        this.calcDutyTime([], info.prisonerList, isCalc);
        info.id = "";
        info.name = "";
        info.isGroup = false;
        info.prisonerList = [];
        isChange = true;
      }
      if (type === "add" && !info.isGroup && info.prisonerList.length) {
        for (let i = info.prisonerList.length - 1; i >= 0; i--) {
          let id = info.prisonerList[i].id;
          if (personId[id]) {
            let delArr = info.prisonerList.splice(i, 1);
            this.calcDutyTime([], delArr, isCalc);
            isChange = true;
          }
        }
      }
      return isChange;
    },
    clearAll() {
      this.arrangeList && this.arrangeList.forEach((item, idx) => {
        item.dutyList.forEach((duty, dutyIdx) => {
          let flag = false;
          duty.dutyInfo.forEach(info => {
            if (info.shiftType !== this.shiftStatus.NEXT) return;
            this.calcDutyTime([], info.prisonerList);
            flag = flag || !!info.prisonerList.length;
            info.id = "";
            info.name = "";
            info.isGroup = false;
            info.prisonerList = [];
          });
          flag && this.setSettleList([idx, dutyIdx]);
        });
      });
      this.emitDutyTime();
    },
    clearStatus() {
      this.dragType = "none";
      this.dragIdx = "";
      this.dragPerson = "";
      this.dropIdx = "";
      this.dropPerson = "";
    },
  },
  mounted() {
    document.addEventListener("mouseup", this.clearStatus);
    
  },
  beforeDestroy() {
    document.removeEventListener("mouseup", this.clearStatus);
  }
};
</script>
<style scoped lang="less">
  @import "../../../assets/style/roomDuty.less";
  .arrange-board{
    .arrange-box{
      width: calc(~'100% / 8');
    }
    .header-box {
      height: 81px;
    }
    .content-box {
      height: 136px;
    }
  }
  .duty-box{
    .duty-content {
      padding: 0 16px;
      flex-direction: column;
      &.duty-next{
        background: #F5F7FA;
      }
      &.duty-current {
        position: relative;
        &:after, &:before {
          content: "";
          display: inline-block;
          width: 100%;
          left: 0;
          top: 0;
          position: absolute;
        }
        &:after {
          width: 100%;
          height: 3px;
          background:  #2390FF;
          z-index: 1;
        }
        &:before {
          z-index: 2;
          width: 20px;
          height: 20px;
          background: url("../../../assets/images/prisonRoomDuty/icon_clock.png");
        }
      }
      &.duty-hover {
        border: 1px solid #2390FF;
      }
    }
    .arrange-person +.arrange-person {
      margin-top: 8px;
    }
    .choice-box{
      position: absolute;
      top: 81px;
      left: -1px;
      padding: 16px;
      border-left: 1px solid #E4EAF0;
      border-bottom: 1px solid #E4EAF0;
      .choice {
        text-align: center;
        cursor: pointer;
        min-height: 45px;
        white-space: pre-wrap;
        color: #2390FF;
        background: #E9F4FF;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 20px;
      }
    }
  }
  .duty-arrange-board{
    display: flex;
    justify-content: flex-start !important;
  }
</style>